# Настройка OAuth для HIVE Agency

## ⚠️ ВАЖНО: Исправление ошибки "Invalid URI"

Если вы получаете ошибку:
```
Error 400
Invalid `success` param: Invalid URI. Register your new client (hive-agency-git-main-sacralpros-projects.vercel.app) as a new Web platform on your project console dashboard
```

**Решение:**
1. Откройте [Appwrite Console](https://cloud.appwrite.io)
2. Выберите ваш проект HIVE Agency
3. Перейдите в **Auth → Settings**
4. Найдите **Google OAuth** в списке провайдеров
5. В поле **Redirect URLs** добавьте ВСЕ необходимые URL:
   ```
   http://localhost:3000/auth/callback
   https://hive-agency-git-main-sacralpros-projects.vercel.app/auth/callback
   https://yourdomain.com/auth/callback
   ```
6. Нажмите **Update**

## 🔧 Поддерживаемые методы аутентификации

HIVE Agency поддерживает 3 основных способа входа:
- **Email/Пароль** - стандартная регистрация
- **Google OAuth** - быстрый вход через Google аккаунт
- **Telegram** - вход через Telegram (в разработке)

## 🔧 Настройка Appwrite OAuth

### 1. Настройка Google OAuth

1. Перейдите в [Google Cloud Console](https://console.cloud.google.com/)
2. Создайте новый проект или выберите существующий
3. Включите Google+ API
4. Создайте OAuth 2.0 Client ID:
   - Тип приложения: Web application
   - Authorized redirect URIs: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/[PROJECT_ID]`
   - Замените `[PROJECT_ID]` на ваш Project ID из Appwrite

5. В Appwrite Console:
   - Перейдите в Auth → Settings
   - Найдите Google в списке провайдеров
   - Включите Google OAuth
   - Введите Client ID и Client Secret из Google Cloud Console

### 2. Настройка Telegram Login Widget

1. Создайте бота через [@BotFather](https://t.me/botfather)
2. Получите Bot Token
3. Настройте домен для Telegram Login Widget:
   ```
   /setdomain
   localhost:3002 (для разработки)
   yourdomain.com (для продакшена)
   ```

4. Добавьте Telegram Login Widget на страницы:
   ```html
   <script async src="https://telegram.org/js/telegram-widget.js?22"
           data-telegram-login="your_bot_username"
           data-size="large"
           data-auth-url="http://localhost:3002/auth/telegram"
           data-request-access="write">
   </script>
   ```

5. Обработайте callback в `/auth/telegram` endpoint

## 🌐 Настройка переменных окружения

Создайте файл `.env.local` на основе `.env.local.example`:

```bash
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here

# Database Configuration
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id_here

# Collection IDs (получите из Appwrite Console)
NEXT_PUBLIC_APPWRITE_COLLECTION_USERS=your_users_collection_id
# ... остальные коллекции
```

## 🔄 Redirect URLs

### Для локальной разработки (localhost:3000):
- Success URL: `http://localhost:3000/auth/callback`
- Failure URL: `http://localhost:3000/login?error=oauth_failed`

### Для Vercel деплоя:
- Success URL: `https://hive-agency-git-main-sacralpros-projects.vercel.app/auth/callback`
- Failure URL: `https://hive-agency-git-main-sacralpros-projects.vercel.app/login?error=oauth_failed`

### Для продакшена (ваш домен):
- Success URL: `https://yourdomain.com/auth/callback`
- Failure URL: `https://yourdomain.com/login?error=oauth_failed`

## 🚀 Тестирование

1. Запустите проект: `npm run dev`
2. Перейдите на `/login`
3. Нажмите на кнопку "Продолжить с Google"
4. Должен открыться popup с Google OAuth
5. После успешной авторизации вы будете перенаправлены в дашборд

## 🛠️ Troubleshooting

### ❌ Ошибка 412: "This provider is disabled" (САМАЯ ЧАСТАЯ ПРОБЛЕМА)

**Симптомы**: При нажатии на "Продолжить с Google" появляется ошибка:
```
Error 412
This provider is disabled. Please enable the provider from your Appwrite console to continue.
project_provider_disabled
```

**Решение**:
1. 🔧 **Откройте Appwrite Console**: https://cloud.appwrite.io
2. 📂 **Выберите ваш проект** HIVE Agency
3. 🔐 **Перейдите в Authentication → Settings**
4. 📋 **Найдите раздел "OAuth2 Providers"**
5. 🔍 **Найдите "Google" в списке провайдеров**
6. ⚡ **ВКЛЮЧИТЕ переключатель** рядом с Google (должен стать зеленым)
7. 📝 **Введите ваши Google OAuth данные**:
   - App ID: Ваш Google Client ID
   - App Secret: Ваш Google Client Secret
8. 💾 **Нажмите "Update"**

**Важно**: Провайдер должен быть **ВКЛЮЧЕН** (Enabled), а не просто настроен!

### ❌ Ошибка "Invalid redirect URI"
- Проверьте, что redirect URI в настройках провайдера точно совпадает с URL из Appwrite
- Формат: `https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/[PROJECT_ID]`
- Замените `[PROJECT_ID]` на ваш реальный Project ID

### ❌ Ошибка "Invalid client"
- Проверьте правильность Client ID и Client Secret в Appwrite Console
- Убедитесь, что Google+ API включен в Google Cloud Console
- Проверьте настройки OAuth consent screen

### ❌ Ошибка "Access blocked"
- Настройте OAuth consent screen в Google Console
- Добавьте тестовых пользователей, если приложение в режиме тестирования
- Для продакшена подайте заявку на верификацию

## 📋 Пошаговая проверка настройки

### 1. Проверка Appwrite Console
- [ ] Проект создан и выбран
- [ ] Authentication → Settings открыт
- [ ] Google OAuth **ВКЛЮЧЕН** (переключатель зеленый)
- [ ] Client ID и Secret введены корректно

### 2. Проверка Google Cloud Console
- [ ] Проект создан
- [ ] Google+ API включен
- [ ] OAuth 2.0 Client ID создан
- [ ] Redirect URI добавлен правильно
- [ ] OAuth consent screen настроен

### 3. Проверка кода
- [ ] Сервер разработки запущен (`npm run dev`)
- [ ] Страница `/login` открывается
- [ ] Кнопка "Продолжить с Google" видна
- [ ] При клике происходит редирект на Google

## 🔗 Полезные ссылки

- [Appwrite Console](https://cloud.appwrite.io) - основная консоль управления
- [Google Cloud Console](https://console.cloud.google.com/) - настройка OAuth
- [Appwrite OAuth Documentation](https://appwrite.io/docs/products/auth/oauth2) - официальная документация
- Убедитесь, что используете правильный Project ID

### Ошибка "OAuth provider not configured"
- Убедитесь, что провайдер включен в Appwrite Console
- Проверьте правильность Client ID и Client Secret

### Пользователь не создается
- Проверьте настройки Auth в Appwrite Console
- Убедитесь, что включена регистрация новых пользователей
