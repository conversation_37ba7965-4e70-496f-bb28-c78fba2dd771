# 🤖 AI-Помощник HIVE Agency

## 📋 Обзор

AI-Помощник HIVE Agency - это интеллектуальная система для подбора оптимальных стратегий продвижения музыки. Система анализирует запросы пользователей и предлагает персонализированные кампании с расчетом стоимости и времени.

## ✨ Основные возможности

### 🎯 Умный анализ запросов
- **Категории**: Продвижение артистов и треков
- **Готовые промпты**: Популярные запросы для быстрого старта
- **Персонализация**: Анализ ключевых слов для подбора сервисов

### 🛠️ Рекомендации сервисов
- **Instagram**: Органический рост, контент, Stories
- **TikTok**: Вирусные видео, тренды, челленджи
- **YouTube**: Развитие канала, SEO, монетизация
- **Spotify**: Плейлисты, стримы, кураторы
- **Радио**: Ротация, интервью, региональное покрытие

### 💰 Автоматический расчет
- **Стоимость**: Суммарная цена всех сервисов
- **Время**: Максимальная длительность кампании
- **Результаты**: Ожидаемый охват и метрики

## 🗄️ Структура базы данных

### Коллекции Appwrite

#### `ai_prompts` - Готовые промпты
```javascript
{
  prompt_id: string,        // Уникальный ID промпта
  category: 'artist|track', // Категория
  title: string,           // Заголовок
  description: string,     // Описание
  prompt_text: string,     // Текст промпта
  tags: string[],         // Теги для поиска
  popularity: number,     // Популярность (0-100)
  active: boolean,        // Активность
  created_at: datetime,   // Дата создания
  updated_at: datetime    // Дата обновления
}
```

#### `service_templates` - Шаблоны сервисов
```javascript
{
  template_id: string,           // Уникальный ID шаблона
  category: 'artist|track',      // Категория
  service_type: string,          // Тип сервиса
  platform: string,             // Платформа
  name: string,                  // Название
  description: string,           // Описание
  icon: string,                  // Иконка
  color: string,                 // Цвет
  base_price: number,           // Базовая цена
  duration_days: number,        // Длительность в днях
  features: string[],           // Список возможностей
  metrics: object,              // Ожидаемые метрики
  requirements: string[],       // Требования
  active: boolean,              // Активность
  created_at: datetime,         // Дата создания
  updated_at: datetime          // Дата обновления
}
```

#### `campaign_proposals` - Предложения кампаний
```javascript
{
  proposal_id: string,          // Уникальный ID предложения
  user_id: string,             // ID пользователя (опционально)
  session_id: string,          // ID сессии
  category: 'artist|track',    // Категория
  user_query: string,          // Запрос пользователя
  ai_analysis: object,         // Анализ AI
  recommended_services: string[], // Рекомендованные сервисы
  total_price: number,         // Общая стоимость
  total_duration: number,      // Общая длительность
  expected_results: object,    // Ожидаемые результаты
  status: string,              // Статус (draft, presented, accepted, etc.)
  expires_at: datetime,        // Срок действия
  created_at: datetime,        // Дата создания
  updated_at: datetime         // Дата обновления
}
```

#### `ai_conversations` - История разговоров
```javascript
{
  conversation_id: string,     // Уникальный ID разговора
  user_id: string,            // ID пользователя (опционально)
  session_id: string,         // ID сессии
  category: 'artist|track',   // Категория
  messages: object[],         // Сообщения
  context: object,            // Контекст разговора
  last_message_at: datetime,  // Последнее сообщение
  created_at: datetime        // Дата создания
}
```

## 🚀 Установка и настройка

### 1. Создание коллекций
```bash
# Создать AI коллекции
npm run setup:ai

# Или по отдельности
node scripts/setup-ai-collections.js
node scripts/seed-ai-data.js
```

### 2. Переменные окружения
Добавьте в `.env.local`:
```env
# AI Collections
NEXT_PUBLIC_APPWRITE_COLLECTION_AI_PROMPTS=ai_prompts
NEXT_PUBLIC_APPWRITE_COLLECTION_AI_RECOMMENDATIONS=ai_recommendations
NEXT_PUBLIC_APPWRITE_COLLECTION_SERVICE_TEMPLATES=service_templates
NEXT_PUBLIC_APPWRITE_COLLECTION_CAMPAIGN_PROPOSALS=campaign_proposals
NEXT_PUBLIC_APPWRITE_COLLECTION_AI_CONVERSATIONS=ai_conversations
```

## 🎨 Компоненты

### `AIPromoHelper`
Основной компонент AI-помощника:
- Переключатель категорий (Artist/Track)
- Поле ввода запроса
- Готовые промпты
- Отображение предложений
- Анимированные карточки сервисов

### `AIService`
Сервис для работы с AI данными:
- `getPromptsByCategory()` - Получение промптов
- `getServiceTemplatesByCategory()` - Получение шаблонов
- `generateAnalysis()` - Генерация анализа
- `createCampaignProposal()` - Создание предложения

## 📊 Готовые данные

### Промпты для артистов:
1. **Продвижение нового релиза** (95% популярность)
2. **Рост в социальных сетях** (88% популярность)
3. **Построение бренда артиста** (75% популярность)
4. **Продвижение концертов** (70% популярность)

### Промпты для треков:
1. **Вирусная стратегия для трека** (92% популярность)
2. **Увеличение прослушиваний** (85% популярность)
3. **Попадание в плейлисты** (80% популярность)
4. **Продвижение на радио** (65% популярность)

### Сервисы для артистов:
- **Instagram**: 15,000₽ / 30 дней
- **TikTok**: 20,000₽ / 21 день
- **YouTube**: 35,000₽ / 60 дней

### Сервисы для треков:
- **Spotify**: 12,000₽ / 14 дней
- **TikTok тренды**: 25,000₽ / 21 день
- **Радиокампания**: 40,000₽ / 30 дней

## 🔄 Алгоритм работы

1. **Выбор категории**: Пользователь выбирает "Артист" или "Трек"
2. **Ввод запроса**: Описание целей или выбор готового промпта
3. **AI анализ**: Система анализирует ключевые слова
4. **Подбор сервисов**: Автоматический выбор релевантных сервисов
5. **Расчет стоимости**: Суммирование цен и времени
6. **Предложение**: Отображение анимированных карточек
7. **Заказ**: Создание задачи в дашборде и уведомление менеджера

## 🎯 Логика подбора сервисов

### Для артистов:
- `instagram/соцсети/подписчик` → Instagram продвижение
- `tiktok/вирус/тренд` → TikTok продвижение
- `youtube/видео/канал` → YouTube развитие

### Для треков:
- `spotify/стрим/прослушивани` → Spotify продвижение
- `tiktok/вирус/тренд` → TikTok тренды
- `радио/эфир` → Радиокампания

## 🎨 UI/UX особенности

### Анимации:
- Плавное появление карточек с задержкой
- Hover эффекты на карточках
- Анимированные фоновые элементы
- Пульсирующие иконки

### Адаптивность:
- Мобильная версия с вертикальными карточками
- Адаптивная сетка сервисов
- Responsive переключатель категорий

### Цветовая схема:
- Градиенты: blue-500 → purple-600
- Фон: black → gray-900
- Акценты: различные цвета для платформ

## 🔧 Расширение функционала

### Добавление новых промптов:
1. Создайте документ в коллекции `ai_prompts`
2. Укажите категорию, теги и популярность
3. Промпт автоматически появится в интерфейсе

### Добавление новых сервисов:
1. Создайте документ в коллекции `service_templates`
2. Укажите платформу, цену и возможности
3. Обновите логику в `AIService.generateAnalysis()`

### Интеграция с реальным AI:
1. Замените моковую логику в `generateAnalysis()`
2. Подключите OpenAI API или другой сервис
3. Добавьте более сложный анализ запросов

## 📈 Метрики и аналитика

Система отслеживает:
- Популярность промптов
- Частоту выбора сервисов
- Конверсию в заказы
- Время сессий пользователей

## 🔒 Безопасность

- Валидация входных данных
- Ограничение длины запросов
- Защита от SQL инъекций через Appwrite
- Контроль доступа к коллекциям

---

**AI-Помощник HIVE Agency готов к использованию! 🎵✨**
