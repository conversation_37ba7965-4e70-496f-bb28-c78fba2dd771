# Коллекции Appwrite для H!VE Agency

## 1. Users (Пользователи)
**Collection ID:** `users`

### Атрибуты:
- `user_id` (string, required) - ID пользователя из Appwrite Auth
- `name` (string, required) - Имя пользователя/артиста
- `email` (string, required) - Email
- `phone` (string, optional) - Телефон
- `avatar` (string, optional) - URL аватара
- `bio` (string, optional) - Биография артиста
- `type` (enum: artist, label, manager) - Тип аккаунта
- `verified` (boolean, default: false) - Верифицирован ли аккаунт
- `tier` (enum: basic, premium, vip) - Уровень подписки
- `social_accounts` (json, optional) - Социальные сети
- `telegram` (string, optional) - Telegram для связи
- `is_dj` (boolean, default: false) - Является ли диджеем
- `mix_link` (string, optional) - Ссылка на микс
- `latest_track` (string, optional) - Ссылка на последний трек
- `youtube_video` (string, optional) - Ссылка на YouTube видео
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## 2. Orders (Заказы)
**Collection ID:** `orders`

### Атрибуты:
- `order_id` (string, required, unique) - Уникальный ID заказа
- `user_id` (string, required) - ID пользователя
- `type` (enum: artist, track) - Тип продвижения
- `service_name` (string, required) - Название услуги
- `status` (enum: pending, in_progress, completed, cancelled) - Статус заказа
- `priority` (enum: low, medium, high) - Приоритет
- `progress` (integer, default: 0) - Прогресс выполнения (0-100)
- `price` (integer, required) - Стоимость в копейках
- `assigned_to` (string, optional) - Кому назначен заказ
- `services_selected` (json, required) - Выбранные услуги
- `track_info` (json, optional) - Информация о треке
- `artist_info` (json, optional) - Информация об артисте
- `files` (json, optional) - Загруженные файлы
- `notes` (string, optional) - Заметки
- `deadline` (datetime, optional) - Дедлайн
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## 3. Tracks (Треки)
**Collection ID:** `tracks`

### Атрибуты:
- `track_id` (string, required, unique) - ID трека
- `user_id` (string, required) - ID владельца
- `order_id` (string, optional) - Связанный заказ
- `title` (string, required) - Название трека
- `artist` (string, required) - Исполнитель
- `genre` (string, required) - Жанр
- `duration` (string, optional) - Длительность
- `description` (string, optional) - Описание
- `track_file_url` (string, optional) - URL файла трека
- `artwork_url` (string, optional) - URL обложки
- `release_on_sacral` (boolean, default: false) - Релиз на Sacral Track
- `sacral_track_id` (string, optional) - ID на Sacral Track
- `streaming_links` (json, optional) - Ссылки на стриминги
- `statistics` (json, optional) - Статистика прослушиваний
- `royalties` (json, optional) - Данные о роялти
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## 4. Social_Pages (Социальные страницы)
**Collection ID:** `social_pages`

### Атрибуты:
- `page_id` (string, required, unique) - ID страницы
- `user_id` (string, required) - ID владельца
- `order_id` (string, optional) - Связанный заказ
- `platform` (enum: vk, instagram, youtube, tiktok, telegram) - Платформа
- `page_url` (string, required) - URL страницы
- `username` (string, required) - Имя пользователя
- `status` (enum: created, active, suspended) - Статус страницы
- `followers_count` (integer, default: 0) - Количество подписчиков
- `posts_count` (integer, default: 0) - Количество постов
- `engagement_rate` (float, default: 0) - Уровень вовлеченности
- `last_post_date` (datetime, optional) - Дата последнего поста
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## 5. Campaigns (Кампании)
**Collection ID:** `campaigns`

### Атрибуты:
- `campaign_id` (string, required, unique) - ID кампании
- `order_id` (string, required) - Связанный заказ
- `user_id` (string, required) - ID клиента
- `name` (string, required) - Название кампании
- `type` (enum: radio, social, email, clubs, forums) - Тип кампании
- `status` (enum: draft, active, paused, completed) - Статус
- `start_date` (datetime, required) - Дата начала
- `end_date` (datetime, optional) - Дата окончания
- `budget` (integer, required) - Бюджет в копейках
- `spent` (integer, default: 0) - Потрачено
- `target_audience` (json, optional) - Целевая аудитория
- `platforms` (json, required) - Платформы для продвижения
- `metrics` (json, optional) - Метрики кампании
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## 6. Statistics (Статистика)
**Collection ID:** `statistics`

### Атрибуты:
- `stat_id` (string, required, unique) - ID записи статистики
- `user_id` (string, required) - ID пользователя
- `track_id` (string, optional) - ID трека
- `platform` (string, required) - Платформа
- `metric_type` (enum: plays, views, likes, shares, downloads) - Тип метрики
- `value` (integer, required) - Значение
- `date` (datetime, required) - Дата записи
- `source` (string, optional) - Источник данных
- `campaign_id` (string, optional) - Связанная кампания
- `created_at` (datetime, required)

## 7. Threads_Posts (Посты в Threads)
**Collection ID:** `threads_posts`

### Атрибуты:
- `post_id` (string, required, unique) - ID поста
- `user_id` (string, required) - ID пользователя
- `track_id` (string, optional) - ID трека
- `content` (string, required) - Содержимое поста
- `post_url` (string, optional) - URL поста
- `likes_count` (integer, default: 0) - Количество лайков
- `comments_count` (integer, default: 0) - Количество комментариев
- `shares_count` (integer, default: 0) - Количество репостов
- `posted_at` (datetime, required) - Дата публикации
- `bot_generated` (boolean, default: true) - Создан ботом
- `created_at` (datetime, required)

## 8. Royalties (Роялти)
**Collection ID:** `royalties`

### Атрибуты:
- `royalty_id` (string, required, unique) - ID записи роялти
- `user_id` (string, required) - ID пользователя
- `track_id` (string, required) - ID трека
- `platform` (string, required) - Платформа
- `amount` (integer, required) - Сумма в копейках
- `currency` (string, default: RUB) - Валюта
- `period_start` (datetime, required) - Начало периода
- `period_end` (datetime, required) - Конец периода
- `plays_count` (integer, required) - Количество прослушиваний
- `rate_per_play` (float, required) - Ставка за прослушивание
- `status` (enum: pending, paid, cancelled) - Статус выплаты
- `paid_at` (datetime, optional) - Дата выплаты
- `created_at` (datetime, required)

## 9. Files (Файлы)
**Collection ID:** `files`

### Атрибуты:
- `file_id` (string, required, unique) - ID файла
- `user_id` (string, required) - ID владельца
- `order_id` (string, optional) - Связанный заказ
- `track_id` (string, optional) - Связанный трек
- `file_name` (string, required) - Имя файла
- `file_type` (enum: audio, image, video, document) - Тип файла
- `file_url` (string, required) - URL файла
- `file_size` (integer, required) - Размер в байтах
- `mime_type` (string, required) - MIME тип
- `purpose` (enum: track, artwork, video, document) - Назначение файла
- `created_at` (datetime, required)

## 10. Team_Members (Члены команды)
**Collection ID:** `team_members`

### Атрибуты:
- `member_id` (string, required, unique) - ID сотрудника
- `user_id` (string, required) - ID пользователя
- `name` (string, required) - Имя сотрудника
- `email` (string, required) - Email
- `role` (enum: admin, manager, specialist, intern) - Роль
- `department` (enum: promotion, social, radio, design) - Отдел
- `permissions` (json, required) - Права доступа
- `active` (boolean, default: true) - Активен ли сотрудник
- `hire_date` (datetime, required) - Дата найма
- `created_at` (datetime, required)
- `updated_at` (datetime, required)

## Индексы для оптимизации:

### Users:
- `user_id` (unique)
- `email` (unique)
- `type`

### Orders:
- `order_id` (unique)
- `user_id`
- `status`
- `created_at`

### Tracks:
- `track_id` (unique)
- `user_id`
- `order_id`

### Social_Pages:
- `page_id` (unique)
- `user_id`
- `platform`

### Statistics:
- `user_id`
- `track_id`
- `platform`
- `date`

### Campaigns:
- `campaign_id` (unique)
- `order_id`
- `user_id`
- `status`

## Связи между коллекциями:

1. **Users** ← **Orders** (один ко многим)
2. **Orders** ← **Tracks** (один к одному/многим)
3. **Orders** ← **Campaigns** (один ко многим)
4. **Users** ← **Social_Pages** (один ко многим)
5. **Tracks** ← **Statistics** (один ко многим)
6. **Tracks** ← **Royalties** (один ко многим)
7. **Users** ← **Files** (один ко многим)
8. **Orders** ← **Files** (один ко многим)
