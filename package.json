{"name": "hive-agency", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true next build", "build:analyze": "npm run analyze", "export": "next export", "type-check": "tsc --noEmit", "lighthouse": "lhci autorun", "sitemap": "next-sitemap", "optimize": "npm run lint && npm run type-check && npm run build", "setup:appwrite": "node scripts/setup-appwrite.js", "setup:attributes": "node scripts/setup-attributes.js", "seed:data": "node scripts/seed-data.js", "setup:ai": "node scripts/setup-ai-collections.js && node scripts/seed-ai-data.js", "setup:db": "npm run setup:appwrite && npm run setup:attributes && npm run seed:data && npm run setup:ai"}, "dependencies": {"@heroicons/react": "^2.2.0", "@next/bundle-analyzer": "^15.4.1", "@stripe/stripe-js": "^7.4.0", "appwrite": "^18.1.1", "critters": "^0.0.23", "framer-motion": "^12.23.3", "lucide-react": "^0.525.0", "next": "15.3.5", "next-i18next": "^15.4.2", "next-seo": "^6.8.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^4.12.0", "stripe": "^18.3.0", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "node-appwrite": "^17.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}