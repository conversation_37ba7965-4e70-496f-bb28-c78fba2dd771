# 🎉 HIVE Agency - Appwrite Database Setup Complete

## ✅ Что было создано

### 📦 **Новые коллекции:**

1. **pricing_tiers** - Тарифные планы
2. **social_pages** - Социальные страницы пользователей  
3. **statistics** - Статистика продвижения
4. **royalties** - Роялти и выплаты
5. **files** - Файлы пользователей
6. **bot_activations** - Активации ботов
7. **email_campaigns** - Email кампании
8. **telegram_channels** - Telegram каналы
9. **payment_transactions** - Платежные транзакции
10. **admin_logs** - <PERSON>оги администраторов

### 🔧 **Обновленные коллекции:**

1. **users** - Добавлены атрибуты для профилей артистов
2. **orders** - Полная система заказов с платежами
3. **notifications** - Система уведомлений

## 📊 Структура данных

### **Pricing Tiers (Тарифные планы)**
```typescript
{
  tier_id: string,           // Уникальный ID тарифа
  name: string,              // Название тарифа
  type: 'artist' | 'track',  // Тип продвижения
  price: number,             // Цена в копейках
  currency: string,          // Валюта (RUB)
  duration: number,          // Длительность в днях
  features: JSON,            // Список функций
  popular: boolean,          // Популярный тариф
  limits: JSON,              // Лимиты тарифа
  active: boolean,           // Активен ли тариф
  created_at: datetime,
  updated_at: datetime
}
```

### **Orders (Заказы)**
```typescript
{
  order_id: string,          // Уникальный ID заказа
  user_id: string,           // ID пользователя
  pricing_tier_id: string,   // ID тарифного плана
  service_type: 'artist' | 'track',
  status: 'draft' | 'pending_payment' | 'paid' | 'in_progress' | 'completed' | 'cancelled',
  payment_status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded',
  payment_method: 'card' | 'crypto' | 'sbp',
  price: number,             // Цена в копейках
  currency: string,          // Валюта
  client_name: string,       // Имя клиента
  client_email: string,      // Email клиента
  progress: number,          // Прогресс выполнения (0-100)
  requirements: JSON,        // Требования к заказу
  metadata: JSON,            // Дополнительные данные
  created_at: datetime,
  updated_at: datetime
}
```

### **Bot Activations (Активации ботов)**
```typescript
{
  activation_id: string,     // Уникальный ID активации
  user_id: string,           // ID пользователя
  order_id: string,          // ID заказа
  bot_type: 'like_bot' | 'comment_bot' | 'follow_bot' | 'view_bot' | 'share_bot',
  platform: string,         // Платформа (instagram, tiktok, etc.)
  target_url: string,        // Целевая ссылка
  status: 'pending' | 'active' | 'paused' | 'completed' | 'failed',
  actions_completed: number, // Выполнено действий
  actions_target: number,    // Целевое количество действий
  config: JSON,              // Конфигурация бота
  created_at: datetime,
  updated_at: datetime
}
```

### **Statistics (Статистика)**
```typescript
{
  stat_id: string,           // Уникальный ID статистики
  user_id: string,           // ID пользователя
  track_id: string,          // ID трека (опционально)
  order_id: string,          // ID заказа (опционально)
  platform: string,         // Платформа
  metric_type: 'plays' | 'likes' | 'shares' | 'comments' | 'followers' | 'reach',
  value: number,             // Значение метрики
  date: datetime,            // Дата измерения
  metadata: JSON,            // Дополнительные данные
  created_at: datetime
}
```

## 🎯 **Тестовые данные**

### **Созданы тарифные планы:**

#### **Продвижение артистов:**
- 🥉 **Старт** - 25,000 ₽/30 дней
- 🥈 **Профи** - 50,000 ₽/30 дней ⭐ (Популярный)
- 🥇 **Энтерпрайз** - 100,000 ₽/30 дней

#### **Продвижение треков:**
- 🎵 **Базовый** - 15,000 ₽/14 дней
- 🎵 **Вирусный** - 35,000 ₽/21 день ⭐ (Популярный)
- 🎵 **Платиновый** - 75,000 ₽/30 дней

## 🔍 **Созданные индексы для оптимизации:**

### **Users:**
- `user_id_unique` (уникальный)
- `email_unique` (уникальный)
- `type_index` (по типу пользователя)

### **Orders:**
- `order_id_unique` (уникальный)
- `user_id_index` (по пользователю)
- `status_index` (по статусу)
- `created_at_index` (по дате создания, desc)

### **Notifications:**
- `notification_id_unique` (уникальный)
- `user_id_index` (по пользователю)
- `is_read_index` (по статусу прочтения)
- `created_at_index` (по дате создания, desc)

## 🚀 **Следующие шаги:**

1. **Обновить компоненты** для работы с новыми коллекциями
2. **Подключить реальные тарифы** из базы данных
3. **Настроить систему платежей** с созданием заказов
4. **Добавить админ-панель** для управления ботами
5. **Интегрировать статистику** в дашборд

## 📋 **Команды для управления:**

```bash
# Создать коллекции
npm run setup:appwrite

# Создать атрибуты и индексы
npm run setup:attributes

# Заполнить тестовыми данными
npm run seed:data

# Полная настройка (все вместе)
npm run setup:db
```

## 🔗 **Переменные окружения обновлены:**

Все новые коллекции добавлены в `.env.local` и готовы к использованию в коде.

**База данных HIVE Agency полностью настроена и готова к продакшену! 🎵✨**
