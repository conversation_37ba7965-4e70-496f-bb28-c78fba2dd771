// Сервис для работы с криптоплатежами
// Интеграция с популярными криптовалютными платежными системами

export interface CryptoPayment {
  id: string;
  amount: number;
  currency: string;
  cryptoCurrency: string;
  cryptoAmount: number;
  address: string;
  status: 'pending' | 'confirming' | 'completed' | 'failed' | 'expired';
  txHash?: string;
  confirmations: number;
  requiredConfirmations: number;
  createdAt: string;
  expiresAt: string;
  orderId: string;
  userId: string;
}

export interface CryptoRate {
  currency: string;
  rate: number;
  lastUpdated: string;
}

export interface CreateCryptoPaymentParams {
  amount: number;
  currency: string;
  cryptoCurrency: string;
  orderId: string;
  userId: string;
  description?: string;
  callbackUrl?: string;
}

class CryptoPaymentService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_CRYPTO_API_KEY || '';
    this.baseUrl = process.env.NEXT_PUBLIC_CRYPTO_API_URL || 'https://api.cryptopay.com';
  }

  // Получение актуальных курсов криптовалют
  async getCryptoRates(): Promise<CryptoRate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/rates`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch crypto rates');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching crypto rates:', error);
      throw error;
    }
  }

  // Создание криптоплатежа
  async createPayment(params: CreateCryptoPaymentParams): Promise<CryptoPayment> {
    try {
      const response = await fetch(`${this.baseUrl}/payments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('Failed to create crypto payment');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating crypto payment:', error);
      throw error;
    }
  }

  // Получение статуса платежа
  async getPaymentStatus(paymentId: string): Promise<CryptoPayment> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment status');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment status:', error);
      throw error;
    }
  }

  // Конвертация фиатной валюты в криптовалюту
  async convertToCrypto(amount: number, fromCurrency: string, toCrypto: string): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/convert`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          from: fromCurrency,
          to: toCrypto,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to convert currency');
      }

      const result = await response.json();
      return result.convertedAmount;
    } catch (error) {
      console.error('Error converting currency:', error);
      throw error;
    }
  }

  // Получение поддерживаемых криптовалют
  getSupportedCryptocurrencies(): Array<{
    code: string;
    name: string;
    symbol: string;
    network: string;
    minAmount: number;
    confirmations: number;
  }> {
    return [
      {
        code: 'BTC',
        name: 'Bitcoin',
        symbol: '₿',
        network: 'Bitcoin',
        minAmount: 0.0001,
        confirmations: 3,
      },
      {
        code: 'ETH',
        name: 'Ethereum',
        symbol: 'Ξ',
        network: 'Ethereum',
        minAmount: 0.001,
        confirmations: 12,
      },
      {
        code: 'USDT',
        name: 'Tether',
        symbol: '₮',
        network: 'Ethereum',
        minAmount: 1,
        confirmations: 12,
      },
      {
        code: 'USDC',
        name: 'USD Coin',
        symbol: '$',
        network: 'Ethereum',
        minAmount: 1,
        confirmations: 12,
      },
      {
        code: 'LTC',
        name: 'Litecoin',
        symbol: 'Ł',
        network: 'Litecoin',
        minAmount: 0.001,
        confirmations: 6,
      },
      {
        code: 'BCH',
        name: 'Bitcoin Cash',
        symbol: '₿',
        network: 'Bitcoin Cash',
        minAmount: 0.001,
        confirmations: 6,
      },
      {
        code: 'BNB',
        name: 'Binance Coin',
        symbol: 'BNB',
        network: 'BSC',
        minAmount: 0.01,
        confirmations: 15,
      },
      {
        code: 'ADA',
        name: 'Cardano',
        symbol: '₳',
        network: 'Cardano',
        minAmount: 1,
        confirmations: 15,
      },
      {
        code: 'DOT',
        name: 'Polkadot',
        symbol: '●',
        network: 'Polkadot',
        minAmount: 0.1,
        confirmations: 10,
      },
      {
        code: 'MATIC',
        name: 'Polygon',
        symbol: '⬟',
        network: 'Polygon',
        minAmount: 1,
        confirmations: 128,
      },
    ];
  }

  // Валидация криптоадреса
  validateCryptoAddress(address: string, cryptocurrency: string): boolean {
    const patterns: Record<string, RegExp> = {
      BTC: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
      ETH: /^0x[a-fA-F0-9]{40}$/,
      LTC: /^[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}$/,
      BCH: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bitcoincash:[a-z0-9]{42}$/,
      USDT: /^0x[a-fA-F0-9]{40}$/,
      USDC: /^0x[a-fA-F0-9]{40}$/,
      BNB: /^0x[a-fA-F0-9]{40}$/,
      ADA: /^addr1[a-z0-9]{98}$/,
      DOT: /^1[a-zA-Z0-9]{46}$/,
      MATIC: /^0x[a-fA-F0-9]{40}$/,
    };

    const pattern = patterns[cryptocurrency.toUpperCase()];
    return pattern ? pattern.test(address) : false;
  }

  // Форматирование криптосуммы
  formatCryptoAmount(amount: number, cryptocurrency: string): string {
    const decimals: Record<string, number> = {
      BTC: 8,
      ETH: 6,
      LTC: 8,
      BCH: 8,
      USDT: 2,
      USDC: 2,
      BNB: 4,
      ADA: 2,
      DOT: 4,
      MATIC: 2,
    };

    const decimal = decimals[cryptocurrency.toUpperCase()] || 6;
    return amount.toFixed(decimal);
  }

  // Получение QR-кода для платежа
  generatePaymentQR(address: string, amount: number, cryptocurrency: string): string {
    const qrData = `${cryptocurrency.toLowerCase()}:${address}?amount=${amount}`;
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData)}`;
  }

  // Мониторинг платежа
  async monitorPayment(paymentId: string, onUpdate: (payment: CryptoPayment) => void): Promise<void> {
    const checkStatus = async () => {
      try {
        const payment = await this.getPaymentStatus(paymentId);
        onUpdate(payment);

        if (payment.status === 'completed' || payment.status === 'failed' || payment.status === 'expired') {
          return;
        }

        // Проверяем статус каждые 30 секунд
        setTimeout(checkStatus, 30000);
      } catch (error) {
        console.error('Error monitoring payment:', error);
        setTimeout(checkStatus, 60000); // Повторяем через минуту при ошибке
      }
    };

    checkStatus();
  }

  // Получение истории криптоплатежей
  async getPaymentHistory(userId: string): Promise<CryptoPayment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/history?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment history');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }
}

export const cryptoPaymentService = new CryptoPaymentService();
