import { databases, storage, ID } from '@/lib/appwrite';
import { Query } from 'appwrite';

export interface AIPrompt {
  $id: string;
  prompt_id: string;
  category: 'artist' | 'track';
  title: string;
  description: string;
  prompt_text: string;
  tags: string[];
  popularity: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ServiceTemplate {
  $id: string;
  template_id: string;
  category: 'artist' | 'track';
  service_type: string;
  platform: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  base_price: number;
  duration_days: number;
  features: string[];
  metrics: any;
  requirements: string[];
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CampaignProposal {
  $id?: string;
  proposal_id: string;
  user_id?: string;
  session_id: string;
  category: 'artist' | 'track';
  user_query: string;
  ai_analysis: any;
  recommended_services: string[];
  total_price: number;
  total_duration: number;
  expected_results: any;
  status: 'draft' | 'presented' | 'accepted' | 'rejected' | 'ordered';
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AIConversation {
  $id?: string;
  conversation_id: string;
  user_id?: string;
  session_id: string;
  category: 'artist' | 'track';
  messages: any[];
  context: any;
  last_message_at: string;
  created_at: string;
}

const COLLECTIONS = {
  AI_PROMPTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_AI_PROMPTS || 'ai_prompts',
  SERVICE_TEMPLATES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SERVICE_TEMPLATES || 'service_templates',
  CAMPAIGN_PROPOSALS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CAMPAIGN_PROPOSALS || 'campaign_proposals',
  AI_CONVERSATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_AI_CONVERSATIONS || 'ai_conversations',
};

export class AIService {
  // Получить промпты по категории
  static async getPromptsByCategory(category: 'artist' | 'track'): Promise<AIPrompt[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.AI_PROMPTS,
        [
          Query.equal('category', category),
          Query.equal('active', true),
          Query.orderDesc('popularity')
        ]
      );

      return response.documents.map(doc => ({
        ...doc,
        tags: typeof doc.tags === 'string' ? JSON.parse(doc.tags) : doc.tags
      })) as AIPrompt[];
    } catch (error) {
      console.error('Error fetching prompts:', error);
      throw error;
    }
  }

  // Получить шаблоны сервисов по категории
  static async getServiceTemplatesByCategory(category: 'artist' | 'track'): Promise<ServiceTemplate[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.SERVICE_TEMPLATES,
        [
          Query.equal('category', category),
          Query.equal('active', true),
          Query.orderAsc('base_price')
        ]
      );

      return response.documents.map(doc => ({
        ...doc,
        features: typeof doc.features === 'string' ? JSON.parse(doc.features) : doc.features,
        metrics: typeof doc.metrics === 'string' ? JSON.parse(doc.metrics) : doc.metrics,
        requirements: typeof doc.requirements === 'string' ? JSON.parse(doc.requirements) : doc.requirements
      })) as ServiceTemplate[];
    } catch (error) {
      console.error('Error fetching service templates:', error);
      throw error;
    }
  }

  // Создать предложение кампании
  static async createCampaignProposal(proposal: Omit<CampaignProposal, '$id' | 'created_at' | 'updated_at'>): Promise<CampaignProposal> {
    try {
      const now = new Date().toISOString();
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 дней

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CAMPAIGN_PROPOSALS,
        ID.unique(),
        {
          ...proposal,
          ai_analysis: JSON.stringify(proposal.ai_analysis),
          recommended_services: JSON.stringify(proposal.recommended_services),
          expected_results: JSON.stringify(proposal.expected_results),
          expires_at: expiresAt,
          created_at: now,
          updated_at: now
        }
      );

      return {
        ...response,
        ai_analysis: typeof response.ai_analysis === 'string' ? JSON.parse(response.ai_analysis) : response.ai_analysis,
        recommended_services: typeof response.recommended_services === 'string' ? JSON.parse(response.recommended_services) : response.recommended_services,
        expected_results: typeof response.expected_results === 'string' ? JSON.parse(response.expected_results) : response.expected_results
      } as CampaignProposal;
    } catch (error) {
      console.error('Error creating campaign proposal:', error);
      throw error;
    }
  }

  // Обновить статус предложения
  static async updateProposalStatus(proposalId: string, status: CampaignProposal['status']): Promise<void> {
    try {
      await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.CAMPAIGN_PROPOSALS,
        proposalId,
        {
          status,
          updated_at: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error updating proposal status:', error);
      throw error;
    }
  }

  // Сохранить разговор с AI
  static async saveConversation(conversation: Omit<AIConversation, '$id' | 'created_at'>): Promise<AIConversation> {
    try {
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.AI_CONVERSATIONS,
        ID.unique(),
        {
          ...conversation,
          messages: JSON.stringify(conversation.messages),
          context: JSON.stringify(conversation.context),
          created_at: new Date().toISOString()
        }
      );

      return {
        ...response,
        messages: typeof response.messages === 'string' ? JSON.parse(response.messages) : response.messages,
        context: typeof response.context === 'string' ? JSON.parse(response.context) : response.context
      } as AIConversation;
    } catch (error) {
      console.error('Error saving conversation:', error);
      throw error;
    }
  }

  // Генерация AI анализа (моковая функция)
  static async generateAnalysis(query: string, category: 'artist' | 'track'): Promise<{
    analysis: string;
    recommendedServices: ServiceTemplate[];
    expectedResults: any;
  }> {
    try {
      // Получаем шаблоны сервисов
      const allServices = await this.getServiceTemplatesByCategory(category);
      
      // Простая логика выбора сервисов на основе ключевых слов
      const keywords = query.toLowerCase();
      let recommendedServices: ServiceTemplate[] = [];

      if (category === 'artist') {
        if (keywords.includes('instagram') || keywords.includes('соцсети') || keywords.includes('подписчик')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'Instagram'));
        }
        if (keywords.includes('tiktok') || keywords.includes('вирус') || keywords.includes('тренд')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'TikTok'));
        }
        if (keywords.includes('youtube') || keywords.includes('видео') || keywords.includes('канал')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'YouTube'));
        }
        
        // Если ничего не найдено, берем топ-3
        if (recommendedServices.length === 0) {
          recommendedServices = allServices.slice(0, 3);
        }
      } else {
        if (keywords.includes('spotify') || keywords.includes('стрим') || keywords.includes('прослушивани')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'Spotify'));
        }
        if (keywords.includes('tiktok') || keywords.includes('вирус') || keywords.includes('тренд')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'TikTok'));
        }
        if (keywords.includes('радио') || keywords.includes('эфир')) {
          recommendedServices.push(...allServices.filter(s => s.platform === 'Radio'));
        }
        
        // Если ничего не найдено, берем топ-3
        if (recommendedServices.length === 0) {
          recommendedServices = allServices.slice(0, 3);
        }
      }

      // Убираем дубликаты
      recommendedServices = recommendedServices.filter((service, index, self) => 
        index === self.findIndex(s => s.template_id === service.template_id)
      );

      const analysis = `На основе вашего запроса "${query}" я рекомендую комплексную стратегию ${
        category === 'artist' ? 'продвижения артиста' : 'продвижения трека'
      }. Эта кампания охватит основные каналы и обеспечит максимальный охват вашей целевой аудитории.`;

      const expectedResults = {
        reach: category === 'artist' ? '500K-2M' : '1M-10M',
        engagement: '+150-300%',
        conversion: '5-15%',
        timeline: `${Math.max(...recommendedServices.map(s => s.duration_days))} дней`
      };

      return {
        analysis,
        recommendedServices,
        expectedResults
      };
    } catch (error) {
      console.error('Error generating analysis:', error);
      throw error;
    }
  }

  // Форматирование цены
  static formatPrice(price: number, currency: string = 'RUB'): string {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  }

  // Генерация уникального ID сессии
  static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Получение иконки платформы
  static getPlatformIcon(platform: string): string {
    const icons: Record<string, string> = {
      'Instagram': '📷',
      'TikTok': '🎵',
      'YouTube': '📺',
      'Spotify': '🎧',
      'Radio': '📻',
      'Twitter': '🐦',
      'VK': '🔵',
      'Telegram': '✈️'
    };
    return icons[platform] || '🎯';
  }

  // Новые методы для AI-агента
  async saveAIConversation(userId: string, type: 'artist' | 'track', conversation: any): Promise<string> {
    try {
      const document = await databases.createDocument(
        process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!,
        process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_AI_CONVERSATIONS!,
        ID.unique(),
        {
          userId,
          type,
          conversation: JSON.stringify(conversation),
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return document.$id;
    } catch (error) {
      console.error('Error saving AI conversation:', error);
      throw error;
    }
  }

  async uploadArtwork(file: File, userId: string): Promise<string> {
    try {
      const fileId = ID.unique();
      const uploadedFile = await storage.createFile(
        process.env.NEXT_PUBLIC_APPWRITE_BUCKET_IMAGES!,
        fileId,
        file
      );

      const fileUrl = storage.getFileView(
        process.env.NEXT_PUBLIC_APPWRITE_BUCKET_IMAGES!,
        uploadedFile.$id
      );
      return fileUrl.toString();
    } catch (error) {
      console.error('Error uploading artwork:', error);
      throw error;
    }
  }

  async generateServiceRecommendations(type: 'artist' | 'track', userData: any): Promise<ServiceTemplate[]> {
    try {
      // Получаем шаблоны сервисов из базы
      const templates = await this.getServiceTemplates(type);

      // Простая логика фильтрации на основе бюджета
      let filtered = templates;
      if (userData.budget) {
        filtered = templates.filter(t => t.base_price <= userData.budget);
      }

      // Возвращаем 3-6 лучших рекомендаций
      return filtered.slice(0, Math.min(6, Math.max(3, filtered.length)));
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return this.getDefaultRecommendations(type);
    }
  }

  private getDefaultRecommendations(type: 'artist' | 'track'): ServiceTemplate[] {
    return [
      {
        $id: '1',
        template_id: 'tiktok_promo',
        category: type,
        service_type: 'social_promotion',
        platform: 'TikTok',
        name: 'TikTok Продвижение',
        description: 'Вирусное продвижение в TikTok с таргетингом',
        icon: '🎵',
        color: 'from-pink-500 to-purple-600',
        base_price: 25000,
        duration_days: 30,
        features: ['Таргетинг', 'Вирусный контент', 'Аналитика'],
        estimated_reach: '500K - 1.2M',
        success_rate: 85,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        $id: '2',
        template_id: 'instagram_promo',
        category: type,
        service_type: 'social_promotion',
        platform: 'Instagram',
        name: 'Instagram Stories & Reels',
        description: 'Продвижение через Stories и Reels',
        icon: '📷',
        color: 'from-purple-500 to-pink-600',
        base_price: 35000,
        duration_days: 30,
        features: ['Микро-инфлюенсеры', 'Stories & Reels', 'Геотаргетинг'],
        estimated_reach: '300K - 800K',
        success_rate: 78,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        $id: '3',
        template_id: 'youtube_promo',
        category: type,
        service_type: 'video_promotion',
        platform: 'YouTube',
        name: 'YouTube Shorts',
        description: 'Создание и продвижение YouTube Shorts',
        icon: '📺',
        color: 'from-red-500 to-orange-600',
        base_price: 30000,
        duration_days: 30,
        features: ['YouTube Shorts', 'SEO оптимизация', 'Монетизация'],
        estimated_reach: '200K - 600K',
        success_rate: 82,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}

// Экспортируем экземпляр сервиса
export const aiService = new AIService();
