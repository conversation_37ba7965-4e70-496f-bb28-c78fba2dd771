import { Client, Databases, Account, Storage, Query, ID } from 'appwrite';

// Appwrite configuration with environment variable checks
const appwriteEndpoint = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT;
const appwriteProjectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID;

if (!appwriteEndpoint) {
  console.warn('NEXT_PUBLIC_APPWRITE_ENDPOINT not found in environment variables');
}

if (!appwriteProjectId) {
  console.warn('NEXT_PUBLIC_APPWRITE_PROJECT_ID not found in environment variables');
}

const client = new Client();

if (appwriteEndpoint && appwriteProjectId) {
  client
    .setEndpoint(appwriteEndpoint)
    .setProject(appwriteProjectId);
}

export const databases = appwriteEndpoint && appwriteProjectId ? new Databases(client) : null;
export const account = appwriteEndpoint && appwriteProjectId ? new Account(client) : null;
export const storage = appwriteEndpoint && appwriteProjectId ? new Storage(client) : null;

// Database and Collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '';
export const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || '',
  ARTISTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ARTISTS || '',
  TRACKS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TRACKS || '',
  SUBSCRIPTIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SUBSCRIPTIONS || '',
  CAMPAIGNS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CAMPAIGNS || '',
  PARTNERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PARTNERS || '',
  TEAM_MEMBERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TEAM_MEMBERS || '',
  CASE_STUDIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CASE_STUDIES || '',
  SERVICES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SERVICES || '',
  CONTACTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACTS || '',
  NOTIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_NOTIFICATIONS || '',
  ORDERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ORDERS || '',
};

// Storage Buckets
export const BUCKETS = {
  AVATARS: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_AVATARS || '',
  TRACKS: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_TRACKS || '',
  IMAGES: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_IMAGES || '',
};

// Notification Service with fallbacks for missing environment variables
export class AppwriteNotificationService {
  // Create notification
  static async createNotification(notification: {
    userId: string;
    type: string;
    category: string;
    title: string;
    message: string;
    actionUrl?: string;
    actionText?: string;
    relatedOrderId?: string;
    relatedUserId?: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    isImportant?: boolean;
    metadata?: Record<string, unknown>;
  }) {
    if (!databases) {
      console.warn('Appwrite not configured, returning mock notification');
      return { $id: 'mock-id', ...notification };
    }
    
    try {
      const result = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        ID.unique(),
        {
          notificationId: ID.unique(),
          userId: notification.userId,
          type: notification.type,
          category: notification.category,
          title: notification.title,
          message: notification.message,
          actionUrl: notification.actionUrl || null,
          actionText: notification.actionText || null,
          relatedOrderId: notification.relatedOrderId || null,
          relatedUserId: notification.relatedUserId || null,
          priority: notification.priority,
          isImportant: notification.isImportant || false,
          isRead: false,
          metadata: notification.metadata || {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  // Get user notifications
  static async getUserNotifications(userId: string, limit = 50) {
    if (!databases) {
      console.warn('Appwrite not configured, returning empty notifications');
      return [];
    }
    
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit)
        ]
      );
      return result.documents;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return null;
    }
    
    try {
      const result = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId,
        {
          isRead: true,
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return null;
    }
  }

  // Mark notification as unread
  static async markAsUnread(notificationId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return null;
    }
    
    try {
      const result = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId,
        {
          isRead: false,
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      return null;
    }
  }

  // Delete notification
  static async deleteNotification(notificationId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return false;
    }
    
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId
      );
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      return false;
    }
  }

  // Mark all as read
  static async markAllAsRead(userId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return false;
    }
    
    try {
      const notifications = await this.getUserNotifications(userId, 100);
      const unreadNotifications = notifications.filter((n: any) => !n.isRead);
      
      const promises = unreadNotifications.map((notification: any) =>
        this.markAsRead(notification.$id)
      );
      
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  }

  // Clear all notifications
  static async clearAll(userId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return false;
    }
    
    try {
      const notifications = await this.getUserNotifications(userId, 100);
      
      const promises = notifications.map((notification: any) =>
        this.deleteNotification(notification.$id)
      );
      
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      return false;
    }
  }

  // Get unread count
  static async getUnreadCount(userId: string) {
    if (!databases) {
      console.warn('Appwrite not configured');
      return 0;
    }
    
    try {
      const result = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        [
          Query.equal('userId', userId),
          Query.equal('isRead', false),
          Query.limit(1000)
        ]
      );
      return result.total;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }
}
