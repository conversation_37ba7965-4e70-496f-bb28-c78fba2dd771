import { databases, ID } from '@/lib/appwrite';
import { Query } from 'appwrite';

export interface Order {
  id?: string;
  userId: string;
  type: 'artist' | 'track';
  services: Array<{
    serviceId: string;
    title: string;
    platform: string;
    price: number;
    estimatedReach: string;
  }>;
  totalAmount: number;
  status: 'pending' | 'paid' | 'processing' | 'completed' | 'cancelled';
  paymentMethod?: 'stripe' | 'crypto' | 'bank_transfer';
  paymentId?: string;
  aiConversationId?: string;
  projectData: {
    title: string;
    description: string;
    genre?: string;
    targetAudience?: string;
    artworkUrl?: string;
    goals?: string;
  };
  deliverables: Array<{
    title: string;
    description: string;
    status: 'pending' | 'in_progress' | 'completed';
    dueDate: Date;
  }>;
  statistics?: {
    totalReach: number;
    totalPlays: number;
    totalFollowers: number;
    engagementRate: number;
    platforms: Record<string, unknown>;
  };
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface OrderStats {
  totalOrders: number;
  totalRevenue: number;
  activeOrders: number;
  completedOrders: number;
  averageOrderValue: number;
}

class OrderService {
  private databaseId = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;
  private ordersCollectionId = process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ORDERS!;
  private notificationsCollectionId = process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_NOTIFICATIONS!;

  // Создать новый заказ
  async createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
    try {
      const document = await databases.createDocument(
        this.databaseId,
        this.ordersCollectionId,
        ID.unique(),
        {
          ...orderData,
          services: JSON.stringify(orderData.services),
          projectData: JSON.stringify(orderData.projectData),
          deliverables: JSON.stringify(orderData.deliverables),
          statistics: JSON.stringify(orderData.statistics || {}),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      // Создаем уведомление для пользователя
      await this.createNotification(orderData.userId, {
        type: 'order_created',
        title: 'Заказ создан',
        message: `Ваш заказ на сумму ${orderData.totalAmount.toLocaleString()} ₽ успешно создан`,
        orderId: document.$id
      });

      return this.parseOrder(document);
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Обновить статус заказа
  async updateOrderStatus(orderId: string, status: Order['status'], paymentId?: string): Promise<Order> {
    try {
      const updateData: Record<string, unknown> = {
        status,
        updatedAt: new Date().toISOString(),
      };

      if (paymentId) {
        updateData.paymentId = paymentId;
      }

      if (status === 'completed') {
        updateData.completedAt = new Date().toISOString();
      }

      const document = await databases.updateDocument(
        this.databaseId,
        this.ordersCollectionId,
        orderId,
        updateData
      );

      // Создаем уведомление об изменении статуса
      const order = this.parseOrder(document);
      await this.createNotification(order.userId, {
        type: 'order_status_changed',
        title: 'Статус заказа изменен',
        message: this.getStatusMessage(status),
        orderId: orderId
      });

      return order;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Получить заказы пользователя
  async getUserOrders(userId: string): Promise<Order[]> {
    try {
      const response = await databases.listDocuments(
        this.databaseId,
        this.ordersCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(50)
        ]
      );

      return response.documents.map(doc => this.parseOrder(doc));
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  }

  // Получить все заказы (для админа)
  async getAllOrders(limit: number = 50, offset: number = 0): Promise<Order[]> {
    try {
      const response = await databases.listDocuments(
        this.databaseId,
        this.ordersCollectionId,
        [
          Query.orderDesc('createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents.map(doc => this.parseOrder(doc));
    } catch (error) {
      console.error('Error fetching all orders:', error);
      throw error;
    }
  }

  // Получить статистику заказов
  async getOrderStats(): Promise<OrderStats> {
    try {
      const allOrders = await databases.listDocuments(
        this.databaseId,
        this.ordersCollectionId,
        [Query.limit(1000)] // Для простоты берем все заказы
      );

      const orders = allOrders.documents.map(doc => this.parseOrder(doc));
      
      const totalOrders = orders.length;
      const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
      const activeOrders = orders.filter(order => 
        ['pending', 'paid', 'processing'].includes(order.status)
      ).length;
      const completedOrders = orders.filter(order => order.status === 'completed').length;
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      return {
        totalOrders,
        totalRevenue,
        activeOrders,
        completedOrders,
        averageOrderValue
      };
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }
  }

  // Обновить статистику заказа
  async updateOrderStatistics(orderId: string, statistics: Order['statistics']): Promise<Order> {
    try {
      const document = await databases.updateDocument(
        this.databaseId,
        this.ordersCollectionId,
        orderId,
        {
          statistics: JSON.stringify(statistics),
          updatedAt: new Date().toISOString(),
        }
      );

      return this.parseOrder(document);
    } catch (error) {
      console.error('Error updating order statistics:', error);
      throw error;
    }
  }

  // Создать уведомление
  private async createNotification(userId: string, notification: {
    type: string;
    title: string;
    message: string;
    orderId?: string;
  }): Promise<void> {
    try {
      await databases.createDocument(
        this.databaseId,
        this.notificationsCollectionId,
        ID.unique(),
        {
          userId,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          orderId: notification.orderId || null,
          read: false,
          createdAt: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error('Error creating notification:', error);
      // Не бросаем ошибку, так как это не критично
    }
  }

  // Парсинг документа заказа
  private parseOrder(document: Record<string, unknown>): Order {
    return {
      id: document.$id,
      userId: document.userId,
      type: document.type,
      services: JSON.parse(document.services || '[]'),
      totalAmount: document.totalAmount,
      status: document.status,
      paymentMethod: document.paymentMethod,
      paymentId: document.paymentId,
      aiConversationId: document.aiConversationId,
      projectData: JSON.parse(document.projectData || '{}'),
      deliverables: JSON.parse(document.deliverables || '[]'),
      statistics: JSON.parse(document.statistics || '{}'),
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
      completedAt: document.completedAt ? new Date(document.completedAt) : undefined,
    };
  }

  // Получить сообщение для статуса
  private getStatusMessage(status: Order['status']): string {
    const messages = {
      pending: 'Заказ ожидает оплаты',
      paid: 'Заказ оплачен и принят в работу',
      processing: 'Заказ выполняется',
      completed: 'Заказ успешно выполнен',
      cancelled: 'Заказ отменен'
    };
    return messages[status] || 'Статус заказа изменен';
  }
}

export const orderService = new OrderService();
