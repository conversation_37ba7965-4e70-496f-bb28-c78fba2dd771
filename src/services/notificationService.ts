import { NotificationType, notificationHelpers } from '@/hooks/useNotifications';
import { AppwriteNotificationService } from '@/services/appwriteService';

// Интерфейс для событий системы
interface SystemEvent {
  type: 'order' | 'payment' | 'user' | 'system';
  action: string;
  data: Record<string, any>;
  userId?: string;
}

// Класс для управления уведомлениями
export class NotificationService {
  private static instance: NotificationService;
  private listeners: ((notification: any) => void)[] = [];

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Подписка на уведомления
  subscribe(callback: (notification: any) => void) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  // Отправка уведомления всем подписчикам
  private async notify(notification: any) {
    // Отправляем в Appwrite если есть userId
    if (notification.userId) {
      try {
        await AppwriteNotificationService.createNotification({
          userId: notification.userId,
          type: notification.type,
          category: this.getCategoryFromType(notification.type),
          title: notification.customTitle || this.getDefaultTitle(notification.type),
          message: notification.customMessage || this.getDefaultMessage(notification.type, notification.variables),
          actionUrl: notification.customActionUrl,
          actionText: notification.customActionText,
          relatedOrderId: notification.variables?.orderId,
          relatedUserId: notification.variables?.userId,
          priority: this.getPriorityFromType(notification.type),
          isImportant: this.isImportantType(notification.type),
          metadata: notification.variables || {},
        });
      } catch (error) {
        console.error('Error creating notification in Appwrite:', error);
      }
    }

    // Также уведомляем локальных слушателей
    this.listeners.forEach(listener => listener(notification));
  }

  // Вспомогательные методы для определения свойств уведомления
  private getCategoryFromType(type: string): string {
    if (type.includes('error') || type.includes('failed') || type.includes('cancelled')) return 'error';
    if (type.includes('warning') || type.includes('deadline') || type.includes('reminder')) return 'warning';
    if (type.includes('completed') || type.includes('received') || type.includes('confirmed')) return 'success';
    return 'info';
  }

  private getPriorityFromType(type: string): 'low' | 'normal' | 'high' | 'urgent' {
    if (type.includes('urgent') || type.includes('deadline')) return 'urgent';
    if (type.includes('important') || type.includes('failed') || type.includes('cancelled')) return 'high';
    if (type.includes('reminder') || type.includes('warning')) return 'normal';
    return 'low';
  }

  private isImportantType(type: string): boolean {
    return type.includes('completed') || type.includes('failed') || type.includes('cancelled') ||
           type.includes('confirmed') || type.includes('deadline');
  }

  private getDefaultTitle(type: string): string {
    const titles: Record<string, string> = {
      order_created: 'Заказ создан',
      order_confirmed: 'Заказ подтвержден',
      order_in_progress: 'Заказ в работе',
      order_completed: 'Заказ выполнен',
      order_cancelled: 'Заказ отменен',
      payment_received: 'Платеж получен',
      payment_failed: 'Ошибка платежа',
      system_update: 'Обновление системы',
      promo_discount: 'Специальное предложение',
    };
    return titles[type] || 'Уведомление';
  }

  private getDefaultMessage(type: string, variables: Record<string, any> = {}): string {
    const messages: Record<string, string> = {
      order_created: `Заказ "${variables.orderTitle || 'без названия'}" создан`,
      order_confirmed: `Заказ "${variables.orderTitle || 'без названия'}" подтвержден`,
      order_in_progress: `Работа над заказом "${variables.orderTitle || 'без названия'}" началась`,
      order_completed: `Заказ "${variables.orderTitle || 'без названия'}" выполнен`,
      order_cancelled: `Заказ "${variables.orderTitle || 'без названия'}" отменен`,
      payment_received: `Платеж ${variables.amount || '0'} получен`,
      payment_failed: `Ошибка при обработке платежа ${variables.amount || '0'}`,
    };
    return messages[type] || 'У вас новое уведомление';
  }

  // Обработка системных событий
  handleSystemEvent(event: SystemEvent) {
    const notifications = this.createNotificationsForEvent(event);
    notifications.forEach(notification => this.notify(notification));
  }

  // Создание уведомлений на основе событий
  private createNotificationsForEvent(event: SystemEvent): any[] {
    const notifications: any[] = [];

    switch (event.type) {
      case 'order':
        notifications.push(...this.handleOrderEvents(event));
        break;
      case 'payment':
        notifications.push(...this.handlePaymentEvents(event));
        break;
      case 'user':
        notifications.push(...this.handleUserEvents(event));
        break;
      case 'system':
        notifications.push(...this.handleSystemEvents(event));
        break;
    }

    return notifications;
  }

  // Обработка событий заказов
  private handleOrderEvents(event: SystemEvent): any[] {
    const { action, data } = event;
    const notifications: any[] = [];

    switch (action) {
      case 'created':
        notifications.push(notificationHelpers.orderCreated(data.orderTitle, data.orderId));
        // Уведомление администраторам
        notifications.push({
          type: 'order_created',
          variables: { orderTitle: data.orderTitle, orderId: data.orderId, customerName: data.customerName },
          customTitle: 'Новый заказ',
          customMessage: `Получен новый заказ "${data.orderTitle}" от ${data.customerName}`,
          userId: 'admin'
        });
        break;

      case 'confirmed':
        notifications.push({
          type: 'order_confirmed',
          variables: { 
            orderTitle: data.orderTitle, 
            orderId: data.orderId,
            startDate: data.startDate 
          }
        });
        break;

      case 'started':
        notifications.push({
          type: 'order_in_progress',
          variables: { 
            orderTitle: data.orderTitle, 
            orderId: data.orderId,
            progress: data.progress || 0
          }
        });
        break;

      case 'progress_updated':
        // Уведомление только при значительном прогрессе (каждые 25%)
        if (data.progress && data.progress % 25 === 0) {
          notifications.push({
            type: 'order_in_progress',
            variables: { 
              orderTitle: data.orderTitle, 
              orderId: data.orderId,
              progress: data.progress
            }
          });
        }
        break;

      case 'completed':
        notifications.push(notificationHelpers.orderCompleted(data.orderTitle, data.orderId));
        // Запрос на отзыв через 24 часа
        setTimeout(() => {
          this.notify({
            type: 'review_request',
            variables: { orderTitle: data.orderTitle, orderId: data.orderId }
          });
        }, 24 * 60 * 60 * 1000);
        break;

      case 'cancelled':
        notifications.push({
          type: 'order_cancelled',
          variables: { 
            orderTitle: data.orderTitle, 
            orderId: data.orderId,
            reason: data.reason || 'Причина не указана'
          }
        });
        break;

      case 'requires_info':
        notifications.push(notificationHelpers.orderRequiresInfo(data.orderTitle, data.orderId));
        break;

      case 'deadline_approaching':
        notifications.push({
          type: 'order_deadline_soon',
          variables: { 
            orderTitle: data.orderTitle, 
            orderId: data.orderId,
            daysLeft: data.daysLeft
          }
        });
        break;
    }

    return notifications;
  }

  // Обработка событий платежей
  private handlePaymentEvents(event: SystemEvent): any[] {
    const { action, data } = event;
    const notifications: any[] = [];

    switch (action) {
      case 'received':
        notifications.push(notificationHelpers.paymentReceived(data.amount, data.orderId));
        break;

      case 'failed':
        notifications.push(notificationHelpers.paymentFailed(data.amount, data.paymentId));
        break;

      case 'refunded':
        notifications.push({
          type: 'payment_refunded',
          variables: { amount: data.amount, orderId: data.orderId }
        });
        break;

      case 'reminder':
        notifications.push({
          type: 'payment_reminder',
          variables: { 
            orderTitle: data.orderTitle, 
            orderId: data.orderId,
            dueDate: data.dueDate
          }
        });
        break;
    }

    return notifications;
  }

  // Обработка событий пользователей
  private handleUserEvents(event: SystemEvent): any[] {
    const { action, data } = event;
    const notifications: any[] = [];

    switch (action) {
      case 'registered':
        notifications.push(notificationHelpers.welcome());
        // Напоминание о заполнении профиля через 1 час
        setTimeout(() => {
          this.notify(notificationHelpers.profileIncomplete());
        }, 60 * 60 * 1000);
        break;

      case 'profile_incomplete':
        notifications.push(notificationHelpers.profileIncomplete());
        break;

      case 'milestone_reached':
        notifications.push({
          type: 'milestone_reached',
          variables: { 
            milestone: data.milestone,
            details: data.details
          }
        });
        break;
    }

    return notifications;
  }

  // Обработка системных событий
  private handleSystemEvents(event: SystemEvent): any[] {
    const { action, data } = event;
    const notifications: any[] = [];

    switch (action) {
      case 'maintenance':
        notifications.push({
          type: 'system_maintenance',
          variables: { 
            date: data.date,
            startTime: data.startTime,
            endTime: data.endTime
          }
        });
        break;

      case 'update':
        notifications.push({
          type: 'system_update',
          variables: { features: data.features }
        });
        break;

      case 'announcement':
        notifications.push({
          type: 'system_announcement',
          variables: { 
            title: data.title,
            message: data.message,
            url: data.url
          }
        });
        break;

      case 'promo_discount':
        notifications.push(notificationHelpers.discount(
          data.discount,
          data.services,
          data.endDate,
          data.promoCode
        ));
        break;

      case 'new_service':
        notifications.push({
          type: 'promo_new_service',
          variables: { 
            serviceName: data.serviceName,
            description: data.description,
            serviceId: data.serviceId
          }
        });
        break;
    }

    return notifications;
  }

  // Автоматические уведомления
  setupAutomaticNotifications() {
    // Проверка дедлайнов каждый день в 9:00
    this.scheduleDaily('09:00', () => {
      this.checkOrderDeadlines();
    });

    // Напоминания об оплате каждый день в 10:00
    this.scheduleDaily('10:00', () => {
      this.checkPaymentReminders();
    });

    // Еженедельные отчеты по воскресеньям в 18:00
    this.scheduleWeekly(0, '18:00', () => {
      this.sendWeeklyReports();
    });
  }

  private scheduleDaily(time: string, callback: () => void) {
    const [hours, minutes] = time.split(':').map(Number);
    const now = new Date();
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    const timeUntilExecution = scheduledTime.getTime() - now.getTime();
    
    setTimeout(() => {
      callback();
      setInterval(callback, 24 * 60 * 60 * 1000); // Повторять каждый день
    }, timeUntilExecution);
  }

  private scheduleWeekly(dayOfWeek: number, time: string, callback: () => void) {
    const [hours, minutes] = time.split(':').map(Number);
    const now = new Date();
    const scheduledTime = new Date();
    
    scheduledTime.setHours(hours, minutes, 0, 0);
    scheduledTime.setDate(now.getDate() + (dayOfWeek - now.getDay() + 7) % 7);

    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 7);
    }

    const timeUntilExecution = scheduledTime.getTime() - now.getTime();
    
    setTimeout(() => {
      callback();
      setInterval(callback, 7 * 24 * 60 * 60 * 1000); // Повторять каждую неделю
    }, timeUntilExecution);
  }

  private checkOrderDeadlines() {
    // Здесь будет логика проверки дедлайнов заказов
    // В реальном приложении это будет API вызов
    console.log('Checking order deadlines...');
  }

  private checkPaymentReminders() {
    // Здесь будет логика проверки неоплаченных заказов
    console.log('Checking payment reminders...');
  }

  private sendWeeklyReports() {
    // Здесь будет логика отправки еженедельных отчетов
    console.log('Sending weekly reports...');
  }
}

// Экспорт синглтона
export const notificationService = NotificationService.getInstance();

// Хелперы для быстрого создания событий
export const createOrderEvent = (action: string, data: Record<string, any>, userId?: string): SystemEvent => ({
  type: 'order',
  action,
  data,
  userId
});

export const createPaymentEvent = (action: string, data: Record<string, any>, userId?: string): SystemEvent => ({
  type: 'payment',
  action,
  data,
  userId
});

export const createUserEvent = (action: string, data: Record<string, any>, userId?: string): SystemEvent => ({
  type: 'user',
  action,
  data,
  userId
});

export const createSystemEvent = (action: string, data: Record<string, any>): SystemEvent => ({
  type: 'system',
  action,
  data
});

// Инициализация автоматических уведомлений
if (typeof window !== 'undefined') {
  notificationService.setupAutomaticNotifications();
}
