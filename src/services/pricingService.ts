import { databases, DATABASE_ID } from '@/lib/appwrite';
import { Query } from 'appwrite';

export interface PricingTier {
  $id: string;
  tier_id: string;
  name: string;
  type: 'artist' | 'track';
  price: number;
  currency: string;
  duration: number;
  features: any[];
  popular: boolean;
  discount_percentage?: number;
  discount_valid_until?: string;
  limits: {
    socialAccounts: number;
    botActivations: number;
    emailCampaigns: number;
    telegramMembers: number;
    analyticsReports: number;
  };
  active: boolean;
  created_at: string;
  updated_at: string;
}

const COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PRICING_TIERS || 'pricing_tiers';

export class PricingService {
  // Получить все активные тарифы
  static async getAllTiers(): Promise<PricingTier[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID,
        [
          Query.equal('active', true),
          Query.orderAsc('price')
        ]
      );

      return response.documents.map(doc => ({
        ...doc,
        features: typeof doc.features === 'string' ? JSON.parse(doc.features) : doc.features,
        limits: typeof doc.limits === 'string' ? JSON.parse(doc.limits) : doc.limits
      })) as PricingTier[];
    } catch (error) {
      console.error('Error fetching pricing tiers:', error);
      throw error;
    }
  }

  // Получить тарифы по типу
  static async getTiersByType(type: 'artist' | 'track'): Promise<PricingTier[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID,
        [
          Query.equal('active', true),
          Query.equal('type', type),
          Query.orderAsc('price')
        ]
      );

      return response.documents.map(doc => ({
        ...doc,
        features: typeof doc.features === 'string' ? JSON.parse(doc.features) : doc.features,
        limits: typeof doc.limits === 'string' ? JSON.parse(doc.limits) : doc.limits
      })) as PricingTier[];
    } catch (error) {
      console.error('Error fetching pricing tiers by type:', error);
      throw error;
    }
  }

  // Получить тариф по ID
  static async getTierById(tierId: string): Promise<PricingTier | null> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID,
        [
          Query.equal('tier_id', tierId),
          Query.equal('active', true)
        ]
      );

      if (response.documents.length === 0) {
        return null;
      }

      const doc = response.documents[0];
      return {
        ...doc,
        features: typeof doc.features === 'string' ? JSON.parse(doc.features) : doc.features,
        limits: typeof doc.limits === 'string' ? JSON.parse(doc.limits) : doc.limits
      } as PricingTier;
    } catch (error) {
      console.error('Error fetching pricing tier by ID:', error);
      throw error;
    }
  }

  // Получить популярные тарифы
  static async getPopularTiers(): Promise<PricingTier[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID,
        [
          Query.equal('active', true),
          Query.equal('popular', true),
          Query.orderAsc('price')
        ]
      );

      return response.documents.map(doc => ({
        ...doc,
        features: typeof doc.features === 'string' ? JSON.parse(doc.features) : doc.features,
        limits: typeof doc.limits === 'string' ? JSON.parse(doc.limits) : doc.limits
      })) as PricingTier[];
    } catch (error) {
      console.error('Error fetching popular pricing tiers:', error);
      throw error;
    }
  }

  // Форматировать цену
  static formatPrice(price: number, currency: string = 'RUB'): string {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  }

  // Проверить активность скидки
  static isDiscountActive(tier: PricingTier): boolean {
    if (!tier.discount_percentage || !tier.discount_valid_until) {
      return false;
    }

    const now = new Date();
    const discountEnd = new Date(tier.discount_valid_until);
    return now <= discountEnd;
  }

  // Получить цену со скидкой
  static getDiscountedPrice(tier: PricingTier): number {
    if (!this.isDiscountActive(tier)) {
      return tier.price;
    }

    const discount = tier.discount_percentage! / 100;
    return Math.round(tier.price * (1 - discount));
  }

  // Получить экономию от скидки
  static getDiscountSavings(tier: PricingTier): number {
    if (!this.isDiscountActive(tier)) {
      return 0;
    }

    return tier.price - this.getDiscountedPrice(tier);
  }

  // Проверить лимиты тарифа
  static checkLimits(tier: PricingTier, usage: Partial<PricingTier['limits']>): {
    valid: boolean;
    exceeded: string[];
  } {
    const exceeded: string[] = [];

    Object.entries(usage).forEach(([key, value]) => {
      const limit = tier.limits[key as keyof PricingTier['limits']];
      if (limit !== -1 && value > limit) {
        exceeded.push(key);
      }
    });

    return {
      valid: exceeded.length === 0,
      exceeded
    };
  }

  // Получить рекомендуемый тариф
  static getRecommendedTier(tiers: PricingTier[], budget?: number): PricingTier | null {
    if (tiers.length === 0) return null;

    // Если указан бюджет, найти лучший тариф в рамках бюджета
    if (budget) {
      const affordableTiers = tiers.filter(tier => 
        this.getDiscountedPrice(tier) <= budget
      );
      
      if (affordableTiers.length > 0) {
        // Вернуть самый дорогой из доступных
        return affordableTiers[affordableTiers.length - 1];
      }
    }

    // Иначе вернуть популярный тариф
    const popularTier = tiers.find(tier => tier.popular);
    if (popularTier) return popularTier;

    // Или средний тариф
    const middleIndex = Math.floor(tiers.length / 2);
    return tiers[middleIndex];
  }

  // Сравнить тарифы
  static compareTiers(tier1: PricingTier, tier2: PricingTier): {
    priceDifference: number;
    featureDifference: number;
    recommendation: string;
  } {
    const price1 = this.getDiscountedPrice(tier1);
    const price2 = this.getDiscountedPrice(tier2);
    const priceDifference = price2 - price1;

    const features1 = tier1.features.filter(f => f.included).length;
    const features2 = tier2.features.filter(f => f.included).length;
    const featureDifference = features2 - features1;

    let recommendation = '';
    if (priceDifference > 0 && featureDifference > 0) {
      recommendation = `${tier2.name} дороже на ${this.formatPrice(priceDifference)}, но включает ${featureDifference} дополнительных функций`;
    } else if (priceDifference < 0) {
      recommendation = `${tier1.name} дешевле на ${this.formatPrice(Math.abs(priceDifference))}`;
    } else {
      recommendation = 'Тарифы имеют одинаковую стоимость';
    }

    return {
      priceDifference,
      featureDifference,
      recommendation
    };
  }
}
