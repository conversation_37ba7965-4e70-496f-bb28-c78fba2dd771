import { Client, Databases, Account, Storage, Query, ID } from 'appwrite';

// Safe environment variable access for SSR/SSG
const getEnvVar = (key: string): string | undefined => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }
  return undefined;
};

// Appwrite configuration with environment variable checks
const appwriteEndpoint = getEnvVar('NEXT_PUBLIC_APPWRITE_ENDPOINT');
const appwriteProjectId = getEnvVar('NEXT_PUBLIC_APPWRITE_PROJECT_ID');

if (!appwriteEndpoint) {
  console.warn('NEXT_PUBLIC_APPWRITE_ENDPOINT not found in environment variables');
}

if (!appwriteProjectId) {
  console.warn('NEXT_PUBLIC_APPWRITE_PROJECT_ID not found in environment variables');
}

const client = new Client();

// More robust validation for environment variables
const isValidEndpoint = appwriteEndpoint && appwriteEndpoint.trim() !== '' && appwriteEndpoint.startsWith('http');
const isValidProjectId = appwriteProjectId && appwriteProjectId.trim() !== '';

if (isValidEndpoint && isValidProjectId) {
  try {
    client
      .setEndpoint(appwriteEndpoint)
      .setProject(appwriteProjectId);
  } catch (error) {
    console.error('Failed to initialize Appwrite client:', error);
  }
}

export const databases = isValidEndpoint && isValidProjectId ? new Databases(client) : null;
export const account = isValidEndpoint && isValidProjectId ? new Account(client) : null;
export const storage = isValidEndpoint && isValidProjectId ? new Storage(client) : null;

// Database and Collection IDs
export const DATABASE_ID = getEnvVar('NEXT_PUBLIC_APPWRITE_DATABASE_ID') || '';
export const COLLECTIONS = {
  USERS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_USERS') || '',
  ARTISTS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_ARTISTS') || '',
  TRACKS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_TRACKS') || '',
  SUBSCRIPTIONS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_SUBSCRIPTIONS') || '',
  CAMPAIGNS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_CAMPAIGNS') || '',
  PARTNERS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_PARTNERS') || '',
  TEAM_MEMBERS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_TEAM_MEMBERS') || '',
  CASE_STUDIES: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_CASE_STUDIES') || '',
  SERVICES: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_SERVICES') || '',
  CONTACTS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACTS') || '',
  NOTIFICATIONS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_NOTIFICATIONS') || '',
  ORDERS: getEnvVar('NEXT_PUBLIC_APPWRITE_COLLECTION_ORDERS') || '',
};

// Storage Buckets
export const BUCKETS = {
  AVATARS: getEnvVar('NEXT_PUBLIC_APPWRITE_BUCKET_AVATARS') || '',
  TRACKS: getEnvVar('NEXT_PUBLIC_APPWRITE_BUCKET_TRACKS') || '',
  IMAGES: getEnvVar('NEXT_PUBLIC_APPWRITE_BUCKET_IMAGES') || '',
};

// Helper function to check if databases is available
const checkDatabases = () => {
  if (!databases) {
    console.warn('Appwrite databases not initialized');
    return false;
  }
  return true;
};

// Notification Service with fallbacks for missing environment variables
export class AppwriteNotificationService {
  // Helper method to check if service is available
  private static isAvailable(): boolean {
    return checkDatabases();
  }

  // Fallback method for when service is not available
  private static getEmptyResult() {
    return { documents: [], total: 0 };
  }
  // Create notification
  static async createNotification(notification: {
    userId: string;
    type: string;
    category: string;
    title: string;
    message: string;
    actionUrl?: string;
    actionText?: string;
    relatedOrderId?: string;
    relatedUserId?: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    isImportant?: boolean;
    metadata?: Record<string, unknown>;
  }) {
    try {
      if (!checkDatabases()) return null;

      const result = await databases!.createDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        ID.unique(),
        {
          notificationId: ID.unique(),
          userId: notification.userId,
          type: notification.type,
          category: notification.category,
          title: notification.title,
          message: notification.message,
          actionUrl: notification.actionUrl || null,
          actionText: notification.actionText || null,
          relatedOrderId: notification.relatedOrderId || null,
          relatedUserId: notification.relatedUserId || null,
          priority: notification.priority,
          isRead: false,
          isImportant: notification.isImportant || false,
          metadata: notification.metadata || {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Get user notifications
  static async getUserNotifications(userId: string, limit = 50) {
    try {
      if (!checkDatabases()) return [];

      const result = await databases!.listDocuments(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit)
        ]
      );
      return result.documents;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId: string) {
    try {
      if (!checkDatabases()) return null;

      const result = await databases!.updateDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId,
        {
          isRead: true,
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark notification as unread
  static async markAsUnread(notificationId: string) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const result = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId,
        {
          isRead: false,
          updatedAt: new Date().toISOString(),
        }
      );
      return result;
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      throw error;
    }
  }

  // Delete notification
  static async deleteNotification(notificationId: string) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        notificationId
      );
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Mark all notifications as read for user
  static async markAllAsRead(userId: string) {
    try {
      const notifications = await this.getUserNotifications(userId, 100);
      const unreadNotifications = notifications.filter(n => !n.isRead);
      
      const promises = unreadNotifications.map(notification =>
        this.markAsRead(notification.$id)
      );
      
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Clear all notifications for user
  static async clearAllNotifications(userId: string) {
    try {
      const notifications = await this.getUserNotifications(userId, 100);
      
      const promises = notifications.map(notification =>
        this.deleteNotification(notification.$id)
      );
      
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      throw error;
    }
  }

  // Get unread count
  static async getUnreadCount(userId: string) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const result = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.NOTIFICATIONS,
        [
          Query.equal('userId', userId),
          Query.equal('isRead', false),
          Query.limit(100)
        ]
      );
      return result.total;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }
}

// Order Service
export class AppwriteOrderService {
  // Create order
  static async createOrder(order: {
    userId: string;
    serviceType: 'artist' | 'track';
    packageType: string;
    title: string;
    description: string;
    price: number;
    currency: string;
    requirements?: Record<string, any>;
    metadata?: Record<string, any>;
  }) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const orderId = ID.unique();
      const result = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        ID.unique(),
        {
          orderId: orderId,
          userId: order.userId,
          serviceType: order.serviceType,
          packageType: order.packageType,
          title: order.title,
          description: order.description,
          price: order.price,
          currency: order.currency,
          status: 'pending',
          paymentStatus: 'pending',
          progress: 0,
          requirements: order.requirements || {},
          metadata: order.metadata || {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          estimatedCompletionDate: null,
          actualCompletionDate: null,
          assignedManagerId: null,
          notes: '',
          files: [],
          deliverables: [],
        }
      );

      // Create notification for user
      await AppwriteNotificationService.createNotification({
        userId: order.userId,
        type: 'order_created',
        category: 'success',
        title: 'Заказ создан',
        message: `Ваш заказ "${order.title}" успешно создан и ожидает обработки`,
        actionUrl: `/dashboard/orders/${orderId}`,
        actionText: 'Посмотреть заказ',
        relatedOrderId: orderId,
        priority: 'normal',
        isImportant: true,
      });

      return result;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Get user orders
  static async getUserOrders(userId: string, limit = 50) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const result = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit)
        ]
      );
      return result.documents;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  // Update order status
  static async updateOrderStatus(orderId: string, status: string, progress?: number) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const updateData: any = {
        status: status,
        updatedAt: new Date().toISOString(),
      };

      if (progress !== undefined) {
        updateData.progress = progress;
      }

      if (status === 'completed') {
        updateData.actualCompletionDate = new Date().toISOString();
      }

      const result = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        orderId,
        updateData
      );

      // Get order details for notification
      const order = await databases.getDocument(DATABASE_ID, COLLECTIONS.ORDERS, orderId);

      // Create notification based on status
      let notificationData: any = {
        userId: order.userId,
        relatedOrderId: orderId,
        priority: 'normal' as const,
      };

      switch (status) {
        case 'confirmed':
          notificationData = {
            ...notificationData,
            type: 'order_confirmed',
            category: 'success',
            title: 'Заказ подтвержден',
            message: `Ваш заказ "${order.title}" подтвержден и скоро будет запущен`,
            actionUrl: `/dashboard/orders/${orderId}`,
            actionText: 'Посмотреть заказ',
            isImportant: true,
          };
          break;
        case 'in_progress':
          notificationData = {
            ...notificationData,
            type: 'order_in_progress',
            category: 'info',
            title: 'Заказ в работе',
            message: `Работа над заказом "${order.title}" началась. Прогресс: ${progress || 0}%`,
            actionUrl: `/dashboard/orders/${orderId}`,
            actionText: 'Посмотреть прогресс',
          };
          break;
        case 'completed':
          notificationData = {
            ...notificationData,
            type: 'order_completed',
            category: 'success',
            title: 'Заказ выполнен',
            message: `Ваш заказ "${order.title}" успешно выполнен!`,
            actionUrl: `/dashboard/orders/${orderId}`,
            actionText: 'Посмотреть результат',
            isImportant: true,
          };
          break;
        case 'cancelled':
          notificationData = {
            ...notificationData,
            type: 'order_cancelled',
            category: 'error',
            title: 'Заказ отменен',
            message: `Заказ "${order.title}" был отменен`,
            actionUrl: `/dashboard/orders/${orderId}`,
            actionText: 'Подробности',
            isImportant: true,
          };
          break;
      }

      if (notificationData.type) {
        await AppwriteNotificationService.createNotification(notificationData);
      }

      return result;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Get order by ID
  static async getOrder(orderId: string) {
    try {
      if (!databases) {
        throw new Error('Appwrite databases not initialized');
      }

      const result = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        orderId
      );
      return result;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }
}

export { client };
