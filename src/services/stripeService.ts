import { loadStripe, Stripe } from '@stripe/stripe-js';

// Инициализация Stripe
let stripePromise: Promise<Stripe | null>;

const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    if (!publishableKey) {
      console.warn('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY not found in environment variables');
      return Promise.resolve(null);
    }
    stripePromise = loadStripe(publishableKey);
  }
  return stripePromise;
};

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  clientSecret: string;
}

export interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
}

export interface CreatePaymentIntentParams {
  amount: number;
  currency?: string;
  orderId: string;
  userId: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface ConfirmPaymentParams {
  clientSecret: string;
  paymentMethodId?: string;
  returnUrl?: string;
}

class StripeService {
  private stripe: Stripe | null = null;

  async initialize() {
    if (!this.stripe) {
      this.stripe = await getStripe();
    }
    return this.stripe;
  }

  // Создание платежного намерения
  async createPaymentIntent(params: CreatePaymentIntentParams): Promise<PaymentIntent> {
    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  // Подтверждение платежа
  async confirmPayment(params: ConfirmPaymentParams) {
    const stripe = await this.initialize();
    if (!stripe) throw new Error('Stripe not initialized');

    try {
      const result = await stripe.confirmCardPayment(params.clientSecret, {
        payment_method: params.paymentMethodId,
        return_url: params.returnUrl,
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      return result.paymentIntent;
    } catch (error) {
      console.error('Error confirming payment:', error);
      throw error;
    }
  }

  // Создание способа оплаты
  async createPaymentMethod(cardElement: any) {
    const stripe = await this.initialize();
    if (!stripe) throw new Error('Stripe not initialized');

    try {
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (error) {
        throw new Error(error.message);
      }

      return paymentMethod;
    } catch (error) {
      console.error('Error creating payment method:', error);
      throw error;
    }
  }

  // Получение сохраненных способов оплаты
  async getPaymentMethods(customerId: string): Promise<PaymentMethod[]> {
    try {
      const response = await fetch(`/api/payments/methods?customerId=${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw error;
    }
  }

  // Сохранение способа оплаты
  async savePaymentMethod(paymentMethodId: string, customerId: string) {
    try {
      const response = await fetch('/api/payments/save-method', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethodId,
          customerId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save payment method');
      }

      return await response.json();
    } catch (error) {
      console.error('Error saving payment method:', error);
      throw error;
    }
  }

  // Создание подписки
  async createSubscription(customerId: string, priceId: string) {
    try {
      const response = await fetch('/api/payments/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId,
          priceId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create subscription');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  // Отмена подписки
  async cancelSubscription(subscriptionId: string) {
    try {
      const response = await fetch('/api/payments/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      return await response.json();
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw error;
    }
  }

  // Получение истории платежей
  async getPaymentHistory(customerId: string) {
    try {
      const response = await fetch(`/api/payments/history?customerId=${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch payment history');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }

  // Возврат платежа
  async refundPayment(paymentIntentId: string, amount?: number) {
    try {
      const response = await fetch('/api/payments/refund', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId,
          amount,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process refund');
      }

      return await response.json();
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  // Форматирование суммы для отображения
  formatAmount(amount: number, currency: string = 'rub'): string {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount / 100);
  }

  // Валидация карты
  validateCard(cardNumber: string, expiryDate: string, cvc: string): boolean {
    // Простая валидация номера карты (Luhn algorithm)
    const luhnCheck = (num: string) => {
      let sum = 0;
      let isEven = false;
      for (let i = num.length - 1; i >= 0; i--) {
        let digit = parseInt(num.charAt(i), 10);
        if (isEven) {
          digit *= 2;
          if (digit > 9) {
            digit -= 9;
          }
        }
        sum += digit;
        isEven = !isEven;
      }
      return sum % 10 === 0;
    };

    const cleanCardNumber = cardNumber.replace(/\s/g, '');
    const [month, year] = expiryDate.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    // Валидация номера карты
    if (!/^\d{13,19}$/.test(cleanCardNumber) || !luhnCheck(cleanCardNumber)) {
      return false;
    }

    // Валидация срока действия
    if (!/^\d{2}\/\d{2}$/.test(expiryDate)) {
      return false;
    }

    const expMonth = parseInt(month, 10);
    const expYear = parseInt(year, 10);

    if (expMonth < 1 || expMonth > 12) {
      return false;
    }

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      return false;
    }

    // Валидация CVC
    if (!/^\d{3,4}$/.test(cvc)) {
      return false;
    }

    return true;
  }
}

export const stripeService = new StripeService();
