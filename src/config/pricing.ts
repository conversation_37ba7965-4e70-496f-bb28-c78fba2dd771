import { PricingTier } from '@/types/pricing';

export const PRICING_TIERS: PricingTier[] = [
  // Тарифы для артистов
  {
    id: 'artist-starter',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'artist',
    price: 25000,
    currency: 'RUB',
    duration: 30,
    features: [
      { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', included: true, limit: 3 },
      { id: 'basic-bots', name: 'Базовые боты', description: 'Автоматизация лайков и комментариев', included: true, limit: 2 },
      { id: 'email-campaigns', name: '<PERSON><PERSON> рассылки', description: 'Рассылки для фанатов', included: true, limit: 5 },
      { id: 'telegram-growth', name: 'Рост Telegram', description: 'Привлечение подписчиков в Telegram', included: true, limit: 1000 },
      { id: 'analytics', name: 'Баз<PERSON>ая аналитика', description: 'Отчеты по продвижению', included: true },
      { id: 'support', name: 'Поддержка', description: 'Email поддержка', included: true }
    ],
    limits: {
      socialAccounts: 3,
      botActivations: 2,
      emailCampaigns: 5,
      telegramMembers: 1000,
      analyticsReports: 10
    }
  },
  {
    id: 'artist-pro',
    name: 'Профи',
    type: 'artist',
    price: 50000,
    currency: 'RUB',
    duration: 30,
    popular: true,
    features: [
      { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', included: true, limit: 8 },
      { id: 'advanced-bots', name: 'Продвинутые боты', description: 'ИИ боты для всех платформ', included: true, limit: 6 },
      { id: 'email-campaigns', name: 'Email рассылки', description: 'Неограниченные рассылки', included: true },
      { id: 'telegram-growth', name: 'Рост Telegram', description: 'Массовое привлечение подписчиков', included: true, limit: 5000 },
      { id: 'content-creation', name: 'Создание контента', description: 'ИИ генерация постов и сторис', included: true },
      { id: 'influencer-outreach', name: 'Работа с блогерами', description: 'Поиск и контакт с инфлюенсерами', included: true },
      { id: 'advanced-analytics', name: 'Продвинутая аналитика', description: 'Детальные отчеты и прогнозы', included: true },
      { id: 'priority-support', name: 'Приоритетная поддержка', description: 'Чат поддержка 24/7', included: true }
    ],
    limits: {
      socialAccounts: 8,
      botActivations: 6,
      emailCampaigns: -1, // неограничено
      telegramMembers: 5000,
      analyticsReports: -1
    }
  },
  {
    id: 'artist-enterprise',
    name: 'Энтерпрайз',
    type: 'artist',
    price: 100000,
    currency: 'RUB',
    duration: 30,
    features: [
      { id: 'unlimited-accounts', name: 'Неограниченные аккаунты', description: 'Без лимитов на количество', included: true },
      { id: 'ai-bots', name: 'ИИ боты премиум', description: 'Самые умные боты с GPT-4', included: true },
      { id: 'viral-strategies', name: 'Вирусные стратегии', description: 'Создание вирусного контента', included: true },
      { id: 'mass-outreach', name: 'Массовый охват', description: 'Скрапинг и массовые рассылки', included: true },
      { id: 'telegram-empire', name: 'Telegram империя', description: 'Создание сети каналов', included: true },
      { id: 'personal-manager', name: 'Персональный менеджер', description: 'Выделенный специалист', included: true },
      { id: 'custom-bots', name: 'Кастомные боты', description: 'Разработка под ваши задачи', included: true },
      { id: 'white-label', name: 'White Label', description: 'Брендинг под ваш лейбл', included: true }
    ],
    limits: {
      socialAccounts: -1,
      botActivations: -1,
      emailCampaigns: -1,
      telegramMembers: -1,
      analyticsReports: -1
    }
  },

  // Тарифы для треков
  {
    id: 'track-basic',
    name: 'Базовый',
    type: 'track',
    price: 15000,
    currency: 'RUB',
    duration: 14,
    features: [
      { id: 'playlist-placement', name: 'Размещение в плейлистах', description: 'Spotify, Apple Music, Яндекс', included: true, limit: 10 },
      { id: 'social-promotion', name: 'Продвижение в соцсетях', description: 'Instagram, TikTok, VK', included: true },
      { id: 'basic-bots', name: 'Базовые боты', description: 'Автолайки и репосты', included: true, limit: 2 },
      { id: 'email-blast', name: 'Email рассылка', description: 'Уведомление о релизе', included: true, limit: 1 },
      { id: 'telegram-post', name: 'Посты в Telegram', description: 'Размещение в музыкальных каналах', included: true, limit: 5 }
    ],
    limits: {
      socialAccounts: 2,
      botActivations: 2,
      emailCampaigns: 1,
      telegramMembers: 500,
      analyticsReports: 5
    }
  },
  {
    id: 'track-viral',
    name: 'Вирусный',
    type: 'track',
    price: 35000,
    currency: 'RUB',
    duration: 21,
    popular: true,
    features: [
      { id: 'viral-campaign', name: 'Вирусная кампания', description: 'Создание трендового контента', included: true },
      { id: 'influencer-network', name: 'Сеть инфлюенсеров', description: 'Охват через блогеров', included: true, limit: 20 },
      { id: 'ai-content', name: 'ИИ контент', description: 'Генерация мемов и видео', included: true },
      { id: 'mass-engagement', name: 'Массовое вовлечение', description: 'Боты для всех платформ', included: true },
      { id: 'telegram-network', name: 'Telegram сеть', description: 'Размещение в 50+ каналах', included: true },
      { id: 'radio-promotion', name: 'Радио продвижение', description: 'Размещение на радиостанциях', included: true },
      { id: 'press-release', name: 'Пресс-релиз', description: 'Публикация в музыкальных СМИ', included: true }
    ],
    limits: {
      socialAccounts: 5,
      botActivations: 8,
      emailCampaigns: 3,
      telegramMembers: 2000,
      analyticsReports: 15
    }
  },
  {
    id: 'track-platinum',
    name: 'Платиновый',
    type: 'track',
    price: 75000,
    currency: 'RUB',
    duration: 30,
    features: [
      { id: 'platinum-campaign', name: 'Платиновая кампания', description: 'Максимальный охват', included: true },
      { id: 'celebrity-network', name: 'Сеть знаменитостей', description: 'Продвижение через звезд', included: true },
      { id: 'ai-army', name: 'Армия ИИ ботов', description: 'Сотни умных ботов', included: true },
      { id: 'global-outreach', name: 'Глобальный охват', description: 'Международное продвижение', included: true },
      { id: 'telegram-empire', name: 'Telegram империя', description: 'Сеть из 100+ каналов', included: true },
      { id: 'media-blitz', name: 'Медиа блиц', description: 'Освещение во всех СМИ', included: true },
      { id: 'chart-push', name: 'Продвижение в чарты', description: 'Попадание в топы стриминговых сервисов', included: true },
      { id: 'dedicated-team', name: 'Выделенная команда', description: 'Команда из 5+ специалистов', included: true }
    ],
    limits: {
      socialAccounts: -1,
      botActivations: -1,
      emailCampaigns: -1,
      telegramMembers: -1,
      analyticsReports: -1
    }
  }
];

// Конфигурация ботов для разных платформ
export const BOT_TEMPLATES = {
  instagram: {
    engagement: {
      name: 'Instagram Engagement Bot',
      capabilities: ['like', 'comment', 'follow', 'story'],
      smartFeatures: ['hashtag_analysis', 'competitor_tracking', 'optimal_timing'],
      antiDetection: ['human_delays', 'random_patterns', 'proxy_rotation']
    },
    content: {
      name: 'Instagram Content Bot',
      capabilities: ['post', 'story', 'reels'],
      smartFeatures: ['ai_captions', 'trend_analysis', 'visual_optimization'],
      antiDetection: ['content_variation', 'posting_schedule', 'engagement_simulation']
    },
    outreach: {
      name: 'Instagram Outreach Bot',
      capabilities: ['dm', 'comment_outreach', 'influencer_contact'],
      smartFeatures: ['lead_scoring', 'personalization', 'follow_up_sequences'],
      antiDetection: ['conversation_ai', 'response_delays', 'natural_language']
    }
  },
  telegram: {
    growth: {
      name: 'Telegram Growth Bot',
      capabilities: ['member_invite', 'content_sharing', 'engagement'],
      smartFeatures: ['target_scraping', 'quality_filtering', 'retention_optimization'],
      antiDetection: ['gradual_growth', 'organic_patterns', 'activity_simulation']
    },
    content: {
      name: 'Telegram Content Bot',
      capabilities: ['auto_post', 'cross_post', 'content_curation'],
      smartFeatures: ['trending_topics', 'audience_analysis', 'optimal_timing'],
      antiDetection: ['human_like_posting', 'content_variation', 'engagement_tracking']
    }
  },
  youtube: {
    promotion: {
      name: 'YouTube Promotion Bot',
      capabilities: ['comment', 'like', 'subscribe', 'share'],
      smartFeatures: ['video_analysis', 'keyword_optimization', 'competitor_research'],
      antiDetection: ['watch_time_simulation', 'natural_engagement', 'device_rotation']
    }
  },
  tiktok: {
    viral: {
      name: 'TikTok Viral Bot',
      capabilities: ['like', 'comment', 'share', 'follow'],
      smartFeatures: ['trend_detection', 'hashtag_optimization', 'viral_prediction'],
      antiDetection: ['human_behavior', 'device_fingerprinting', 'session_management']
    }
  },
  email: {
    scraper: {
      name: 'Email Scraper Bot',
      capabilities: ['email_extraction', 'validation', 'list_building'],
      smartFeatures: ['genre_targeting', 'quality_scoring', 'gdpr_compliance'],
      antiDetection: ['rate_limiting', 'proxy_usage', 'captcha_solving']
    }
  }
};

export const PROMOTION_STRATEGIES = {
  viral_launch: {
    name: 'Вирусный запуск',
    duration: 7,
    phases: [
      { name: 'Подготовка', duration: 1, actions: ['content_creation', 'influencer_outreach'] },
      { name: 'Запуск', duration: 3, actions: ['mass_posting', 'bot_activation', 'paid_promotion'] },
      { name: 'Усиление', duration: 2, actions: ['trend_riding', 'community_engagement'] },
      { name: 'Закрепление', duration: 1, actions: ['analytics', 'optimization'] }
    ]
  },
  organic_growth: {
    name: 'Органический рост',
    duration: 30,
    phases: [
      { name: 'Исследование', duration: 3, actions: ['audience_analysis', 'competitor_research'] },
      { name: 'Контент-план', duration: 5, actions: ['content_strategy', 'calendar_creation'] },
      { name: 'Реализация', duration: 20, actions: ['daily_posting', 'engagement', 'community_building'] },
      { name: 'Оптимизация', duration: 2, actions: ['performance_analysis', 'strategy_adjustment'] }
    ]
  }
};
