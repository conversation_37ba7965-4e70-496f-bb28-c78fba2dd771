'use client';

import { useState } from 'react';
import { account, databases, DATABASE_ID } from '@/lib/appwrite';
import { CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';

export default function TestAppwritePage() {
  const [tests, setTests] = useState<Record<string, 'pending' | 'success' | 'error' | 'loading'>>({
    connection: 'pending',
    account: 'pending',
    database: 'pending',
    registration: 'pending'
  });
  const [results, setResults] = useState<Record<string, string>>({});

  const updateTest = (test: string, status: 'pending' | 'success' | 'error' | 'loading', message?: string) => {
    setTests(prev => ({ ...prev, [test]: status }));
    if (message) {
      setResults(prev => ({ ...prev, [test]: message }));
    }
  };

  const testConnection = async () => {
    updateTest('connection', 'loading');
    try {
      const health = await fetch(`${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/health`);
      if (health.ok) {
        updateTest('connection', 'success', 'Подключение к Appwrite успешно');
      } else {
        updateTest('connection', 'error', 'Ошибка подключения к Appwrite');
      }
    } catch (error) {
      updateTest('connection', 'error', `Ошибка: ${error}`);
    }
  };

  const testAccount = async () => {
    updateTest('account', 'loading');
    try {
      await account.get();
      updateTest('account', 'success', 'Аккаунт сервис работает');
    } catch (error: any) {
      if (error.code === 401) {
        updateTest('account', 'success', 'Аккаунт сервис работает (не авторизован)');
      } else {
        updateTest('account', 'error', `Ошибка аккаунт сервиса: ${error.message}`);
      }
    }
  };

  const testDatabase = async () => {
    updateTest('database', 'loading');
    try {
      await databases.listDocuments(DATABASE_ID, process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS!);
      updateTest('database', 'success', 'База данных доступна');
    } catch (error: any) {
      updateTest('database', 'error', `Ошибка базы данных: ${error.message}`);
    }
  };

  const testRegistration = async () => {
    updateTest('registration', 'loading');
    try {
      const testEmail = `test-${Date.now()}@example.com`;
      const testPassword = 'testpassword123';
      
      // Пытаемся создать тестового пользователя
      const user = await account.create('unique()', testEmail, testPassword, 'Test User');
      
      // Сразу удаляем сессию
      try {
        await account.deleteSession('current');
      } catch (e) {
        // Игнорируем ошибку удаления сессии
      }
      
      updateTest('registration', 'success', `Регистрация работает. Создан пользователь: ${user.email}`);
    } catch (error: any) {
      updateTest('registration', 'error', `Ошибка регистрации: ${error.message}`);
    }
  };

  const runAllTests = async () => {
    await testConnection();
    await testAccount();
    await testDatabase();
    await testRegistration();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-black" style={{ padding: '30px' }}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px' }}>
            🔧 Тестирование Appwrite
          </h1>
          <p className="text-gray-400">
            Проверка подключения и функциональности Appwrite сервисов
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800/50" style={{ padding: '30px', marginBottom: '30px' }}>
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button
              onClick={runAllTests}
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-semibold flex items-center"
              style={{ padding: '12px 24px', gap: '8px' }}
            >
              <RefreshCw className="w-5 h-5" />
              Запустить все тесты
            </button>
            
            <button
              onClick={testConnection}
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 20px' }}
            >
              Тест подключения
            </button>
            
            <button
              onClick={testAccount}
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 20px' }}
            >
              Тест аккаунта
            </button>
            
            <button
              onClick={testDatabase}
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 20px' }}
            >
              Тест БД
            </button>
            
            <button
              onClick={testRegistration}
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 20px' }}
            >
              Тест регистрации
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
          {Object.entries(tests).map(([testName, status]) => (
            <div
              key={testName}
              className="bg-gray-900/50 rounded-xl border border-gray-800/50"
              style={{ padding: '20px' }}
            >
              <div className="flex items-center" style={{ gap: '12px', marginBottom: '10px' }}>
                {getStatusIcon(status)}
                <h3 className="text-white font-semibold capitalize">
                  {testName === 'connection' && 'Подключение'}
                  {testName === 'account' && 'Аккаунт сервис'}
                  {testName === 'database' && 'База данных'}
                  {testName === 'registration' && 'Регистрация'}
                </h3>
              </div>
              
              {results[testName] && (
                <p className={`text-sm ${
                  status === 'success' ? 'text-green-400' : 
                  status === 'error' ? 'text-red-400' : 'text-gray-400'
                }`}>
                  {results[testName]}
                </p>
              )}
            </div>
          ))}
        </div>

        {/* Environment Info */}
        <div className="bg-gray-900/50 rounded-xl border border-gray-800/50" style={{ padding: '20px', marginTop: '30px' }}>
          <h3 className="text-white font-semibold" style={{ marginBottom: '15px' }}>
            Конфигурация окружения:
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '10px' }}>
            <div>
              <span className="text-gray-400 text-sm">Endpoint:</span>
              <p className="text-white text-sm font-mono break-all">
                {process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}
              </p>
            </div>
            
            <div>
              <span className="text-gray-400 text-sm">Project ID:</span>
              <p className="text-white text-sm font-mono">
                {process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}
              </p>
            </div>
            
            <div>
              <span className="text-gray-400 text-sm">Database ID:</span>
              <p className="text-white text-sm font-mono">
                {process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID}
              </p>
            </div>
            
            <div>
              <span className="text-gray-400 text-sm">Site URL:</span>
              <p className="text-white text-sm font-mono">
                {process.env.NEXT_PUBLIC_SITE_URL}
              </p>
            </div>
          </div>
        </div>

        {/* Back Link */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <a
            href="/test-auth"
            className="text-gray-400 hover:text-white transition-colors text-sm"
          >
            ← Вернуться к тестам авторизации
          </a>
        </div>
      </div>
    </div>
  );
}
