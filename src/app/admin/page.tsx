'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRealtimeOrders } from '@/hooks/useRealtimeOrders';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import Link from 'next/link';
import OrderProcessingPage from '@/components/OrderProcessingPage';
import TelegramBot from '@/components/TelegramBot';
import EmailBot from '@/components/EmailBot';
import { AdminUserInfo } from '@/components/AdminRoute';
import AdminNotificationCenter from '@/components/AdminNotificationCenter';
import {
  Users,
  Music,
  BarChart3,
  Settings,
  Bell,
  CheckCircle,
  Clock,
  AlertCircle,
  Play,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  Shield,
  ArrowRight,
  Bot,
  Mail,
  Search
} from 'lucide-react';

interface Order {
  id: string;
  type: 'artist' | 'track';
  clientName: string;
  clientEmail: string;
  serviceName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: string;
  price: number;
  trackInfo?: {
    title: string;
    artist: string;
    genre: string;
    duration: string;
  };
  socialAccounts?: {
    instagram?: string;
    vk?: string;
    youtube?: string;
    telegram?: string;
  };
  assignedTo?: string;
  priority: 'low' | 'medium' | 'high';
  progress: number;
}

const AdminDashboard = () => {
  const { user, loading } = useAuth();
  const { isAdmin, canAccessAdminPanel } = useAdminAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [showOrderProcessing, setShowOrderProcessing] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [mounted, setMounted] = useState(false);

  // Предотвращаем ошибку гидратации
  useEffect(() => {
    setMounted(true);
  }, []);

  // Реал-тайм заказы для админа (все заказы)
  const {
    orders: allOrders
  } = useRealtimeOrders({
    status: statusFilter
  });

  // Предотвращаем ошибку гидратации - не рендерим до монтирования
  if (!mounted) {
    return null;
  }

  // Показываем загрузку во время проверки авторизации
  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Загрузка...</p>
        </div>
      </div>
    );
  }

  // Проверки авторизации теперь выполняются в AdminRoute (layout.tsx)

  // Используем реальные заказы из Appwrite
  const orders = allOrders.map(order => ({
    id: order.id || '',
    type: order.type,
    clientName: order.projectData?.title || 'Неизвестный клиент',
    clientEmail: order.userId || '',
    serviceName: order.services[0]?.title || 'Неизвестная услуга',
    status: order.status,
    amount: order.totalAmount,
    createdAt: order.createdAt.toISOString(),
    description: order.projectData?.description || '',
    priority: 'medium' as const,
    progress: Math.floor(Math.random() * 100),
    platforms: {
      spotify: order.services.some(s => s.platform.includes('Spotify')) ? 'active' : undefined,
      youtube: order.services.some(s => s.platform.includes('YouTube')) ? 'active' : undefined,
      instagram: order.services.some(s => s.platform.includes('Instagram')) ? 'active' : undefined
    }
  }));

  // Fallback mock data если нет реальных заказов
  const mockOrders: Order[] = [
    {
      id: 'ORD-001',
      type: 'track',
      clientName: 'MORGENSHTERN',
      clientEmail: '<EMAIL>',
      serviceName: 'Базовый пакет продвижения трека',
      status: 'in_progress',
      createdAt: '2024-01-15T10:30:00Z',
      price: 25000,
      trackInfo: {
        title: 'Новый хит',
        artist: 'MORGENSHTERN',
        genre: 'Hip-Hop',
        duration: '3:45'
      },
      socialAccounts: {
        instagram: '@morgenshtern',
        vk: 'morgenshtern_official',
        youtube: 'MORGENSHTERN',
        telegram: '@morgenshtern_official'
      },
      assignedTo: 'Анна Петрова',
      priority: 'high',
      progress: 65
    },
    {
      id: 'ORD-002',
      type: 'artist',
      clientName: 'FACE',
      clientEmail: '<EMAIL>',
      serviceName: 'Продвижение артиста - Премиум',
      status: 'pending',
      createdAt: '2024-01-14T15:20:00Z',
      price: 50000,
      socialAccounts: {
        instagram: '@faceonline',
        vk: 'face_official'
      },
      priority: 'medium',
      progress: 0
    },
    {
      id: 'ORD-003',
      type: 'track',
      clientName: 'Элджей',
      clientEmail: '<EMAIL>',
      serviceName: 'Продвижение на радио',
      status: 'completed',
      createdAt: '2024-01-10T09:15:00Z',
      price: 15000,
      trackInfo: {
        title: 'Минимал',
        artist: 'Элджей',
        genre: 'Pop',
        duration: '2:58'
      },
      assignedTo: 'Михаил Иванов',
      priority: 'low',
      progress: 100
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400';
      case 'in_progress': return 'bg-blue-500/20 text-blue-400';
      case 'completed': return 'bg-green-500/20 text-green-400';
      case 'cancelled': return 'bg-pink-500/20 text-pink-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'in_progress': return <Play className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-pink-500/20 text-pink-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-green-500/20 text-green-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  // Используем реальные заказы, если есть, иначе mock данные
  const finalOrders = orders.length > 0 ? orders : mockOrders;

  const filteredOrders = finalOrders.filter(order => {
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesSearch = order.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const stats = [
    { title: 'Всего заказов', value: '156', change: '+12%', icon: <BarChart3 className="w-6 h-6" /> },
    { title: 'В работе', value: '23', change: '+5%', icon: <Play className="w-6 h-6" /> },
    { title: 'Выполнено', value: '128', change: '+18%', icon: <CheckCircle className="w-6 h-6" /> },
    { title: 'Доход', value: '2.5M ₽', change: '+25%', icon: <TrendingUp className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <div className="bg-gray-900/80 backdrop-blur-xl border-b border-gray-800/50" style={{ padding: '20px 30px' }}>
        <div className="flex items-center justify-between">
          <div>
            <Link
              href="/"
              className="block hover:opacity-80 transition-opacity cursor-pointer"
              title="Перейти на главную страницу"
            >
              <h1 className="text-2xl font-bold text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text" style={{ marginBottom: '5px', lineHeight: '1.4' }}>
                Админ панель H!VE Agency
              </h1>
            </Link>
            <p className="text-gray-400" style={{ lineHeight: '1.6' }}>Управление заказами и клиентами</p>
          </div>
          <div className="flex items-center" style={{ gap: '20px' }}>
            <AdminNotificationCenter />
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/30">
              <span className="text-white font-semibold text-sm">A</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-900/50 border-b border-gray-800/50" style={{ padding: '20px 30px' }}>
        <div className="flex" style={{ gap: '30px' }}>
          {[
            { id: 'orders', title: 'Заказы', icon: <Music className="w-5 h-5" /> },
            { id: 'clients', title: 'Клиенты', icon: <Users className="w-5 h-5" /> },
            { id: 'telegram-bot', title: 'Telegram Bot', icon: <Bot className="w-5 h-5" /> },
            { id: 'email-bot', title: 'Email Bot', icon: <Mail className="w-5 h-5" /> },
            { id: 'analytics', title: 'Аналитика', icon: <BarChart3 className="w-5 h-5" /> },
            { id: 'settings', title: 'Настройки', icon: <Settings className="w-5 h-5" /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/30'
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
              }`}
              style={{ gap: '10px', padding: '12px 20px', lineHeight: '1.4' }}
            >
              {tab.icon}
              <span>{tab.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '30px' }}>
        {activeTab === 'orders' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4" style={{ gap: '25px' }}>
              {stats.map((stat, index) => (
                <div key={index} className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                    <div className="text-purple-500">{stat.icon}</div>
                    <div className="text-sm text-green-400" style={{ lineHeight: '1.4' }}>{stat.change}</div>
                  </div>
                  <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>{stat.value}</div>
                  <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>{stat.title}</div>
                </div>
              ))}
            </div>

            {/* Filters */}
            <div className="flex items-center justify-between">
              <div className="flex items-center" style={{ gap: '20px' }}>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Поиск заказов..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    style={{ paddingLeft: '40px', paddingRight: '15px', paddingTop: '12px', paddingBottom: '12px', lineHeight: '1.4' }}
                  />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                  style={{ padding: '12px 15px', lineHeight: '1.4' }}
                >
                  <option value="all">Все статусы</option>
                  <option value="pending">Ожидает</option>
                  <option value="in_progress">В работе</option>
                  <option value="completed">Выполнено</option>
                  <option value="cancelled">Отменено</option>
                </select>
              </div>
              <button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-4 py-2 rounded-lg transition-colors shadow-lg shadow-purple-500/30">
                Экспорт
              </button>
            </div>

            {/* Orders Table */}
            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-700/50">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">ID заказа</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Клиент</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Услуга</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Статус</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Приоритет</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Прогресс</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Сумма</th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-300">Действия</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700/50">
                    {filteredOrders.map((order) => (
                      <tr key={order.id} className="hover:bg-gray-700/20 transition-colors">
                        <td className="px-6 py-4">
                          <div className="text-white font-medium">{order.id}</div>
                          <div className="text-gray-400 text-sm">{new Date(order.createdAt).toLocaleDateString()}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-white font-medium">{order.clientName}</div>
                          <div className="text-gray-400 text-sm">{order.clientEmail}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-white">{order.serviceName}</div>
                          {order.trackInfo && (
                            <div className="text-gray-400 text-sm">{order.trackInfo.title} - {order.trackInfo.artist}</div>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center rounded-full text-xs ${getStatusColor(order.status)}`} style={{ gap: '5px', padding: '6px 12px' }}>
                            {getStatusIcon(order.status)}
                            <span className="capitalize">{order.status.replace('_', ' ')}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex rounded-full text-xs ${getPriorityColor(order.priority)}`} style={{ padding: '6px 12px', lineHeight: '1.4' }}>
                            {order.priority}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center" style={{ gap: '10px' }}>
                            <div className="flex-1 bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${order.progress}%` }}
                              ></div>
                            </div>
                            <span className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>{order.progress}%</span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-white font-semibold">{order.price.toLocaleString()} ₽</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center" style={{ gap: '10px' }}>
                            <button
                              onClick={() => {
                                setSelectedOrderId(order.id);
                                setShowOrderProcessing(true);
                              }}
                              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-lg transition-colors flex items-center shadow-lg shadow-blue-500/30"
                              style={{ padding: '8px 12px', gap: '6px' }}
                            >
                              <ArrowRight className="w-4 h-4" />
                              <span className="text-sm">Обработать</span>
                            </button>
                            <button className="text-gray-400 hover:text-white transition-colors" style={{ padding: '8px' }}>
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="text-gray-400 hover:text-white transition-colors" style={{ padding: '8px' }}>
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="text-gray-400 hover:text-pink-400 transition-colors" style={{ padding: '8px' }}>
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'clients' && (
          <div className="text-center py-20">
            <h2 className="text-2xl font-bold text-white mb-4">Управление клиентами</h2>
            <p className="text-gray-400">Раздел в разработке</p>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="text-center py-20">
            <h2 className="text-2xl font-bold text-white mb-4">Аналитика</h2>
            <p className="text-gray-400">Раздел в разработке</p>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="text-center py-20">
            <h2 className="text-2xl font-bold text-white mb-4">Настройки</h2>
            <p className="text-gray-400">Раздел в разработке</p>
          </div>
        )}
      </div>

      {/* Order Processing Modal/Page */}
      {showOrderProcessing && selectedOrderId && (
        <div className="fixed inset-0 bg-black z-50">
          <OrderProcessingPage
            orderId={selectedOrderId}
            onBack={() => {
              setShowOrderProcessing(false);
              setSelectedOrderId(null);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
