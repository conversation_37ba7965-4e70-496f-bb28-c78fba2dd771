'use client';

import { useState } from 'react';
import { account } from '@/lib/appwrite';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Crown, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  LogOut
} from 'lucide-react';

export default function AdminLoginSimplePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const loginAsAdmin = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Сначала выходим из любой активной сессии
      try {
        await account.deleteSession('current');
      } catch (e) {
        // Игнорируем ошибку если сессии нет
      }

      // Входим как админ
      await account.createEmailPasswordSession('<EMAIL>', 'admin123456');
      
      // Устанавливаем права администратора
      await account.updatePrefs({
        type: 'admin',
        role: 'administrator',
        permissions: ['admin_panel', 'manage_orders', 'manage_users', 'view_analytics', 'manage_settings'],
        lastLogin: new Date().toISOString()
      });

      setSuccess('Вход выполнен! Перенаправляем в админ панель...');
      
      setTimeout(() => {
        router.push('/admin');
      }, 1500);

    } catch (error: any) {
      console.error('Login error:', error);
      setError(`Ошибка входа: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await account.deleteSession('current');
      setSuccess('Выход выполнен');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error: any) {
      setError(`Ошибка выхода: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
            <Crown className="w-8 h-8 text-purple-500" />
          </div>
          
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Простой вход админа
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Быстрый вход с готовыми данными
          </p>
        </div>

        {/* Content */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error/Success Messages */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg text-green-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          {/* Admin Info */}
          <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg" style={{ padding: '20px', marginBottom: '30px' }}>
            <h3 className="text-purple-300 font-semibold" style={{ marginBottom: '10px' }}>
              Данные для входа:
            </h3>
            <div className="space-y-2">
              <p className="text-purple-200 text-sm">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-purple-200 text-sm">
                <strong>Пароль:</strong> admin123456
              </p>
            </div>
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '25px' }}>
            <button
              onClick={loginAsAdmin}
              disabled={isLoading}
              className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
              style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Вход...
                </>
              ) : (
                <>
                  <Crown className="w-5 h-5" />
                  Войти как админ
                </>
              )}
            </button>

            <button
              onClick={logout}
              className="w-full bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
              style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
            >
              <LogOut className="w-5 h-5" />
              Выйти из сессии
            </button>
          </div>

          {/* Links */}
          <div className="text-center space-y-2">
            <p className="text-gray-400 text-sm">
              <Link 
                href="/quick-admin" 
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                Создать админа
              </Link>
              {' • '}
              <Link 
                href="/test-appwrite" 
                className="text-green-400 hover:text-green-300 transition-colors"
              >
                Тест Appwrite
              </Link>
            </p>
            
            <p className="text-gray-400 text-sm">
              <Link 
                href="/admin" 
                className="text-purple-400 hover:text-purple-300 transition-colors"
              >
                Админ панель
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            ← Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
