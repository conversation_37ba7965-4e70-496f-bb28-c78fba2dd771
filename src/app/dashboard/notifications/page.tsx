'use client';

import { useState, useEffect } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
import {
  Bell,
  Check,
  Trash2,
  Mail,
  AlertCircle,
  Clock,
  ExternalLink,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

const NotificationsPage = () => {
  const [isMounted, setIsMounted] = useState(false);
  const {
    notifications,
    isLoading,
    unreadCount,
    markAsRead,
    markAsUnread,
    deleteNotification,
    markAllAsRead,
    clearAll,
    getFilteredNotifications
  } = useNotifications();

  const [filter, setFilter] = useState<'all' | 'unread' | 'important' | 'read'>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Prevent hydration mismatch by not rendering until mounted
  if (!isMounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Загрузка...</p>
        </div>
      </div>
    );
  }

  const filteredNotifications = getFilteredNotifications(filter).filter(notification => {
    if (selectedType === 'all') return true;
    return notification.type.startsWith(selectedType);
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'success': return 'border-green-500/30 bg-green-500/10';
      case 'warning': return 'border-yellow-500/30 bg-yellow-500/10';
      case 'error': return 'border-red-500/30 bg-red-500/10';
      default: return 'border-blue-500/30 bg-blue-500/10';
    }
  };

  const getTypeIcon = (type: string) => {
    if (type.startsWith('order')) return '📦';
    if (type.startsWith('payment')) return '💳';
    if (type.startsWith('system')) return '⚙️';
    if (type.startsWith('promo')) return '🎉';
    if (type.startsWith('message')) return '💬';
    return '📢';
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Только что';
    if (diffInHours < 24) return `${diffInHours}ч назад`;
    if (diffInHours < 48) return 'Вчера';
    return date.toLocaleDateString('ru-RU', { 
      day: 'numeric', 
      month: 'long', 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const typeOptions = [
    { value: 'all', label: 'Все типы', count: notifications.length },
    { value: 'order', label: 'Заказы', count: notifications.filter(n => n.type.startsWith('order')).length },
    { value: 'payment', label: 'Платежи', count: notifications.filter(n => n.type.startsWith('payment')).length },
    { value: 'system', label: 'Система', count: notifications.filter(n => n.type.startsWith('system')).length },
    { value: 'promo', label: 'Акции', count: notifications.filter(n => n.type.startsWith('promo')).length },
    { value: 'message', label: 'Сообщения', count: notifications.filter(n => n.type.startsWith('message')).length }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white">Загрузка уведомлений...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="max-w-6xl mx-auto" style={{ padding: '30px' }}>
        {/* Header */}
        <div className="flex items-center justify-between" style={{ marginBottom: '30px' }}>
          <div className="flex items-center space-x-4">
            <Link 
              href="/dashboard"
              className="w-10 h-10 rounded-xl bg-gray-800/50 border border-gray-700/50 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-white" style={{ lineHeight: '1.4' }}>
                Уведомления
              </h1>
              <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
                Управляйте всеми уведомлениями
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="px-4 py-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-xl hover:bg-blue-500/30 transition-colors"
              >
                Отметить все как прочитанные
              </button>
            )}
            <button
              onClick={clearAll}
              className="px-4 py-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-xl hover:bg-red-500/30 transition-colors"
            >
              Очистить все
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 lg:grid-cols-4" style={{ gap: '30px', marginBottom: '30px' }}>
          {/* Status Filter */}
          <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800/50 rounded-2xl" style={{ padding: '20px' }}>
            <h3 className="text-white font-semibold" style={{ marginBottom: '15px' }}>Статус</h3>
            <div className="space-y-2">
              {[
                { key: 'all', label: 'Все', count: notifications.length },
                { key: 'unread', label: 'Непрочитанные', count: notifications.filter(n => !n.isRead).length },
                { key: 'important', label: 'Важные', count: notifications.filter(n => n.isImportant).length },
                { key: 'read', label: 'Прочитанные', count: notifications.filter(n => n.isRead).length }
              ].map(option => (
                <button
                  key={option.key}
                  onClick={() => setFilter(option.key as 'all' | 'unread' | 'important' | 'read')}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                    filter === option.key
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{option.label}</span>
                    {option.count > 0 && (
                      <span className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded-full">
                        {option.count}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Type Filter */}
          <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800/50 rounded-2xl" style={{ padding: '20px' }}>
            <h3 className="text-white font-semibold" style={{ marginBottom: '15px' }}>Тип</h3>
            <div className="space-y-2">
              {typeOptions.map(option => (
                <button
                  key={option.value}
                  onClick={() => setSelectedType(option.value)}
                  className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                    selectedType === option.value
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{option.label}</span>
                    {option.count > 0 && (
                      <span className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded-full">
                        {option.count}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="lg:col-span-2 grid grid-cols-2 gap-4">
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl" style={{ padding: '20px' }}>
              <div className="flex items-center justify-between" style={{ marginBottom: '10px' }}>
                <span className="text-blue-400 text-sm">Всего уведомлений</span>
                <Bell className="w-4 h-4 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">{notifications.length}</div>
            </div>
            
            <div className="bg-red-500/10 border border-red-500/30 rounded-xl" style={{ padding: '20px' }}>
              <div className="flex items-center justify-between" style={{ marginBottom: '10px' }}>
                <span className="text-red-400 text-sm">Непрочитанные</span>
                <AlertCircle className="w-4 h-4 text-red-400" />
              </div>
              <div className="text-2xl font-bold text-white">{unreadCount}</div>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800/50 rounded-2xl" style={{ padding: '20px' }}>
          <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
            <h2 className="text-lg font-semibold text-white">
              {filter === 'all' ? 'Все уведомления' : 
               filter === 'unread' ? 'Непрочитанные уведомления' :
               filter === 'important' ? 'Важные уведомления' : 'Прочитанные уведомления'}
            </h2>
            <span className="text-sm text-gray-400">
              {filteredNotifications.length} из {notifications.length}
            </span>
          </div>

          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="w-16 h-16 text-gray-600 mx-auto" style={{ marginBottom: '20px' }} />
              <h3 className="text-lg font-semibold text-gray-400" style={{ marginBottom: '10px' }}>
                Нет уведомлений
              </h3>
              <p className="text-gray-500">
                {filter === 'unread' ? 'Все уведомления прочитаны' : 'Уведомления появятся здесь'}
              </p>
            </div>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border border-gray-700/50 rounded-xl hover:bg-gray-800/30 transition-all duration-200 ${
                    !notification.isRead ? 'bg-gray-800/20' : ''
                  }`}
                  style={{ padding: '20px' }}
                >
                  <div className="flex items-start space-x-4">
                    {/* Icon */}
                    <div className={`w-12 h-12 rounded-xl border flex items-center justify-center flex-shrink-0 ${getCategoryColor(notification.category)}`}>
                      <span className="text-xl">{getTypeIcon(notification.type)}</span>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between" style={{ marginBottom: '10px' }}>
                        <div className="flex items-center space-x-2">
                          <h4 className={`font-semibold ${!notification.isRead ? 'text-white' : 'text-gray-300'}`} style={{ lineHeight: '1.4' }}>
                            {notification.title}
                          </h4>
                          {notification.isImportant && (
                            <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                          )}
                          {!notification.isRead && (
                            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 flex items-center flex-shrink-0">
                          <Clock className="w-3 h-3 mr-1" />
                          {formatTime(notification.createdAt)}
                        </span>
                      </div>
                      
                      <p className="text-gray-400 text-sm leading-relaxed" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {notification.actionUrl && (
                            <a
                              href={notification.actionUrl}
                              className="text-sm text-red-400 hover:text-red-300 flex items-center transition-colors"
                              onClick={() => markAsRead(notification.id)}
                            >
                              {notification.actionText}
                              <ExternalLink className="w-3 h-3 ml-1" />
                            </a>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => notification.isRead ? markAsUnread(notification.id) : markAsRead(notification.id)}
                            className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                            title={notification.isRead ? 'Отметить как непрочитанное' : 'Отметить как прочитанное'}
                          >
                            {notification.isRead ? <Mail className="w-4 h-4" /> : <Check className="w-4 h-4" />}
                          </button>
                          <button
                            onClick={() => deleteNotification(notification.id)}
                            className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-red-400 transition-colors"
                            title="Удалить уведомление"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
