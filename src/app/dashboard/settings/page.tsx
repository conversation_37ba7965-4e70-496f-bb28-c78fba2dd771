'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Settings,
  User,
  Bell,
  Shield,
  Palette,
  CreditCard,
  Key,
  Download,
  Trash2,
  Save,
  Eye,
  EyeOff,
  Check,
  X,
  Camera,
  Mail,
  Phone,
  Globe,
  Lock,
  Sparkles,
  Moon,
  Sun,
  Volume2,
  Smartphone,
  Monitor,
  LogOut,
  Upload,
  Edit
} from 'lucide-react';

const SettingsPage = () => {
  const { user, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('profile');
  const [theme, setTheme] = useState('dark');
  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false,
    marketing: true
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const sections = [
    { id: 'profile', label: 'Профиль', icon: <User className="w-5 h-5" />, color: 'from-blue-500 to-cyan-500' },
    { id: 'account', label: 'Аккаунт', icon: <Settings className="w-5 h-5" />, color: 'from-purple-500 to-pink-500' },
    { id: 'notifications', label: 'Уведомления', icon: <Bell className="w-5 h-5" />, color: 'from-green-500 to-emerald-500' },
    { id: 'appearance', label: 'Внешний вид', icon: <Palette className="w-5 h-5" />, color: 'from-orange-500 to-red-500' },
    { id: 'security', label: 'Безопасность', icon: <Shield className="w-5 h-5" />, color: 'from-red-500 to-pink-500' },
    { id: 'billing', label: 'Платежи', icon: <CreditCard className="w-5 h-5" />, color: 'from-yellow-500 to-orange-500' },
    { id: 'data', label: 'Данные', icon: <Download className="w-5 h-5" />, color: 'from-indigo-500 to-purple-500' }
  ];

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-b border-gray-800/50 backdrop-blur-xl" style={{ padding: '30px' }}>
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-4 mb-2">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">
                  Настройки
                </h1>
                <p className="text-gray-400">
                  Персонализируйте ваш опыт в HIVE Agency
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl transition-all flex items-center px-6 py-3 gap-2 font-medium">
              <Save className="w-4 h-4" />
              Сохранить все
            </button>
            <button className="bg-gray-800 hover:bg-gray-700 text-white rounded-xl transition-colors flex items-center px-4 py-3 gap-2">
              <Download className="w-4 h-4" />
              Экспорт
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-80 bg-gray-900/30 border-r border-gray-800/50 backdrop-blur-xl" style={{ padding: '30px' }}>
          <nav className="space-y-3">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center text-left rounded-xl transition-all duration-300 group ${
                  activeSection === section.id
                    ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-purple-500/30 shadow-lg shadow-purple-500/10'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 hover:border-gray-700/50 border border-transparent'
                }`}
                style={{ padding: '16px 20px', gap: '16px' }}
              >
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all ${
                  activeSection === section.id
                    ? `bg-gradient-to-r ${section.color}`
                    : 'bg-gray-800 group-hover:bg-gray-700'
                }`}>
                  <div className={activeSection === section.id ? 'text-white' : 'text-gray-400 group-hover:text-white'}>
                    {section.icon}
                  </div>
                </div>
                <div className="flex-1">
                  <span className="font-semibold block">{section.label}</span>
                  <span className="text-xs text-gray-500 group-hover:text-gray-400">
                    {section.id === 'profile' && 'Личная информация'}
                    {section.id === 'account' && 'Основные настройки'}
                    {section.id === 'notifications' && 'Уведомления и оповещения'}
                    {section.id === 'appearance' && 'Тема и интерфейс'}
                    {section.id === 'security' && 'Пароль и безопасность'}
                    {section.id === 'billing' && 'Платежи и подписки'}
                    {section.id === 'data' && 'Экспорт и удаление'}
                  </span>
                </div>
              </button>
            ))}

            {/* Logout Button */}
            <div className="pt-6 mt-6 border-t border-gray-800">
              <button
                onClick={logout}
                className="w-full flex items-center text-left rounded-xl transition-all duration-300 text-red-400 hover:text-white hover:bg-red-500/20 hover:border-red-500/30 border border-transparent"
                style={{ padding: '16px 20px', gap: '16px' }}
              >
                <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-red-500/20 hover:bg-red-500">
                  <LogOut className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <span className="font-semibold block">Выйти</span>
                  <span className="text-xs text-gray-500">Завершить сеанс</span>
                </div>
              </button>
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1" style={{ padding: '30px' }}>
          {activeSection === 'profile' && (
            <div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Информация профиля
              </h2>
              
              <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                  {/* Avatar Section */}
                  <div className="flex items-center" style={{ gap: '20px' }}>
                    <div className="w-20 h-20 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
                      {user?.avatar ? (
                        <img src={user.avatar} alt="Avatar" className="w-full h-full object-cover" />
                      ) : (
                        <User className="w-8 h-8 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-white font-semibold" style={{ lineHeight: '1.4' }}>
                        {user?.name || 'Имя пользователя'}
                      </h3>
                      <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
                        {user?.email || '<EMAIL>'}
                      </p>
                      <button className="text-red-400 hover:text-red-300 text-sm transition-colors" style={{ marginTop: '5px', lineHeight: '1.4' }}>
                        Изменить фото
                      </button>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3" style={{ gap: '20px' }}>
                    <div className="bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
                      <div className="text-2xl font-bold text-white" style={{ lineHeight: '1.2' }}>24</div>
                      <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Заказов</div>
                    </div>
                    <div className="bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
                      <div className="text-2xl font-bold text-white" style={{ lineHeight: '1.2' }}>₽2.4M</div>
                      <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Потрачено</div>
                    </div>
                    <div className="bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
                      <div className="text-2xl font-bold text-white" style={{ lineHeight: '1.2' }}>98%</div>
                      <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Рейтинг</div>
                    </div>
                  </div>

                  {/* Profile Form */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                    <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                      <div>
                        <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                          Имя
                        </label>
                        <input
                          type="text"
                          defaultValue={user?.name || ''}
                          className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                          style={{ padding: '12px 16px', lineHeight: '1.4' }}
                        />
                      </div>
                      <div>
                        <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                          Email
                        </label>
                        <input
                          type="email"
                          defaultValue={user?.email || ''}
                          className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                          style={{ padding: '12px 16px', lineHeight: '1.4' }}
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                        Биография
                      </label>
                      <textarea
                        rows={4}
                        placeholder="Расскажите о себе..."
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                        style={{ padding: '12px 16px', lineHeight: '1.6' }}
                      />
                    </div>

                    <div className="flex justify-end" style={{ gap: '15px' }}>
                      <button className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors" style={{ padding: '12px 20px', lineHeight: '1.4' }}>
                        Отмена
                      </button>
                      <button className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors" style={{ padding: '12px 20px', lineHeight: '1.4' }}>
                        Сохранить
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'notifications' && (
            <div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Настройки уведомлений
              </h2>
              
              <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                  {[
                    { 
                      title: 'Email уведомления', 
                      description: 'Получать уведомления на почту о заказах и акциях',
                      enabled: true 
                    },
                    { 
                      title: 'Push уведомления', 
                      description: 'Уведомления в браузере о важных событиях',
                      enabled: true 
                    },
                    { 
                      title: 'SMS уведомления', 
                      description: 'Получать SMS о критически важных событиях',
                      enabled: false 
                    },
                    { 
                      title: 'Маркетинговые уведомления', 
                      description: 'Информация о новых услугах и специальных предложениях',
                      enabled: false 
                    }
                  ].map((notification, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <h3 className="text-white font-medium" style={{ lineHeight: '1.4' }}>
                          {notification.title}
                        </h3>
                        <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                          {notification.description}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          defaultChecked={notification.enabled}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeSection === 'security' && (
            <div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Безопасность
              </h2>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                {/* Password Change */}
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <h3 className="text-white font-semibold" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                    Изменить пароль
                  </h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    <div>
                      <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                        Текущий пароль
                      </label>
                      <input
                        type="password"
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                        style={{ padding: '12px 16px', lineHeight: '1.4' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                        Новый пароль
                      </label>
                      <input
                        type="password"
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                        style={{ padding: '12px 16px', lineHeight: '1.4' }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                        Подтвердите новый пароль
                      </label>
                      <input
                        type="password"
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                        style={{ padding: '12px 16px', lineHeight: '1.4' }}
                      />
                    </div>
                    <button className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors self-start" style={{ padding: '12px 20px', lineHeight: '1.4' }}>
                      Обновить пароль
                    </button>
                  </div>
                </div>

                {/* Two-Factor Authentication */}
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                    <div>
                      <h3 className="text-white font-semibold" style={{ lineHeight: '1.4' }}>
                        Двухфакторная аутентификация
                      </h3>
                      <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                        Дополнительная защита вашего аккаунта
                      </p>
                    </div>
                    <button className="bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors" style={{ padding: '10px 15px', lineHeight: '1.4' }}>
                      Включить
                    </button>
                  </div>
                </div>

                {/* Active Sessions */}
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <h3 className="text-white font-semibold" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                    Активные сессии
                  </h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    {[
                      { device: 'MacBook Pro', location: 'Москва, Россия', current: true },
                      { device: 'iPhone 15', location: 'Москва, Россия', current: false },
                      { device: 'Chrome Browser', location: 'Санкт-Петербург, Россия', current: false }
                    ].map((session, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
                        <div>
                          <div className="flex items-center" style={{ gap: '8px' }}>
                            <span className="text-white font-medium" style={{ lineHeight: '1.4' }}>
                              {session.device}
                            </span>
                            {session.current && (
                              <span className="bg-green-500/20 text-green-400 text-xs rounded-full" style={{ padding: '2px 8px' }}>
                                Текущая
                              </span>
                            )}
                          </div>
                          <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                            {session.location}
                          </p>
                        </div>
                        {!session.current && (
                          <button className="text-red-400 hover:text-red-300 text-sm transition-colors">
                            Завершить
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Другие секции можно добавить аналогично */}
          {!['profile', 'notifications', 'security'].includes(activeSection) && (
            <div className="text-center" style={{ padding: '60px 20px' }}>
              <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                <Settings className="w-8 h-8 text-gray-400" />
              </div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
                Раздел в разработке
              </h2>
              <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
                Этот раздел настроек скоро будет доступен
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
