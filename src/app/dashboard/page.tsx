'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRealtimeOrders } from '@/hooks/useRealtimeOrders';
import Link from 'next/link';
import NotificationCenter from '@/components/NotificationCenter';
import { StatsGrid } from '@/components/StatsCard';
import OrdersManager from '@/components/OrdersManager';
import AnalyticsDashboard from '@/components/AnalyticsDashboard';
import RealTimeStats from '@/components/RealTimeStats';
import {
  Settings,
  Radio,
  BarChart3,
  CheckCircle,
  TrendingUp,
  Sparkles,
  Home,
  BookOpen,
  MessageCircle,
  ChevronDown,
  AlertCircle,
  Filter,
  Upload,
  Building,
  UserCheck,
  Music,
  Shield,
  Users,
  LogOut
} from 'lucide-react';
import { FaSpotify, FaYoutube, FaTiktok } from 'react-icons/fa';
import TrackPromotion from '@/components/TrackPromotion';
import ArtistPromotion from '@/components/ArtistPromotion';
import SupportCenter from '@/components/SupportCenter';

const DashboardPageContent = () => {
  const { user, loading, logout } = useAuth();
  const [language] = useState('ru');
  const [selectedPeriod, setSelectedPeriod] = useState('Last month');
  const [activeTab, setActiveTab] = useState('overview');
  const [serviceType, setServiceType] = useState<'artist' | 'track'>('artist');
  const [isSupportOpen, setIsSupportOpen] = useState(false);

  // Реал-тайм заказы для текущего пользователя
  useRealtimeOrders({
    userId: user?.$id
  });

  // Мок пользователя для разработки
  const mockUser = user || {
    $id: 'mock-user',
    name: 'MORGENSHTERN',
    email: '<EMAIL>',
    type: 'artist' as const,
    emailVerification: true,
    phoneVerification: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    avatar: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=150&h=150&fit=crop&crop=face',
    followers: 12500000,
    monthlyListeners: 8900000,
    verified: true,
    tier: 'premium'
  };

  // Данные для курсов (в стиле изображения)
  const courses = [
    {
      id: 1,
      title: 'Spotify Promotion',
      subtitle: 'Advanced playlist placement',
      progress: 100,
      totalHours: 120,
      spentHours: 120,
      finalGrade: '192/200',
      status: 'Completed',
      icon: <FaSpotify className="w-6 h-6 text-green-400" />,
      color: 'green'
    },
    {
      id: 2,
      title: 'TikTok Viral',
      subtitle: 'Viral content strategy',
      progress: 20,
      totalHours: 18,
      spentHours: 18,
      finalGrade: '20%',
      status: 'In progress',
      icon: <FaTiktok className="w-6 h-6 text-pink-400" />,
      color: 'pink'
    },
    {
      id: 3,
      title: 'YouTube Growth',
      subtitle: 'Channel optimization',
      progress: 74,
      totalHours: 54,
      spentHours: 54,
      finalGrade: '74%',
      status: 'In progress',
      icon: <FaYoutube className="w-6 h-6 text-purple-400" />,
      color: 'purple'
    },
    {
      id: 4,
      title: 'Radio Campaign',
      subtitle: 'Global radio placement',
      progress: 0,
      totalHours: 0,
      spentHours: 0,
      finalGrade: '0%',
      status: 'In progress',
      icon: <Radio className="w-6 h-6 text-blue-400" />,
      color: 'blue'
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <div className="text-white text-xl font-medium">
            {language === 'ru' ? 'Загрузка дашборда...' : 'Loading dashboard...'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-20 bg-gray-900/80 backdrop-blur-xl border-r border-gray-800/50 flex flex-col items-center py-6 z-50">
        {/* Logo */}
        <Link
          href="/"
          className="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-8 shadow-lg shadow-purple-500/30 hover:scale-110 transition-all duration-300 cursor-pointer"
          title="Перейти на главную страницу"
        >
          <Sparkles className="w-6 h-6 text-white" />
        </Link>
        
        {/* Navigation */}
        <nav className="flex flex-col space-y-4">
          <button 
            onClick={() => setActiveTab('overview')}
            className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200 ${
              activeTab === 'overview'
                ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
            }`}
          >
            <Home className="w-5 h-5" />
          </button>
          <button 
            onClick={() => setActiveTab('courses')}
            className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200 ${
              activeTab === 'courses'
                ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
            }`}
          >
            <BookOpen className="w-5 h-5" />
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200 ${
              activeTab === 'analytics'
                ? 'bg-pink-500/20 text-pink-400 border border-pink-500/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
            }`}
            title="Аналитика"
          >
            <BarChart3 className="w-5 h-5" />
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200 ${
              activeTab === 'stats'
                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
            }`}
            title="Реальная статистика"
          >
            <TrendingUp className="w-5 h-5" />
          </button>
          <button
            onClick={() => setIsSupportOpen(true)}
            className="w-12 h-12 rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-200"
            title="Support Center"
          >
            <MessageCircle className="w-5 h-5" />
          </button>
          <button
            onClick={() => window.location.href = '/dashboard/settings'}
            className="w-12 h-12 rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-200"
            title="Настройки"
          >
            <Settings className="w-5 h-5" />
          </button>
          <button
            onClick={() => window.location.href = '/admin'}
            className="w-12 h-12 rounded-xl flex items-center justify-center text-purple-400 hover:text-white hover:bg-purple-500/20 transition-all duration-200"
            title="Админ панель"
          >
            <Shield className="w-5 h-5" />
          </button>
        </nav>

        {/* Logout Button at Bottom */}
        <div className="mt-auto">
          <button
            onClick={logout}
            className="w-12 h-12 rounded-xl flex items-center justify-center text-red-400 hover:text-white hover:bg-red-500/20 transition-all duration-200"
            title="Выйти из аккаунта"
          >
            <LogOut className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-20">
        {/* Header */}
        <div className="bg-gray-900/50 backdrop-blur-xl border-b border-gray-800/50 sticky top-0 z-40">
          <div className="px-8 py-4">
            <div className="flex items-center justify-between">
              {/* Left: Title */}
              <div>
                <h1 className="text-2xl font-bold text-white">
                  {activeTab === 'overview' ? 'My Classes' : 'Campaigns'}
                </h1>
              </div>
              
              {/* Right: Notifications and Profile */}
              <div className="flex items-center" style={{ gap: '20px' }}>
                <NotificationCenter />
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg shadow-purple-500/30">
                  <img
                    src={mockUser.avatar}
                    alt={mockUser.name}
                    className="w-full h-full rounded-xl object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="dashboard-spacing" style={{ marginLeft: '80px', paddingLeft: '30px', paddingRight: '30px', paddingTop: '30px', paddingBottom: '30px' }}>
          {/* Service Type Toggle */}
          <div style={{ marginBottom: '30px' }}>
            <div className="flex items-center justify-center">
              <div className="bg-gray-800/50 rounded-xl border border-gray-700/50" style={{ padding: '5px', display: 'flex', gap: '5px' }}>
                <button
                  onClick={() => setServiceType('artist')}
                  className={`rounded-lg font-semibold transition-all ${
                    serviceType === 'artist'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/30'
                      : 'text-gray-400 hover:text-white'
                  }`}
                  style={{ padding: '15px 25px', lineHeight: '1.4' }}
                >
                  <Users className="w-5 h-5 inline" style={{ marginRight: '10px' }} />
                  Artist Promotion
                </button>
                <button
                  onClick={() => setServiceType('track')}
                  className={`rounded-lg font-semibold transition-all ${
                    serviceType === 'track'
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg shadow-blue-500/30'
                      : 'text-gray-400 hover:text-white'
                  }`}
                  style={{ padding: '15px 25px', lineHeight: '1.4' }}
                >
                  <Music className="w-5 h-5 inline" style={{ marginRight: '10px' }} />
                  Track Promotion
                </button>
              </div>
            </div>
          </div>

          {activeTab === 'overview' && (
            <div>
              {/* Statistics */}
              <div style={{ marginBottom: '30px' }}>
                <StatsGrid
                  title="Статистика"
                  stats={[
                    {
                      id: 'total-orders',
                      title: 'Всего заказов',
                      value: 24,
                      change: { value: 12, type: 'increase', period: 'за месяц' },
                      icon: <BarChart3 className="w-5 h-5" />,
                      color: 'purple',
                      description: 'Общее количество заказов',
                      trend: [10, 15, 12, 18, 24, 20, 24],
                      actions: [
                        { label: 'Подробнее', icon: <BarChart3 className="w-4 h-4" />, onClick: () => console.log('View details') },
                        { label: 'Экспорт', icon: <Upload className="w-4 h-4" />, onClick: () => console.log('Export') }
                      ]
                    },
                    {
                      id: 'active-campaigns',
                      title: 'Активные кампании',
                      value: 8,
                      change: { value: 25, type: 'increase', period: 'за неделю' },
                      icon: <Radio className="w-5 h-5" />,
                      color: 'blue',
                      description: 'Кампании в работе',
                      trend: [5, 6, 7, 8, 6, 7, 8]
                    },
                    {
                      id: 'revenue',
                      title: 'Доход',
                      value: '₽2.4M',
                      change: { value: 8, type: 'increase', period: 'за месяц' },
                      icon: <Building className="w-5 h-5" />,
                      color: 'green',
                      description: 'Общий доход',
                      trend: [1.8, 2.0, 2.1, 2.2, 2.3, 2.4, 2.4]
                    },
                    {
                      id: 'satisfaction',
                      title: 'Удовлетворенность',
                      value: '98%',
                      change: { value: 2, type: 'increase', period: 'за месяц' },
                      icon: <UserCheck className="w-5 h-5" />,
                      color: 'purple',
                      description: 'Рейтинг клиентов',
                      trend: [94, 95, 96, 97, 98, 98, 98]
                    }
                  ]}
                  actions={[
                    { label: 'Обновить', icon: <Settings className="w-4 h-4" />, onClick: () => console.log('Refresh') },
                    { label: 'Настройки', icon: <Filter className="w-4 h-4" />, onClick: () => console.log('Settings') }
                  ]}
                />
              </div>

              {/* Service Content */}
              {serviceType === 'track' ? (
                <TrackPromotion />
              ) : (
                <ArtistPromotion />
              )}

              {/* Two Column Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-3" style={{ gap: '30px' }}>
                {/* Left Column - Course List */}
                <div className="lg:col-span-2">
                  <div className="flex items-center justify-between" style={{ marginBottom: '30px' }}>
                    <h3 className="text-xl font-bold text-white">Course you&apos;re taking</h3>
                    <div className="flex items-center" style={{ gap: '10px' }}>
                      <span className="text-sm text-gray-400">Filter by</span>
                      <button className="flex items-center text-gray-400 hover:text-white transition-colors" style={{ gap: '5px' }}>
                        <span className="text-sm">All</span>
                        <ChevronDown className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
                    {courses.map((course) => (
                      <div key={course.id} className="bg-gray-900/50 backdrop-blur-xl border border-gray-800/50 rounded-2xl hover:border-gray-700/50 transition-all duration-300" style={{ padding: '20px' }}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center" style={{ gap: '20px' }}>
                            <div className={`w-12 h-12 rounded-xl bg-${course.color}-500/20 border border-${course.color}-500/30 flex items-center justify-center`}>
                              {course.icon}
                            </div>
                            <div>
                              <h4 className="text-white font-semibold" style={{ marginBottom: '20px' }}>{course.title}</h4>
                              <p className="text-gray-400 text-sm" style={{ marginBottom: '20px' }}>{course.subtitle}</p>
                              <div className="flex items-center" style={{ gap: '20px', marginTop: '20px' }}>
                                <span className="text-xs text-gray-500">{course.spentHours} Hours spend</span>
                                <span className="text-xs text-gray-500">{course.finalGrade} Final grade</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm px-3 py-1 rounded-full ${
                              course.status === 'Completed'
                                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                : 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                            }`}>
                              {course.status}
                            </div>
                            <div className="w-24 h-2 bg-gray-800 rounded-full overflow-hidden" style={{ marginTop: '20px' }}>
                              <div
                                className={`h-full bg-${course.color}-500 transition-all duration-500`}
                                style={{ width: `${course.progress}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-500" style={{ marginTop: '20px', display: 'block' }}>{course.progress}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right Column - Progress & Stats */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
                  {/* My Progress */}
                  <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800/50 rounded-2xl" style={{ padding: '20px' }}>
                    <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                      <h3 className="text-white font-semibold">My Progress</h3>
                      <button className="text-gray-400 hover:text-white transition-colors">
                        <Filter className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="text-right" style={{ marginBottom: '20px' }}>
                      <button
                        onClick={() => setSelectedPeriod(selectedPeriod === 'Last month' ? 'This week' : 'Last month')}
                        className="flex items-center text-gray-400 hover:text-white transition-colors ml-auto" style={{ gap: '5px' }}
                      >
                        <span className="text-sm">{selectedPeriod}</span>
                        <ChevronDown className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Track Study Time */}
                    <div style={{ marginBottom: '30px' }}>
                      <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                        <span className="text-gray-400 text-sm">Track your study time</span>
                        <button className="text-gray-400 hover:text-white transition-colors">
                          <AlertCircle className="w-4 h-4" />
                        </button>
                      </div>
                      <div className="h-32 bg-gray-800/50 rounded-xl p-4 flex items-end justify-between">
                        {[40, 60, 80, 45, 70, 90, 65].map((height, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div
                              className="w-3 bg-purple-500 rounded-t"
                              style={{ height: `${height}%` }}
                            ></div>
                          </div>
                        ))}
                      </div>
                      <div className="text-center mt-3">
                        <span className="text-gray-500 text-xs">Hours</span>
                      </div>
                    </div>

                    {/* Courses Completed */}
                    <div className="bg-green-500/20 border border-green-500/30 rounded-xl" style={{ padding: '20px', marginBottom: '30px' }}>
                      <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                        <span className="text-green-400 text-sm">Courses completed</span>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="text-3xl font-bold text-white">36</div>
                    </div>

                    {/* Performance */}
                    <div>
                      <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                        <span className="text-gray-400 text-sm">Performance</span>
                        <button className="text-gray-400 hover:text-white transition-colors">
                          <AlertCircle className="w-4 h-4" />
                        </button>
                      </div>
                      <div className="text-3xl font-bold text-white mb-4">124</div>
                      <div className="h-16 bg-gray-800/50 rounded-xl p-2 flex items-end">
                        <div className="w-full h-full bg-gradient-to-t from-purple-500 to-purple-400 rounded opacity-80"></div>
                      </div>
                    </div>
                  </div>

                  {/* Complete Tests */}
                  <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-2xl p-6">
                    <div className="flex items-center justify-between mb-6">
                      <span className="text-purple-400 text-sm">Complete tests</span>
                      <CheckCircle className="w-4 h-4 text-purple-400" />
                    </div>
                    <div className="text-2xl font-bold text-white">Take a test</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'courses' && (
            <div>
              <OrdersManager />
            </div>
          )}

          {activeTab === 'analytics' && (
            <div>
              <AnalyticsDashboard />
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="p-8">
              <RealTimeStats userId={user?.$id} />
            </div>
          )}
        </div>
      </div>

      {/* Support Center Modal */}
      <SupportCenter
        isOpen={isSupportOpen}
        onClose={() => setIsSupportOpen(false)}
      />
    </div>
  );
};

export default DashboardPageContent;
