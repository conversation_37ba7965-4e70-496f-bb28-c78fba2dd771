'use client';

import Link from 'next/link';
import { 
  Shield, 
  FileText, 
  Users, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle,
  Music,
  Instagram,
  Youtube,
  Mail,
  Phone,
  Globe,
  ArrowLeft,
  Calendar,
  DollarSign,
  Lock,
  Eye,
  UserCheck,
  Zap
} from 'lucide-react';

export default function TermsOfUsePage() {
  const lastUpdated = "15 января 2024";

  const sections = [
    {
      id: 'general',
      title: 'Общие положения',
      icon: <FileText className="w-5 h-5" />,
      content: [
        'HIVE Agency - профессиональное агентство по продвижению музыкальных артистов и треков в социальных сетях и стриминговых платформах.',
        'Настоящие Условия использования регулируют отношения между HIVE Agency и пользователями наших услуг.',
        'Используя наши услуги, вы соглашаетесь с данными условиями в полном объеме.',
        'Мы оставляем за собой право изменять данные условия с уведомлением пользователей за 7 дней.'
      ]
    },
    {
      id: 'services',
      title: 'Наши услуги',
      icon: <Music className="w-5 h-5" />,
      content: [
        'Продвижение артистов: создание и развитие имиджа, PR-кампании, медиа-размещения',
        'Продвижение треков: SMM, реклама в социальных сетях, плейлист-питчинг',
        'Создание контента: фото/видео съемки, дизайн обложек, создание клипов',
        'Аналитика и отчетность: детальная статистика по всем кампаниям',
        'Консультации: стратегическое планирование карьеры артиста'
      ]
    },
    {
      id: 'user-obligations',
      title: 'Обязанности пользователей',
      icon: <UserCheck className="w-5 h-5" />,
      content: [
        'Предоставлять достоверную информацию при регистрации и заказе услуг',
        'Соблюдать авторские права на предоставляемые материалы',
        'Не использовать наши услуги для продвижения контента, нарушающего законодательство',
        'Своевременно оплачивать заказанные услуги согласно выставленным счетам',
        'Предоставлять необходимые материалы и доступы для выполнения работ'
      ]
    },
    {
      id: 'payment',
      title: 'Оплата и возврат средств',
      icon: <CreditCard className="w-5 h-5" />,
      content: [
        'Оплата производится согласно прайс-листу на момент заказа услуги',
        'Принимаем оплату банковскими картами, переводами, криптовалютой',
        'Возврат средств возможен в течение 14 дней при отказе от услуги до начала работ',
        'При частичном выполнении работ возврат рассчитывается пропорционально',
        'Возврат не предоставляется после завершения кампании продвижения'
      ]
    },
    {
      id: 'intellectual-property',
      title: 'Интеллектуальная собственность',
      icon: <Shield className="w-5 h-5" />,
      content: [
        'Клиент гарантирует наличие всех прав на предоставляемые материалы',
        'HIVE Agency не несет ответственности за нарушение авторских прав клиентом',
        'Созданные нами материалы (дизайн, тексты, концепции) остаются нашей собственностью',
        'Клиент получает право использования созданных материалов в рамках заказанной услуги',
        'Запрещается передача наших методик и стратегий третьим лицам'
      ]
    },
    {
      id: 'privacy',
      title: 'Конфиденциальность',
      icon: <Lock className="w-5 h-5" />,
      content: [
        'Мы обрабатываем персональные данные в соответствии с ФЗ "О персональных данных"',
        'Собираем только необходимые для оказания услуг данные',
        'Не передаем данные клиентов третьим лицам без согласия',
        'Используем современные методы защиты информации',
        'Клиент может запросить удаление своих данных в любое время'
      ]
    },
    {
      id: 'guarantees',
      title: 'Гарантии и ответственность',
      icon: <CheckCircle className="w-5 h-5" />,
      content: [
        'Гарантируем выполнение работ в соответствии с техническим заданием',
        'Не гарантируем конкретные результаты продвижения (количество подписчиков, прослушиваний)',
        'Результаты зависят от качества контента, конкуренции, алгоритмов платформ',
        'Предоставляем детальную отчетность по всем проведенным работам',
        'Ответственность ограничена стоимостью заказанной услуги'
      ]
    },
    {
      id: 'prohibited',
      title: 'Запрещенные действия',
      icon: <AlertTriangle className="w-5 h-5" />,
      content: [
        'Продвижение контента с нарушением авторских прав',
        'Размещение материалов экстремистского, порнографического характера',
        'Использование наших услуг для спама или мошенничества',
        'Попытки взлома или нарушения работы наших систем',
        'Передача доступов к аккаунту третьим лицам'
      ]
    },
    {
      id: 'platforms',
      title: 'Работа с платформами',
      icon: <Globe className="w-5 h-5" />,
      content: [
        'Работаем с Instagram, YouTube, VK, TikTok, Spotify, Apple Music и другими',
        'Соблюдаем правила и политики каждой платформы',
        'Не несем ответственности за изменения алгоритмов платформ',
        'Клиент должен предоставить необходимые доступы к аккаунтам',
        'Рекомендуем создание бэкапов важного контента'
      ]
    },
    {
      id: 'termination',
      title: 'Прекращение сотрудничества',
      icon: <Eye className="w-5 h-5" />,
      content: [
        'Любая сторона может прекратить сотрудничество с уведомлением за 3 дня',
        'При нарушении условий договора сотрудничество может быть прекращено немедленно',
        'Выполненные работы оплачиваются в полном объеме',
        'Клиент сохраняет права на полученные результаты продвижения',
        'Конфиденциальная информация остается защищенной после прекращения сотрудничества'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-black" style={{ padding: '30px 30px 60px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '50px' }}>
          <div className="flex items-center justify-center" style={{ gap: '15px', marginBottom: '20px' }}>
            <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-400" />
            </div>
            <h1 className="text-4xl font-bold text-white">Terms of Use</h1>
          </div>
          
          <p className="text-xl text-gray-400" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
            Условия использования услуг HIVE Agency
          </p>
          
          <div className="flex items-center justify-center" style={{ gap: '20px' }}>
            <div className="flex items-center" style={{ gap: '8px' }}>
              <Calendar className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-500">Обновлено: {lastUpdated}</span>
            </div>
            <div className="flex items-center" style={{ gap: '8px' }}>
              <Shield className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-400">Юридически обязательный документ</span>
            </div>
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '30px', marginBottom: '40px' }}>
          <h2 className="text-xl font-semibold text-white" style={{ marginBottom: '20px' }}>
            Быстрая навигация
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5" style={{ gap: '15px' }}>
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="flex items-center bg-gray-800/50 hover:bg-gray-700/50 rounded-lg transition-colors"
                style={{ padding: '12px', gap: '8px' }}
              >
                <div className="text-blue-400">
                  {section.icon}
                </div>
                <span className="text-sm text-gray-300 hover:text-white transition-colors">
                  {section.title}
                </span>
              </a>
            ))}
          </div>
        </div>

        {/* Sections */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
          {sections.map((section, index) => (
            <div
              key={section.id}
              id={section.id}
              className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50"
              style={{ padding: '30px' }}
            >
              <div className="flex items-center" style={{ gap: '12px', marginBottom: '20px' }}>
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  {section.icon}
                </div>
                <h2 className="text-2xl font-bold text-white">
                  {index + 1}. {section.title}
                </h2>
              </div>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {section.content.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start" style={{ gap: '12px' }}>
                    <div className="w-2 h-2 bg-blue-400 rounded-full" style={{ marginTop: '8px', flexShrink: 0 }} />
                    <p className="text-gray-300" style={{ lineHeight: '1.6' }}>
                      {item}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Contact Information */}
        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-2xl" style={{ padding: '30px', marginTop: '40px' }}>
          <h2 className="text-2xl font-bold text-white" style={{ marginBottom: '20px' }}>
            Контактная информация
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
            <div>
              <h3 className="text-lg font-semibold text-blue-300" style={{ marginBottom: '15px' }}>
                Для вопросов по условиям:
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Mail className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-200"><EMAIL></span>
                </div>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Phone className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-200">+7 (999) 123-45-67</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-purple-300" style={{ marginBottom: '15px' }}>
                Техническая поддержка:
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Mail className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-200"><EMAIL></span>
                </div>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Globe className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-200">hive-agency.com/support</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="text-center" style={{ marginTop: '40px' }}>
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap', marginBottom: '20px' }}>
            <Link
              href="/privacy"
              className="bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 24px' }}
            >
              Политика конфиденциальности
            </Link>
            
            <Link
              href="/contact"
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-semibold"
              style={{ padding: '12px 24px' }}
            >
              Связаться с нами
            </Link>
          </div>
          
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            style={{ gap: '8px' }}
          >
            <ArrowLeft className="w-4 h-4" />
            Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
