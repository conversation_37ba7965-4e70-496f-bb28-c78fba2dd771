import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, ORDERS_COLLECTION_ID } from '@/lib/appwrite';
import { ID } from 'appwrite';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const accountData = await request.json();

    // Получаем текущий заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    // Парсим текущие назначенные ресурсы
    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Создаем новый социальный аккаунт
    const newAccount = {
      id: ID.unique(),
      platform: accountData.platform,
      username: accountData.username,
      followers: accountData.followers || 0,
      engagement: accountData.engagement || 0,
      niche: accountData.niche || [],
      isActive: true,
      credentials: {
        accessToken: accountData.accessToken || '',
        refreshToken: accountData.refreshToken || '',
        apiKey: accountData.apiKey || ''
      },
      stats: {
        postsCount: 0,
        avgLikes: 0,
        avgComments: 0,
        lastActivity: new Date().toISOString()
      },
      createdAt: new Date().toISOString()
    };

    // Добавляем новый аккаунт к списку
    assignedResources.socialAccounts.push(newAccount);

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources)
      }
    );

    // Создаем уведомление
    try {
      await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'order_requires_info',
          title: 'Добавлен социальный аккаунт',
          message: `К вашему заказу добавлен аккаунт ${accountData.platform}: @${accountData.username}`,
          userId: order.userId,
          metadata: {
            orderId,
            platform: accountData.platform,
            username: accountData.username
          }
        })
      });
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      account: newAccount
    });

  } catch (error) {
    console.error('Error adding social account:', error);
    return NextResponse.json(
      { error: 'Failed to add social account' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [] };

    return NextResponse.json({
      socialAccounts: assignedResources.socialAccounts || []
    });

  } catch (error) {
    console.error('Error fetching social accounts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch social accounts' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const { accountId } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Удаляем аккаунт
    assignedResources.socialAccounts = assignedResources.socialAccounts.filter(
      (account: any) => account.id !== accountId
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources)
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error deleting social account:', error);
    return NextResponse.json(
      { error: 'Failed to delete social account' },
      { status: 500 }
    );
  }
}
