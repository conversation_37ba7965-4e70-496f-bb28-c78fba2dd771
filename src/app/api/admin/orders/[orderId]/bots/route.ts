import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, ORDERS_COLLECTION_ID } from '@/lib/appwrite';
import { ID } from 'appwrite';
import { BOT_TEMPLATES } from '@/config/pricing';

export async function POST(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const botConfig = await request.json();

    // Получаем текущий заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    // Парсим текущие назначенные ресурсы
    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Получаем шаблон бота
    const platform = botConfig.platform;
    const botType = botConfig.type;
    const template = BOT_TEMPLATES[platform as keyof typeof BOT_TEMPLATES]?.[botType as any];

    if (!template) {
      return NextResponse.json(
        { error: 'Invalid bot template' },
        { status: 400 }
      );
    }

    // Создаем нового бота
    const newBot = {
      id: ID.unique(),
      name: template.name,
      platform: platform,
      type: botType,
      isActive: true,
      config: {
        targetKeywords: botConfig.targetKeywords || [],
        activityLevel: botConfig.activityLevel || 'medium',
        responseStyle: botConfig.responseStyle || 'professional',
        workingHours: {
          start: botConfig.workingHours?.start || '09:00',
          end: botConfig.workingHours?.end || '21:00',
          timezone: botConfig.workingHours?.timezone || 'Europe/Moscow'
        },
        limits: {
          dailyActions: botConfig.dailyActions || 100,
          hourlyActions: botConfig.hourlyActions || 10
        }
      },
      capabilities: template.capabilities.map((cap: string) => ({
        type: cap,
        enabled: true,
        settings: {}
      })),
      stats: {
        totalActions: 0,
        successRate: 0,
        lastActivity: new Date().toISOString(),
        generatedLeads: 0
      },
      createdAt: new Date().toISOString(),
      smartFeatures: template.smartFeatures || [],
      antiDetection: template.antiDetection || []
    };

    // Добавляем нового бота к списку
    assignedResources.activeBots.push(newBot);

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources)
      }
    );

    // Создаем уведомление
    try {
      await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'order_requires_info',
          title: 'Активирован бот',
          message: `Для вашего заказа активирован ${template.name} на платформе ${platform}`,
          userId: order.userId,
          metadata: {
            orderId,
            botName: template.name,
            platform
          }
        })
      });
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      bot: newBot
    });

  } catch (error) {
    console.error('Error activating bot:', error);
    return NextResponse.json(
      { error: 'Failed to activate bot' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { activeBots: [] };

    return NextResponse.json({
      bots: assignedResources.activeBots || []
    });

  } catch (error) {
    console.error('Error fetching bots:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bots' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const { botId, action, config } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Находим бота
    const botIndex = assignedResources.activeBots.findIndex((bot: any) => bot.id === botId);
    
    if (botIndex === -1) {
      return NextResponse.json(
        { error: 'Bot not found' },
        { status: 404 }
      );
    }

    // Выполняем действие
    switch (action) {
      case 'toggle':
        assignedResources.activeBots[botIndex].isActive = !assignedResources.activeBots[botIndex].isActive;
        break;
      case 'update_config':
        assignedResources.activeBots[botIndex].config = { 
          ...assignedResources.activeBots[botIndex].config, 
          ...config 
        };
        break;
      case 'update_stats':
        assignedResources.activeBots[botIndex].stats = { 
          ...assignedResources.activeBots[botIndex].stats, 
          ...config,
          lastActivity: new Date().toISOString()
        };
        break;
    }

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources)
      }
    );

    return NextResponse.json({
      success: true,
      bot: assignedResources.activeBots[botIndex]
    });

  } catch (error) {
    console.error('Error updating bot:', error);
    return NextResponse.json(
      { error: 'Failed to update bot' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const { botId } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Удаляем бота
    assignedResources.activeBots = assignedResources.activeBots.filter(
      (bot: any) => bot.id !== botId
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources)
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error deleting bot:', error);
    return NextResponse.json(
      { error: 'Failed to delete bot' },
      { status: 500 }
    );
  }
}
