import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, ORDERS_COLLECTION_ID } from '@/lib/appwrite';

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const { status } = await request.json();

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    const updateData: any = { status };

    // Добавляем временные метки в зависимости от статуса
    if (status === 'completed') {
      updateData.completedAt = new Date().toISOString();
    }

    // Обновляем заказ
    const updatedOrder = await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      updateData
    );

    // Создаем уведомление для клиента
    try {
      const notificationMessages = {
        'in_progress': 'Ваш заказ принят в работу',
        'completed': 'Ваш заказ успешно выполнен',
        'cancelled': 'Ваш заказ был отменен'
      };

      const message = notificationMessages[status as keyof typeof notificationMessages];
      
      if (message) {
        await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: `order_${status}`,
            title: 'Обновление заказа',
            message,
            userId: updatedOrder.userId,
            metadata: {
              orderId: updatedOrder.$id,
              status
            }
          })
        });
      }
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      order: updatedOrder
    });

  } catch (error) {
    console.error('Error updating order status:', error);
    return NextResponse.json(
      { error: 'Failed to update order status' },
      { status: 500 }
    );
  }
}
