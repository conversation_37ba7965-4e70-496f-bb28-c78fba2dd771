import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS } from '@/services/appwriteService';
import { ID } from 'appwrite';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const campaignData = await request.json();

    // Получаем текущий заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    // Парсим текущие назначенные ресурсы
    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Создаем новую email кампанию
    const newCampaign = {
      id: ID.unique(),
      name: campaignData.name,
      type: campaignData.type || 'promotion',
      subject: campaignData.subject,
      content: campaignData.content,
      targetAudience: campaignData.targetAudience || [],
      status: 'draft',
      progress: 0,
      stats: {
        sent: 0,
        opened: 0,
        clicked: 0,
        ctr: 0,
        openRate: 0
      },
      schedule: {
        startDate: campaignData.startDate || new Date().toISOString(),
        frequency: campaignData.frequency || 'once',
        timezone: 'Europe/Moscow'
      },
      settings: {
        trackOpens: true,
        trackClicks: true,
        unsubscribeLink: true
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Добавляем кампанию к ресурсам
    assignedResources.emailCampaigns.push(newCampaign);

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    // Создаем уведомление
    try {
      await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'order_update',
          title: 'Email кампания создана',
          message: `К вашему заказу добавлена email кампания: ${campaignData.name}`,
          userId: order.userId,
          metadata: {
            orderId,
            campaignId: newCampaign.id,
            campaignName: campaignData.name
          }
        })
      });
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      campaign: newCampaign
    });

  } catch (error) {
    console.error('Error creating email campaign:', error);
    return NextResponse.json(
      { error: 'Failed to create email campaign' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { emailCampaigns: [] };

    return NextResponse.json({
      emailCampaigns: assignedResources.emailCampaigns || []
    });

  } catch (error) {
    console.error('Error fetching email campaigns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email campaigns' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const { campaignId } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Удаляем кампанию
    assignedResources.emailCampaigns = assignedResources.emailCampaigns.filter(
      (campaign: any) => campaign.id !== campaignId
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error deleting email campaign:', error);
    return NextResponse.json(
      { error: 'Failed to delete email campaign' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const { campaignId, ...updateData } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Обновляем кампанию
    assignedResources.emailCampaigns = assignedResources.emailCampaigns.map(
      (campaign: any) => campaign.id === campaignId 
        ? { ...campaign, ...updateData, updatedAt: new Date().toISOString() }
        : campaign
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error updating email campaign:', error);
    return NextResponse.json(
      { error: 'Failed to update email campaign' },
      { status: 500 }
    );
  }
}
