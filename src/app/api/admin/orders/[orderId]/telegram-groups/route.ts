import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, COLLECTIONS } from '@/services/appwriteService';
import { ID } from 'appwrite';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const groupData = await request.json();

    // Получаем текущий заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    // Парсим текущие назначенные ресурсы
    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Создаем новую Telegram группу
    const newGroup = {
      id: ID.unique(),
      name: groupData.name,
      username: groupData.username,
      type: groupData.type || 'channel', // channel, group, supergroup
      category: groupData.category || 'music',
      description: groupData.description || '',
      members: groupData.members || 0,
      activity: groupData.activity || 0,
      isActive: true,
      settings: {
        autoPost: groupData.autoPost || false,
        postFrequency: groupData.postFrequency || 'daily',
        contentTypes: groupData.contentTypes || ['music', 'news'],
        moderationLevel: groupData.moderationLevel || 'medium'
      },
      credentials: {
        botToken: groupData.botToken || '',
        chatId: groupData.chatId || '',
        apiHash: groupData.apiHash || ''
      },
      stats: {
        postsCount: 0,
        avgViews: 0,
        avgReactions: 0,
        lastActivity: new Date().toISOString()
      },
      targetAudience: groupData.targetAudience || [],
      languages: groupData.languages || ['ru'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Добавляем группу к ресурсам
    assignedResources.telegramGroups.push(newGroup);

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    // Создаем уведомление
    try {
      await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'order_update',
          title: 'Telegram группа добавлена',
          message: `К вашему заказу добавлена Telegram группа: ${groupData.name}`,
          userId: order.userId,
          metadata: {
            orderId,
            groupId: newGroup.id,
            groupName: groupData.name
          }
        })
      });
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      group: newGroup
    });

  } catch (error) {
    console.error('Error creating telegram group:', error);
    return NextResponse.json(
      { error: 'Failed to create telegram group' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { telegramGroups: [] };

    return NextResponse.json({
      telegramGroups: assignedResources.telegramGroups || []
    });

  } catch (error) {
    console.error('Error fetching telegram groups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch telegram groups' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const { groupId } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Удаляем группу
    assignedResources.telegramGroups = assignedResources.telegramGroups.filter(
      (group: any) => group.id !== groupId
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error deleting telegram group:', error);
    return NextResponse.json(
      { error: 'Failed to delete telegram group' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;
    const { groupId, ...updateData } = await request.json();

    // Получаем заказ
    const order = await databases.getDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId
    );

    const assignedResources = order.assignedResources ? 
      JSON.parse(order.assignedResources) : 
      { socialAccounts: [], activeBots: [], emailCampaigns: [], telegramGroups: [] };

    // Обновляем группу
    assignedResources.telegramGroups = assignedResources.telegramGroups.map(
      (group: any) => group.id === groupId 
        ? { ...group, ...updateData, updatedAt: new Date().toISOString() }
        : group
    );

    // Обновляем заказ
    await databases.updateDocument(
      DATABASE_ID,
      COLLECTIONS.ORDERS,
      orderId,
      {
        assignedResources: JSON.stringify(assignedResources),
        updatedAt: new Date().toISOString()
      }
    );

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error updating telegram group:', error);
    return NextResponse.json(
      { error: 'Failed to update telegram group' },
      { status: 500 }
    );
  }
}
