import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, ORDERS_COLLECTION_ID } from '@/lib/appwrite';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params;

    // Получаем заказ из базы данных
    const order = await databases.getDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId
    );

    // Преобразуем данные в нужный формат
    const orderData = {
      id: order.$id,
      userId: order.userId,
      pricingTierId: order.pricingTierId,
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      paymentId: order.paymentId,
      amount: order.amount,
      currency: order.currency,
      createdAt: order.createdAt,
      paidAt: order.paidAt,
      completedAt: order.completedAt,
      
      clientInfo: {
        name: order.clientName,
        email: order.clientEmail,
        phone: order.clientPhone,
        telegramUsername: order.clientTelegram
      },
      
      promotionData: {
        artistName: order.artistName,
        trackTitle: order.trackTitle,
        genre: order.genre,
        targetAudience: order.targetAudience ? JSON.parse(order.targetAudience) : [],
        socialLinks: order.socialLinks ? JSON.parse(order.socialLinks) : {},
        assets: order.assets ? JSON.parse(order.assets) : {}
      },
      
      assignedResources: order.assignedResources ? JSON.parse(order.assignedResources) : {
        managerId: null,
        socialAccounts: [],
        activeBots: [],
        emailCampaigns: [],
        telegramGroups: []
      }
    };

    return NextResponse.json(orderData);

  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { error: 'Order not found' },
      { status: 404 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const updates = await request.json();

    // Обновляем заказ
    const updatedOrder = await databases.updateDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      orderId,
      updates
    );

    return NextResponse.json({
      success: true,
      order: updatedOrder
    });

  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}
