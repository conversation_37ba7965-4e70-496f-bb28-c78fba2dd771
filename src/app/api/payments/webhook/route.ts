import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { headers } from 'next/headers';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey) {
  console.warn('STRIPE_SECRET_KEY not found in environment variables');
}

if (!webhookSecret) {
  console.warn('STRIPE_WEBHOOK_SECRET not found in environment variables');
}

const stripe = stripeSecretKey ? new Stripe(stripeSecretKey, {
  apiVersion: '2024-06-20',
}) : null;

export async function POST(request: NextRequest) {
  try {
    // Проверяем наличие Stripe и webhook secret
    if (!stripe || !webhookSecret) {
      return NextResponse.json(
        { error: 'Payment service not configured' },
        { status: 503 }
      );
    }

    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Обработка различных типов событий
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);
  
  const orderId = paymentIntent.metadata.orderId;
  const userId = paymentIntent.metadata.userId;

  if (orderId && userId) {
    try {
      // Обновляем статус заказа в базе данных
      // await updateOrderStatus(orderId, 'paid');
      
      // Отправляем уведомление пользователю
      // await sendPaymentSuccessNotification(userId, orderId);
      
      // Создаем запись в истории платежей
      // await createPaymentRecord({
      //   orderId,
      //   userId,
      //   paymentIntentId: paymentIntent.id,
      //   amount: paymentIntent.amount,
      //   currency: paymentIntent.currency,
      //   status: 'succeeded',
      // });

      console.log(`Order ${orderId} marked as paid`);
    } catch (error) {
      console.error('Error handling payment success:', error);
    }
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment failed:', paymentIntent.id);
  
  const orderId = paymentIntent.metadata.orderId;
  const userId = paymentIntent.metadata.userId;

  if (orderId && userId) {
    try {
      // Обновляем статус заказа
      // await updateOrderStatus(orderId, 'payment_failed');
      
      // Отправляем уведомление о неудачном платеже
      // await sendPaymentFailedNotification(userId, orderId);

      console.log(`Order ${orderId} marked as payment failed`);
    } catch (error) {
      console.error('Error handling payment failure:', error);
    }
  }
}

async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment canceled:', paymentIntent.id);
  
  const orderId = paymentIntent.metadata.orderId;

  if (orderId) {
    try {
      // Обновляем статус заказа
      // await updateOrderStatus(orderId, 'canceled');

      console.log(`Order ${orderId} marked as canceled`);
    } catch (error) {
      console.error('Error handling payment cancellation:', error);
    }
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Invoice payment succeeded:', invoice.id);
  
  if (invoice.subscription) {
    try {
      // Обновляем статус подписки
      // await updateSubscriptionStatus(invoice.subscription as string, 'active');
      
      console.log(`Subscription ${invoice.subscription} activated`);
    } catch (error) {
      console.error('Error handling invoice payment:', error);
    }
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Subscription created:', subscription.id);
  
  try {
    // Создаем запись подписки в базе данных
    // await createSubscriptionRecord({
    //   subscriptionId: subscription.id,
    //   customerId: subscription.customer as string,
    //   status: subscription.status,
    //   currentPeriodStart: new Date(subscription.current_period_start * 1000),
    //   currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    // });

    console.log(`Subscription ${subscription.id} created in database`);
  } catch (error) {
    console.error('Error creating subscription record:', error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id);
  
  try {
    // Обновляем запись подписки
    // await updateSubscriptionRecord(subscription.id, {
    //   status: subscription.status,
    //   currentPeriodStart: new Date(subscription.current_period_start * 1000),
    //   currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    // });

    console.log(`Subscription ${subscription.id} updated in database`);
  } catch (error) {
    console.error('Error updating subscription record:', error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id);
  
  try {
    // Помечаем подписку как удаленную
    // await updateSubscriptionRecord(subscription.id, {
    //   status: 'canceled',
    //   canceledAt: new Date(),
    // });

    console.log(`Subscription ${subscription.id} marked as canceled`);
  } catch (error) {
    console.error('Error handling subscription deletion:', error);
  }
}
