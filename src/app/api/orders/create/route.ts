import { NextRequest, NextResponse } from 'next/server';
import { databases, DATABASE_ID, ORDERS_COLLECTION_ID } from '@/lib/appwrite';
import { ID } from 'appwrite';

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      pricingTierId,
      paymentId,
      amount,
      currency,
      clientInfo,
      promotionData
    } = await request.json();

    // Валидация обязательных полей
    if (!userId || !pricingTierId || !amount || !currency) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Создаем заказ в базе данных
    const orderData = {
      userId,
      pricingTierId,
      paymentId: paymentId || null,
      status: paymentId ? 'paid' : 'pending_payment',
      paymentStatus: paymentId ? 'completed' : 'pending',
      paymentMethod: 'card', // По умолчанию, можно передавать в запросе
      amount: Number(amount),
      currency: currency.toUpperCase(),
      createdAt: new Date().toISOString(),
      paidAt: paymentId ? new Date().toISOString() : null,
      
      // Информация о клиенте
      clientName: clientInfo?.name || '',
      clientEmail: clientInfo?.email || '',
      clientPhone: clientInfo?.phone || '',
      clientTelegram: clientInfo?.telegramUsername || '',
      
      // Данные для продвижения
      artistName: promotionData?.artistName || '',
      trackTitle: promotionData?.trackTitle || '',
      genre: promotionData?.genre || '',
      targetAudience: JSON.stringify(promotionData?.targetAudience || []),
      socialLinks: JSON.stringify(promotionData?.socialLinks || {}),
      assets: JSON.stringify(promotionData?.assets || {}),
      
      // Статус обработки
      managerId: null,
      assignedResources: JSON.stringify({
        socialAccounts: [],
        activeBots: [],
        emailCampaigns: [],
        telegramGroups: []
      })
    };

    const order = await databases.createDocument(
      DATABASE_ID,
      ORDERS_COLLECTION_ID,
      ID.unique(),
      orderData
    );

    // Создаем уведомление для админов
    try {
      await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/notifications/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'order_created',
          title: 'Новый заказ',
          message: `Создан новый заказ #${order.$id} на сумму ${amount} ${currency}`,
          userId: 'admin',
          metadata: {
            orderId: order.$id,
            amount,
            currency,
            clientName: clientInfo?.name
          }
        })
      });
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.$id,
        status: order.status,
        amount: order.amount,
        currency: order.currency,
        createdAt: order.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
