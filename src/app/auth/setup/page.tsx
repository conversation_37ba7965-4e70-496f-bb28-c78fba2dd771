'use client';

import Link from 'next/link';
import { FaArrowLeft, FaExternalLinkAlt, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';

export default function AuthSetupPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300 mb-6"
          >
            <FaArrowLeft className="w-4 h-4" />
            Назад к входу
          </Link>
          
          <div className="text-center">
            <Link href="/" className="text-4xl font-bold text-white">
              <span className="text-red-500">H!</span>VE
            </Link>
            <h1 className="text-3xl font-bold text-white mt-4 mb-2">
              Настройка OAuth аутентификации
            </h1>
            <p className="text-gray-400">
              Пошаговая инструкция по настройке Google OAuth в Appwrite
            </p>
          </div>
        </div>

        {/* Alert */}
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 mb-8">
          <div className="flex items-start gap-3">
            <FaExclamationTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-red-400 font-semibold mb-1">
                Ошибка 412: OAuth провайдер отключен
              </h3>
              <p className="text-red-300 text-sm">
                Google OAuth не настроен или отключен в консоли Appwrite. 
                Следуйте инструкциям ниже для настройки.
              </p>
            </div>
          </div>
        </div>

        {/* Steps */}
        <div className="space-y-8">
          {/* Step 1 */}
          <div className="card-monochrome rounded-2xl p-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                1
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-3">
                  Откройте Appwrite Console
                </h3>
                <p className="text-gray-400 mb-4">
                  Перейдите в консоль управления Appwrite и выберите ваш проект.
                </p>
                <a
                  href="https://cloud.appwrite.io"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300"
                >
                  Открыть Appwrite Console
                  <FaExternalLinkAlt className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>

          {/* Step 2 */}
          <div className="card-monochrome rounded-2xl p-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                2
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-3">
                  Перейдите в настройки аутентификации
                </h3>
                <p className="text-gray-400 mb-4">
                  В боковом меню выберите <strong className="text-white">Authentication</strong> → <strong className="text-white">Settings</strong>
                </p>
                <div className="bg-gray-800/50 rounded-lg p-3 border border-gray-700/50">
                  <code className="text-green-400 text-sm">
                    Authentication → Settings → OAuth2 Providers
                  </code>
                </div>
              </div>
            </div>
          </div>

          {/* Step 3 */}
          <div className="card-monochrome rounded-2xl p-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                3
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-3">
                  Включите Google OAuth
                </h3>
                <p className="text-gray-400 mb-4">
                  Найдите <strong className="text-white">Google</strong> в списке OAuth2 провайдеров и включите переключатель.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <FaCheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">Найдите &quot;Google&quot; в списке провайдеров</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <FaCheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">Переключите статус на &quot;Enabled&quot; (зеленый)</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <FaCheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">Введите Google Client ID и Secret</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Step 4 */}
          <div className="card-monochrome rounded-2xl p-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                4
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-3">
                  Настройте Google Cloud Console
                </h3>
                <p className="text-gray-400 mb-4">
                  Если у вас еще нет Google OAuth credentials, создайте их в Google Cloud Console.
                </p>
                <div className="space-y-3 mb-4">
                  <a
                    href="https://console.cloud.google.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300"
                  >
                    Google Cloud Console
                    <FaExternalLinkAlt className="w-4 h-4" />
                  </a>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                  <p className="text-sm text-gray-300 mb-2">
                    <strong>Redirect URI для Appwrite:</strong>
                  </p>
                  <code className="text-green-400 text-xs break-all">
                    https://cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/[PROJECT_ID]
                  </code>
                  <p className="text-xs text-gray-400 mt-2">
                    Замените [PROJECT_ID] на ваш реальный Project ID из Appwrite
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-center gap-3 mb-2">
              <FaCheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-semibold">
                После настройки OAuth
              </span>
            </div>
            <p className="text-green-300 text-sm">
              Вернитесь на страницу входа и попробуйте войти через Google снова
            </p>
          </div>
          
          <div className="space-y-3">
            <Link
              href="/login"
              className="inline-block bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-300"
            >
              Вернуться к входу
            </Link>
            <div>
              <Link
                href="/"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                На главную страницу
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
