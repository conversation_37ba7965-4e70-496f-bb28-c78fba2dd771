'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Mail, CheckCircle, XCircle, RefreshCw, ArrowLeft } from 'lucide-react';
import { account } from '@/lib/appwrite';

function VerifyEmailContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'pending'>('pending');
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const userId = searchParams.get('userId');
  const secret = searchParams.get('secret');

  useEffect(() => {
    if (userId && secret) {
      verifyEmail();
    }
  }, [userId, secret]);

  const verifyEmail = async () => {
    if (!userId || !secret) return;
    
    try {
      setStatus('loading');
      await account.updateVerification(userId, secret);
      setStatus('success');
      setMessage('Email успешно подтвержден! Перенаправляем в личный кабинет...');
      
      // Перенаправляем в дашборд через 3 секунды
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);
    } catch (error: unknown) {
      setStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Ошибка подтверждения email';
      setMessage(errorMessage);
    }
  };

  const resendVerification = async () => {
    try {
      setIsResending(true);
      const verifyUrl = `${window.location.origin}/auth/verify-email`;
      await account.createVerification(verifyUrl);
      setMessage('Письмо с подтверждением отправлено повторно');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка отправки письма';
      setMessage(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center px-4 py-12 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Back Button */}
        <div className="mb-6">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300"
          >
            <ArrowLeft className="w-4 h-4" />
            Назад к входу
          </Link>
        </div>

        {/* Main Card */}
        <div className="card-monochrome rounded-3xl form-padding-lg group text-center">
          <div className="relative z-10">
            {/* Icon */}
            <div className="mb-8 flex justify-center">
              <div className={`p-6 rounded-2xl ${
                status === 'success' 
                  ? 'bg-green-500/20 border border-green-500/30' 
                  : status === 'error'
                  ? 'bg-red-500/20 border border-red-500/30'
                  : status === 'loading'
                  ? 'bg-blue-500/20 border border-blue-500/30'
                  : 'bg-gray-800/50 border border-gray-700/50'
              }`}>
                {status === 'loading' && (
                  <RefreshCw className="w-12 h-12 text-blue-400 animate-spin" />
                )}
                {status === 'success' && (
                  <CheckCircle className="w-12 h-12 text-green-400" />
                )}
                {status === 'error' && (
                  <XCircle className="w-12 h-12 text-red-400" />
                )}
                {status === 'pending' && (
                  <Mail className="w-12 h-12 text-gray-400" />
                )}
              </div>
            </div>

            {/* Title */}
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-4">
              {status === 'loading' && 'Подтверждение email...'}
              {status === 'success' && 'Email подтвержден!'}
              {status === 'error' && 'Ошибка подтверждения'}
              {status === 'pending' && 'Подтвердите email'}
            </h1>

            {/* Message */}
            <p className="text-gray-400 mb-8 leading-relaxed">
              {status === 'pending' && !userId && (
                'Мы отправили письмо с подтверждением на ваш email. Пожалуйста, проверьте почту и перейдите по ссылке.'
              )}
              {message && message}
            </p>

            {/* Actions */}
            <div className="space-y-4">
              {status === 'pending' && (
                <button
                  onClick={resendVerification}
                  disabled={isResending}
                  className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-red-500 hover:to-red-600 disabled:from-gray-600 disabled:to-gray-700 text-white py-4 rounded-xl font-semibold transition-all duration-300 border border-gray-600/50 hover:border-red-500/50 hover:shadow-lg hover:shadow-red-500/25 flex items-center justify-center gap-3"
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="w-5 h-5 animate-spin" />
                      Отправляем...
                    </>
                  ) : (
                    <>
                      <Mail className="w-5 h-5" />
                      Отправить повторно
                    </>
                  )}
                </button>
              )}

              {status === 'success' && (
                <button
                  onClick={() => router.push('/dashboard')}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg shadow-green-500/25 hover:shadow-xl hover:shadow-green-500/40"
                >
                  Перейти в личный кабинет
                </button>
              )}

              {status === 'error' && (
                <div className="space-y-3">
                  <button
                    onClick={resendVerification}
                    disabled={isResending}
                    className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-red-500 hover:to-red-600 text-white py-4 rounded-xl font-semibold transition-all duration-300 border border-gray-600/50 hover:border-red-500/50"
                  >
                    Попробовать снова
                  </button>
                  <Link
                    href="/register"
                    className="block w-full text-center py-4 border border-gray-700/50 rounded-xl text-gray-300 hover:text-white hover:border-red-500/30 transition-all duration-300"
                  >
                    Зарегистрироваться заново
                  </Link>
                </div>
              )}
            </div>

            {/* Help */}
            <div className="mt-8 pt-6 border-t border-gray-700/50">
              <p className="text-sm text-gray-500 mb-2">
                Не получили письмо?
              </p>
              <p className="text-xs text-gray-600">
                Проверьте папку &quot;Спам&quot; или свяжитесь с поддержкой
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Загрузка...</div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}
