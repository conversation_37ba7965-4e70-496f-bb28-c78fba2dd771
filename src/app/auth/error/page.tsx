'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { 
  AlertCircle, 
  RefreshCw, 
  ArrowLeft, 
  Mail,
  Shield,
  Clock,
  XCircle
} from 'lucide-react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  const getErrorInfo = (errorType: string | null) => {
    switch (errorType) {
      case 'oauth_failed':
        return {
          icon: <XCircle className="w-8 h-8 text-red-500" />,
          title: 'Ошибка OAuth авторизации',
          description: 'Не удалось войти через внешний сервис. Попробуйте еще раз или используйте другой способ входа.',
          color: 'red'
        };
      case 'auth_failed':
        return {
          icon: <AlertCircle className="w-8 h-8 text-red-500" />,
          title: 'Ошибка авторизации',
          description: 'Произошла ошибка при входе в систему. Проверьте ваши данные и попробуйте снова.',
          color: 'red'
        };
      case 'email_not_verified':
        return {
          icon: <Mail className="w-8 h-8 text-yellow-500" />,
          title: 'Email не подтвержден',
          description: 'Для входа в систему необходимо подтвердить ваш email адрес.',
          color: 'yellow'
        };
      case 'account_disabled':
        return {
          icon: <Shield className="w-8 h-8 text-red-500" />,
          title: 'Аккаунт заблокирован',
          description: 'Ваш аккаунт временно заблокирован. Обратитесь в службу поддержки.',
          color: 'red'
        };
      case 'session_expired':
        return {
          icon: <Clock className="w-8 h-8 text-orange-500" />,
          title: 'Сессия истекла',
          description: 'Ваша сессия истекла. Пожалуйста, войдите в систему заново.',
          color: 'orange'
        };
      default:
        return {
          icon: <AlertCircle className="w-8 h-8 text-red-500" />,
          title: 'Неизвестная ошибка',
          description: 'Произошла неожиданная ошибка. Попробуйте еще раз или обратитесь в поддержку.',
          color: 'red'
        };
    }
  };

  const errorInfo = getErrorInfo(error);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          <div className="text-center">
            {/* Icon */}
            <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto bg-${errorInfo.color}-500/20`} style={{ marginBottom: '20px' }}>
              {errorInfo.icon}
            </div>
            
            {/* Title */}
            <h1 className="text-2xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
              {errorInfo.title}
            </h1>
            
            {/* Description */}
            <p className="text-gray-400" style={{ marginBottom: '20px', lineHeight: '1.6' }}>
              {errorInfo.description}
            </p>

            {/* Error Details */}
            {errorDescription && (
              <div className="bg-gray-800/50 border border-gray-700 rounded-lg" style={{ padding: '16px', marginBottom: '30px' }}>
                <p className="text-gray-300 text-sm" style={{ lineHeight: '1.6' }}>
                  <strong>Детали ошибки:</strong><br />
                  {errorDescription}
                </p>
              </div>
            )}

            {/* Actions */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '30px' }}>
              {/* Primary Action */}
              {error === 'email_not_verified' ? (
                <Link
                  href="/auth/verify-email"
                  className="w-full bg-yellow-500 hover:bg-yellow-600 text-black rounded-lg transition-colors font-semibold flex items-center justify-center"
                  style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
                >
                  <Mail className="w-5 h-5" />
                  Подтвердить email
                </Link>
              ) : (
                <Link
                  href="/auth/login"
                  className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
                  style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
                >
                  <RefreshCw className="w-5 h-5" />
                  Попробовать снова
                </Link>
              )}

              {/* Secondary Actions */}
              <div style={{ display: 'flex', gap: '8px' }}>
                <Link
                  href="/auth/login"
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium text-center"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Вход
                </Link>
                
                <Link
                  href="/auth/register"
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium text-center"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Регистрация
                </Link>
              </div>
            </div>

            {/* Help */}
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg" style={{ padding: '16px', marginBottom: '20px' }}>
              <p className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
                💡 Если проблема повторяется, обратитесь в службу поддержки
              </p>
            </div>

            {/* Support Link */}
            <Link 
              href="/support" 
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Связаться с поддержкой
            </Link>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            <ArrowLeft className="w-4 h-4" />
            Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
