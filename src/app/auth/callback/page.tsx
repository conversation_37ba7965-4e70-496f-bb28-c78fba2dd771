'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading, refreshSession } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Проверяем, есть ли ошибка в URL параметрах
        const errorParam = searchParams.get('error');
        if (errorParam) {
          router.push('/login?error=oauth_failed');
          return;
        }

        // Обновляем сессию после OAuth редиректа
        await refreshSession();

        // Даем время на обновление состояния
        setTimeout(() => {
          setIsProcessing(false);
        }, 1000);
      } catch (error) {
        console.error('OAuth callback error:', error);
        router.push('/login?error=auth_failed');
      }
    };

    handleCallback();
  }, [searchParams, refreshSession, router]);

  useEffect(() => {
    // Проверяем, есть ли пользователь после обработки OAuth
    if (!isProcessing && !loading) {
      if (user) {
        // Успешная аутентификация - перенаправляем в дашборд
        router.push('/dashboard');
      } else {
        // Ошибка аутентификации - возвращаем на страницу логина
        router.push('/login?error=auth_failed');
      }
    }
  }, [user, loading, isProcessing, router]);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-white mb-2">Завершение входа...</h2>
        <p className="text-gray-400">Пожалуйста, подождите</p>
      </div>
    </div>
  );
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Загрузка...</div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}
