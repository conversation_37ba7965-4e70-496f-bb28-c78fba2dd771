'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Mail, 
  ArrowRight, 
  AlertCircle,
  CheckCircle,
  Loader2,
  ArrowLeft
} from 'lucide-react';

export default function ForgotPasswordPage() {
  const { sendPasswordRecovery } = useAuth();
  const router = useRouter();
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // URL для сброса пароля
      const resetUrl = `${window.location.origin}/auth/reset-password`;
      await sendPasswordRecovery(email, resetUrl);
      setSuccess(true);
    } catch (error: unknown) {
      console.error('Password recovery error:', error);
      setError((error as Error).message || 'Ошибка отправки письма. Попробуйте еще раз.');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 w-full max-w-md">
          <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
              
              <h1 className="text-2xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                Письмо отправлено!
              </h1>
              
              <p className="text-gray-400" style={{ marginBottom: '20px', lineHeight: '1.6' }}>
                Мы отправили инструкции по восстановлению пароля на адрес:
              </p>
              
              <p className="text-white font-medium" style={{ marginBottom: '30px' }}>
                {email}
              </p>
              
              <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg" style={{ padding: '16px', marginBottom: '30px' }}>
                <p className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
                  💡 Проверьте папку "Спам", если письмо не пришло в течение нескольких минут
                </p>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <Link
                  href="/auth/login"
                  className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
                  style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
                >
                  Вернуться к входу
                  <ArrowRight className="w-5 h-5" />
                </Link>
                
                <button
                  onClick={() => {
                    setSuccess(false);
                    setEmail('');
                  }}
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Отправить еще раз
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Восстановление пароля
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Введите email для получения инструкций по сбросу пароля
          </p>
        </div>

        {/* Form */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error Message */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div style={{ marginBottom: '30px' }}>
              <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                Email адрес
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                  <Mail className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="<EMAIL>"
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                  style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
              style={{ padding: '14px', marginBottom: '25px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Отправка...
                </>
              ) : (
                <>
                  Отправить инструкции
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </form>

          {/* Links */}
          <div className="text-center">
            <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
              Вспомнили пароль?{' '}
              <Link 
                href="/auth/login" 
                className="text-red-400 hover:text-red-300 transition-colors font-medium"
              >
                Войти
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Login */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/auth/login" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            <ArrowLeft className="w-4 h-4" />
            Вернуться к входу
          </Link>
        </div>
      </div>
    </div>
  );
}
