'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Shield, 
  User, 
  Mail, 
  Lock, 
  ArrowRight, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Crown
} from 'lucide-react';

export default function CreateAdminPage() {
  const { register, updatePreferences } = useAuth();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    adminKey: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Секретный ключ для создания админа (в продакшене должен быть в переменных окружения)
  const ADMIN_SECRET_KEY = 'HIVE_ADMIN_2024_SECRET';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Проверяем секретный ключ
      if (formData.adminKey !== ADMIN_SECRET_KEY) {
        throw new Error('Неверный секретный ключ администратора');
      }

      // Проверяем совпадение паролей
      if (formData.password !== formData.confirmPassword) {
        throw new Error('Пароли не совпадают');
      }

      if (formData.password.length < 8) {
        throw new Error('Пароль должен содержать минимум 8 символов');
      }

      // Регистрируем пользователя как админа
      await register(formData.email, formData.password, formData.name, 'artist');
      
      // Устанавливаем роль администратора
      await updatePreferences({ 
        type: 'admin',
        role: 'administrator',
        permissions: ['admin_panel', 'manage_orders', 'manage_users', 'view_analytics'],
        createdAt: new Date().toISOString()
      });

      setSuccess('Администратор успешно создан! Перенаправляем в админ панель...');
      
      setTimeout(() => {
        router.push('/admin');
      }, 2000);

    } catch (error: unknown) {
      console.error('Admin creation error:', error);
      setError((error as Error).message || 'Ошибка создания администратора');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
            <Crown className="w-8 h-8 text-purple-500" />
          </div>
          
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Создание администратора
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Создайте аккаунт администратора для управления системой
          </p>
        </div>

        {/* Form */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error/Success Messages */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg text-green-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', marginBottom: '30px' }}>
              {/* Admin Key */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Секретный ключ администратора
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Shield className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    name="adminKey"
                    value={formData.adminKey}
                    onChange={handleInputChange}
                    required
                    placeholder="Введите секретный ключ"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Имя администратора
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <User className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder="Введите имя"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Email адрес
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Mail className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="<EMAIL>"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Пароль
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    minLength={8}
                    placeholder="Минимум 8 символов"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Подтвердите пароль
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                    placeholder="Повторите пароль"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
              style={{ padding: '14px', marginBottom: '25px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Создание администратора...
                </>
              ) : (
                <>
                  <Crown className="w-5 h-5" />
                  Создать администратора
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </form>

          {/* Info */}
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg" style={{ padding: '16px', marginBottom: '20px' }}>
            <p className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
              💡 Секретный ключ: <code className="bg-blue-500/20 px-2 py-1 rounded">HIVE_ADMIN_2024_SECRET</code>
            </p>
          </div>

          {/* Links */}
          <div className="text-center">
            <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
              Уже есть аккаунт администратора?{' '}
              <Link 
                href="/auth/login" 
                className="text-purple-400 hover:text-purple-300 transition-colors font-medium"
              >
                Войти
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            ← Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
