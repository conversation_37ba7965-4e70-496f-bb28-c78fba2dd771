'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Mail, Lock, ArrowLeft, Eye, EyeOff, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

function ResetPasswordContent() {
  const { updatePasswordRecovery } = useAuth();
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();

  const userId = searchParams.get('userId');
  const secret = searchParams.get('secret');

  useEffect(() => {
    // Проверяем наличие необходимых параметров
    if (!userId || !secret) {
      setError('Неверная ссылка для сброса пароля. Запросите новую ссылку.');
    }
  }, [userId, secret]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError('');
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }

    if (formData.password.length < 8) {
      setError('Пароль должен содержать минимум 8 символов');
      return;
    }

    if (!userId || !secret) {
      setError('Неверная ссылка для сброса пароля');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await updatePasswordRecovery(userId, secret, formData.password);
      setSuccess(true);

      // Перенаправляем через 3 секунды
      setTimeout(() => {
        router.push('/auth/login?message=password_reset_success');
      }, 3000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка сброса пароля. Попробуйте еще раз.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Success state
  if (success) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 w-full max-w-md">
          <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>

              <h1 className="text-2xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                Пароль изменен!
              </h1>

              <p className="text-gray-400" style={{ marginBottom: '30px', lineHeight: '1.6' }}>
                Ваш пароль успешно изменен. Сейчас вы будете перенаправлены на страницу входа.
              </p>

              <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg" style={{ padding: '16px', marginBottom: '30px' }}>
                <p className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
                  💡 Теперь вы можете войти в систему с новым паролем
                </p>
              </div>

              <Link
                href="/auth/login"
                className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
                style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
              >
                Перейти к входу
                <ArrowLeft className="w-5 h-5 rotate-180" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Новый пароль
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Введите новый пароль для вашего аккаунта
          </p>
        </div>

        {/* Form */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error Message */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Invalid link message */}
          {(!userId || !secret) && (
            <div className="text-center" style={{ marginBottom: '30px' }}>
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>

              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                Неверная ссылка
              </h2>

              <p className="text-gray-400" style={{ marginBottom: '30px', lineHeight: '1.6' }}>
                Ссылка для сброса пароля недействительна или устарела. Запросите новую ссылку.
              </p>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <Link
                  href="/auth/forgot-password"
                  className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
                  style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
                >
                  Запросить новую ссылку
                </Link>

                <Link
                  href="/auth/login"
                  className="w-full bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Вернуться к входу
                </Link>
              </div>
            </div>
          )}

          {/* Reset Password Form */}
          {userId && secret && (
            <form onSubmit={handleResetPassword}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', marginBottom: '30px' }}>
                {/* New Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Новый пароль
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                      <Lock className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                      minLength={8}
                      placeholder="Минимум 8 символов"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                      style={{ padding: '14px 44px 14px 44px', lineHeight: '1.4' }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center text-gray-400 hover:text-white transition-colors"
                      style={{ paddingRight: '12px' }}
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* Confirm Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Подтвердите пароль
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                      <Lock className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required
                      placeholder="Повторите пароль"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                      style={{ padding: '14px 44px 14px 44px', lineHeight: '1.4' }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute inset-y-0 right-0 flex items-center text-gray-400 hover:text-white transition-colors"
                      style={{ paddingRight: '12px' }}
                    >
                      {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
                style={{ padding: '14px', marginBottom: '25px', gap: '8px', lineHeight: '1.4' }}
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Изменение пароля...
                  </>
                ) : (
                  <>
                    <Lock className="w-5 h-5" />
                    Изменить пароль
                  </>
                )}
              </button>
            </form>
          )}

          {/* Links */}
          <div className="text-center">
            <Link
              href="/auth/login"
              className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
              style={{ gap: '8px' }}
            >
              <ArrowLeft className="w-4 h-4" />
              Вернуться к входу
            </Link>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link
            href="/"
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            ← Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Загрузка...</div>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}
