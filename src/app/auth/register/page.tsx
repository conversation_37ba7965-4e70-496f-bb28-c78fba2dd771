'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  User, 
  ArrowRight, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Music,
  Building
} from 'lucide-react';
import { FaGoogle, FaTelegram } from 'react-icons/fa';

const RegisterPage = () => {
  const { register, loginWithGoogle, loginWithTelegram, user, loading } = useAuth();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    userType: 'artist' as 'artist' | 'label'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (user && !loading) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');

    // Валидация
    if (formData.password !== formData.confirmPassword) {
      setError('Пароли не совпадают');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 8) {
      setError('Пароль должен содержать минимум 8 символов');
      setIsLoading(false);
      return;
    }

    try {
      await register(formData.email, formData.password, formData.name, formData.userType);
      setSuccess('Регистрация успешна! Перенаправляем в дашборд...');
      setTimeout(() => {
        router.push('/dashboard');
      }, 1500);
    } catch (error: unknown) {
      console.error('Registration error:', error);
      setError((error as Error).message || 'Ошибка регистрации. Попробуйте еще раз.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setError('');
      await loginWithGoogle();
    } catch (error: unknown) {
      console.error('Google login error:', error);
      setError('Ошибка входа через Google');
    }
  };

  const handleTelegramLogin = async () => {
    try {
      setError('');
      await loginWithTelegram();
    } catch (error: unknown) {
      console.error('Telegram login error:', error);
      setError('Ошибка входа через Telegram');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Присоединяйтесь к <span className="text-red-500">HIVE</span>
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Создайте аккаунт и начните продвигать свою музыку
          </p>
        </div>

        {/* Register Form */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error/Success Messages */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg text-green-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', marginBottom: '30px' }}>
              {/* User Type */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Тип аккаунта
                </label>
                <div className="grid grid-cols-2" style={{ gap: '12px' }}>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, userType: 'artist' }))}
                    className={`flex items-center justify-center rounded-lg border transition-all ${
                      formData.userType === 'artist'
                        ? 'bg-red-500/20 border-red-500 text-red-400'
                        : 'bg-gray-800/50 border-gray-700 text-gray-400 hover:border-gray-600'
                    }`}
                    style={{ padding: '12px', gap: '8px' }}
                  >
                    <Music className="w-5 h-5" />
                    <span className="text-sm font-medium">Артист</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, userType: 'label' }))}
                    className={`flex items-center justify-center rounded-lg border transition-all ${
                      formData.userType === 'label'
                        ? 'bg-red-500/20 border-red-500 text-red-400'
                        : 'bg-gray-800/50 border-gray-700 text-gray-400 hover:border-gray-600'
                    }`}
                    style={{ padding: '12px', gap: '8px' }}
                  >
                    <Building className="w-5 h-5" />
                    <span className="text-sm font-medium">Лейбл</span>
                  </button>
                </div>
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Имя {formData.userType === 'artist' ? 'артиста' : 'лейбла'}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <User className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder={formData.userType === 'artist' ? 'Ваше имя или псевдоним' : 'Название лейбла'}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Email адрес
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Mail className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="<EMAIL>"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                    style={{ padding: '14px 16px 14px 44px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Пароль
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    placeholder="Минимум 8 символов"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                    style={{ padding: '14px 44px 14px 44px', lineHeight: '1.4' }}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center text-gray-400 hover:text-white transition-colors"
                    style={{ paddingRight: '12px' }}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-gray-300" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Подтвердите пароль
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center" style={{ paddingLeft: '12px' }}>
                    <Lock className="w-5 h-5 text-gray-400" />
                  </div>
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                    placeholder="Повторите пароль"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                    style={{ padding: '14px 44px 14px 44px', lineHeight: '1.4' }}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 flex items-center text-gray-400 hover:text-white transition-colors"
                    style={{ paddingRight: '12px' }}
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
              style={{ padding: '14px', marginBottom: '25px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Регистрация...
                </>
              ) : (
                <>
                  Создать аккаунт
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </form>

          {/* Divider */}
          <div className="relative" style={{ marginBottom: '25px' }}>
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-gray-900 text-gray-400" style={{ padding: '0 16px' }}>или</span>
            </div>
          </div>

          {/* OAuth Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '25px' }}>
            <button
              onClick={handleGoogleLogin}
              className="w-full bg-white hover:bg-gray-100 text-gray-900 rounded-lg transition-colors font-medium flex items-center justify-center"
              style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
            >
              <FaGoogle className="w-5 h-5" />
              Регистрация через Google
            </button>

            <button
              onClick={handleTelegramLogin}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
              style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
            >
              <FaTelegram className="w-5 h-5" />
              Регистрация через Telegram
            </button>
          </div>

          {/* Links */}
          <div className="text-center">
            <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
              Уже есть аккаунт?{' '}
              <Link 
                href="/auth/login" 
                className="text-red-400 hover:text-red-300 transition-colors font-medium"
              >
                Войти
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            ← Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
