'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import {
  User,
  Mail,
  Lock,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  ArrowRight,
  Home
} from 'lucide-react';

export default function TestAuthPage() {
  // Добавляем утилиты в window для консоли браузера
  useEffect(() => {
    import('@/utils/createAdmin').then(module => {
      (window as any).createAdmin = module.quickCreateAdmin;
      (window as any).createCustomAdmin = module.createAdminUser;
      console.log('🔧 Admin utilities loaded! Use in console:');
      console.log('- createAdmin() - creates <EMAIL> / admin123456');
      console.log('- createCustomAdmin({email, password, name}) - creates custom admin');
    });
  }, []);

  const testScenarios = [
    {
      title: 'Регистрация',
      description: 'Создание нового аккаунта',
      link: '/auth/register',
      icon: <User className="w-6 h-6" />,
      color: 'blue'
    },
    {
      title: 'Вход в систему',
      description: 'Авторизация существующего пользователя',
      link: '/auth/login',
      icon: <Lock className="w-6 h-6" />,
      color: 'green'
    },
    {
      title: 'Забыли пароль',
      description: 'Запрос на восстановление пароля',
      link: '/auth/forgot-password',
      icon: <Mail className="w-6 h-6" />,
      color: 'yellow'
    },
    {
      title: 'Сброс пароля',
      description: 'Установка нового пароля (требует токен)',
      link: '/auth/reset-password?userId=test&secret=test',
      icon: <RefreshCw className="w-6 h-6" />,
      color: 'orange'
    },
    {
      title: 'Подтверждение email',
      description: 'Верификация email адреса',
      link: '/auth/verify-email',
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'purple'
    },
    {
      title: 'OAuth Callback',
      description: 'Обработка OAuth авторизации',
      link: '/auth/callback',
      icon: <ArrowRight className="w-6 h-6" />,
      color: 'indigo'
    },
    {
      title: 'Ошибка авторизации',
      description: 'Страница ошибок OAuth',
      link: '/auth/error?error=oauth_failed',
      icon: <AlertCircle className="w-6 h-6" />,
      color: 'red'
    },
    {
      title: 'Создание админа',
      description: 'Создание аккаунта администратора',
      link: '/auth/create-admin',
      icon: <User className="w-6 h-6" />,
      color: 'purple'
    },
    {
      title: 'Вход админа',
      description: 'Вход с правами администратора',
      link: '/auth/admin-login',
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'purple'
    },
    {
      title: 'Админ панель',
      description: 'Панель управления (требует права админа)',
      link: '/admin',
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'purple'
    }
  ];

  const errorScenarios = [
    {
      title: 'OAuth Failed',
      link: '/auth/error?error=oauth_failed',
      description: 'Ошибка OAuth авторизации'
    },
    {
      title: 'Auth Failed',
      link: '/auth/error?error=auth_failed',
      description: 'Общая ошибка авторизации'
    },
    {
      title: 'Email Not Verified',
      link: '/auth/error?error=email_not_verified',
      description: 'Email не подтвержден'
    },
    {
      title: 'Account Disabled',
      link: '/auth/error?error=account_disabled',
      description: 'Аккаунт заблокирован'
    },
    {
      title: 'Session Expired',
      link: '/auth/error?error=session_expired',
      description: 'Сессия истекла'
    }
  ];

  return (
    <div className="min-h-screen bg-black" style={{ padding: '30px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '50px' }}>
          <h1 className="text-4xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
            🧪 Тестирование Auth Flow
          </h1>
          <p className="text-gray-400 text-lg" style={{ lineHeight: '1.6' }}>
            Проверка всех сценариев авторизации и регистрации
          </p>
        </div>

        {/* Main Scenarios */}
        <div style={{ marginBottom: '50px' }}>
          <h2 className="text-2xl font-bold text-white" style={{ marginBottom: '30px', textAlign: 'center' }}>
            Основные сценарии
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" style={{ gap: '20px' }}>
            {testScenarios.map((scenario, index) => (
              <Link
                key={index}
                href={scenario.link}
                className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50 hover:border-gray-700 transition-all duration-300 group"
                style={{ padding: '25px' }}
              >
                <div className="text-center">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center mx-auto bg-${scenario.color}-500/20 group-hover:bg-${scenario.color}-500/30 transition-colors`} style={{ marginBottom: '15px' }}>
                    {scenario.icon}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-white group-hover:text-gray-100 transition-colors" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    {scenario.title}
                  </h3>
                  
                  <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors" style={{ lineHeight: '1.5' }}>
                    {scenario.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Error Scenarios */}
        <div style={{ marginBottom: '50px' }}>
          <h2 className="text-2xl font-bold text-white" style={{ marginBottom: '30px', textAlign: 'center' }}>
            Сценарии ошибок
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3" style={{ gap: '15px' }}>
            {errorScenarios.map((scenario, index) => (
              <Link
                key={index}
                href={scenario.link}
                className="bg-red-500/10 border border-red-500/30 rounded-xl hover:bg-red-500/20 hover:border-red-500/50 transition-all duration-300 group"
                style={{ padding: '20px' }}
              >
                <div className="flex items-center" style={{ gap: '12px' }}>
                  <AlertCircle className="w-5 h-5 text-red-400 group-hover:text-red-300 transition-colors" />
                  <div>
                    <h3 className="text-white font-medium group-hover:text-red-100 transition-colors" style={{ lineHeight: '1.4' }}>
                      {scenario.title}
                    </h3>
                    <p className="text-red-400 text-sm group-hover:text-red-300 transition-colors" style={{ lineHeight: '1.4' }}>
                      {scenario.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-500/20 border border-blue-500/30 rounded-2xl" style={{ padding: '30px', marginBottom: '30px' }}>
          <h3 className="text-xl font-bold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
            📋 Инструкции по тестированию
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
            <div>
              <h4 className="text-blue-300 font-semibold" style={{ marginBottom: '10px' }}>
                Что проверить:
              </h4>
              <ul className="text-blue-200 text-sm space-y-1">
                <li>• Корректность отображения форм</li>
                <li>• Валидация полей ввода</li>
                <li>• Обработка ошибок</li>
                <li>• Переходы между страницами</li>
                <li>• Responsive дизайн</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-blue-300 font-semibold" style={{ marginBottom: '10px' }}>
                Создание админа:
              </h4>
              <ul className="text-blue-200 text-sm space-y-1">
                <li>• Используйте форму "Создание админа"</li>
                <li>• Секретный ключ: <code className="bg-blue-500/20 px-1 rounded">HIVE_ADMIN_2024_SECRET</code></li>
                <li>• Или в консоли браузера: <code className="bg-blue-500/20 px-1 rounded">createAdmin()</code></li>
                <li>• Создает: <EMAIL> / admin123456</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center">
          <Link 
            href="/" 
            className="inline-flex items-center bg-gray-800 hover:bg-gray-700 text-white rounded-xl transition-colors font-medium"
            style={{ padding: '12px 24px', gap: '8px' }}
          >
            <Home className="w-5 h-5" />
            Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
