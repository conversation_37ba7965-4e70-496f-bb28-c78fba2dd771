'use client';

import { useState } from 'react';
import { account, ID } from '@/lib/appwrite';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Crown, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  ArrowRight
} from 'lucide-react';

export default function QuickAdminPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const logoutAndRetry = async () => {
    setIsLoggingOut(true);
    setError('');

    try {
      await account.deleteSession('current');
      setSuccess('Сессия завершена. Теперь можно создать админа.');
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error: any) {
      setError(`Ошибка выхода: ${error.message}`);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const createQuickAdmin = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const adminData = {
        email: '<EMAIL>',
        password: 'admin123456',
        name: 'HIVE Admin'
      };

      console.log('Creating admin user...', adminData);

      // 1. Создаем аккаунт
      const userId = ID.unique();
      const user = await account.create(userId, adminData.email, adminData.password, adminData.name);
      console.log('User created:', user);

      // 2. Логинимся
      await account.createEmailPasswordSession(adminData.email, adminData.password);
      console.log('Session created');

      // 3. Устанавливаем права администратора
      await account.updatePrefs({
        type: 'admin',
        role: 'administrator',
        permissions: ['admin_panel', 'manage_orders', 'manage_users', 'view_analytics', 'manage_settings'],
        createdAt: new Date().toISOString(),
        createdBy: 'quick-admin'
      });
      console.log('Admin preferences set');

      setSuccess(`Админ создан успешно! Email: ${adminData.email}, Пароль: ${adminData.password}`);
      
      setTimeout(() => {
        router.push('/admin');
      }, 3000);

    } catch (error: any) {
      console.error('Error creating admin:', error);
      
      if (error.code === 409) {
        // Пользователь уже существует, попробуем войти
        try {
          // Сначала выходим из текущей сессии
          try {
            await account.deleteSession('current');
          } catch (e) {
            // Игнорируем ошибку если сессии нет
          }

          // Теперь входим как админ
          await account.createEmailPasswordSession('<EMAIL>', 'admin123456');
          await account.updatePrefs({
            type: 'admin',
            role: 'administrator',
            permissions: ['admin_panel', 'manage_orders', 'manage_users', 'view_analytics', 'manage_settings'],
            updatedAt: new Date().toISOString()
          });
          setSuccess('Админ уже существует. Вход выполнен и права обновлены!');
          setTimeout(() => {
            router.push('/admin');
          }, 2000);
        } catch (loginError: any) {
          setError(`Админ существует, но не удалось войти: ${loginError.message}`);
        }
      } else {
        setError(`Ошибка создания админа: ${error.message || error}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center" style={{ padding: '20px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '40px' }}>
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
            <Crown className="w-8 h-8 text-purple-500" />
          </div>
          
          <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            Быстрое создание админа
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Создание админа одним кликом для тестирования
          </p>
        </div>

        {/* Content */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '40px' }}>
          {/* Error/Success Messages */}
          {error && (
            <div className="flex items-center bg-red-500/20 border border-red-500/30 rounded-lg text-red-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <AlertCircle className="w-5 h-5" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {success && (
            <div className="flex items-center bg-green-500/20 border border-green-500/30 rounded-lg text-green-400" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm">{success}</span>
            </div>
          )}

          {/* Admin Info */}
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg" style={{ padding: '20px', marginBottom: '30px' }}>
            <h3 className="text-blue-300 font-semibold" style={{ marginBottom: '10px' }}>
              Будет создан админ:
            </h3>
            <div className="space-y-2">
              <p className="text-blue-200 text-sm">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-blue-200 text-sm">
                <strong>Пароль:</strong> admin123456
              </p>
              <p className="text-blue-200 text-sm">
                <strong>Имя:</strong> HIVE Admin
              </p>
              <p className="text-blue-200 text-sm">
                <strong>Права:</strong> Полные права администратора
              </p>
            </div>
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '25px' }}>
            <button
              onClick={createQuickAdmin}
              disabled={isLoading || isLoggingOut}
              className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white rounded-lg transition-colors font-semibold flex items-center justify-center"
              style={{ padding: '14px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Создание админа...
                </>
              ) : (
                <>
                  <Crown className="w-5 h-5" />
                  Создать админа
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>

            <button
              onClick={logoutAndRetry}
              disabled={isLoading || isLoggingOut}
              className="w-full bg-gray-700 hover:bg-gray-600 disabled:bg-gray-700/50 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
              style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
            >
              {isLoggingOut ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Выход из сессии...
                </>
              ) : (
                <>
                  Выйти и попробовать снова
                </>
              )}
            </button>
          </div>

          {/* Links */}
          <div className="text-center space-y-2">
            <p className="text-gray-400 text-sm">
              <Link 
                href="/test-appwrite" 
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                Протестировать Appwrite
              </Link>
              {' • '}
              <Link 
                href="/test-auth" 
                className="text-purple-400 hover:text-purple-300 transition-colors"
              >
                Тесты авторизации
              </Link>
            </p>
            
            <p className="text-gray-400 text-sm">
              <Link 
                href="/admin" 
                className="text-green-400 hover:text-green-300 transition-colors"
              >
                Перейти в админ панель
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center" style={{ marginTop: '30px' }}>
          <Link 
            href="/" 
            className="text-gray-400 hover:text-white transition-colors text-sm flex items-center justify-center"
            style={{ gap: '8px' }}
          >
            ← Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
