'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { FaEye, FaEyeSlash, FaGoogle, FaUser, FaMusic, FaArrowLeft, FaTelegram } from 'react-icons/fa';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    type: 'artist' as 'artist' | 'label',
    terms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [language] = useState('ru');

  const { register, loginWithGoogle, loginWithTelegram, loading } = useAuth();
  const router = useRouter();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError(language === 'ru' ? 'Пароли не совпадают' : 'Passwords do not match');
      return;
    }

    if (!formData.terms) {
      setError(language === 'ru' ? 'Необходимо согласиться с условиями' : 'You must agree to the terms');
      return;
    }

    try {
      await register(formData.email, formData.password, formData.name, formData.type);
      router.push('/auth/verify-email');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : (language === 'ru' ? 'Ошибка регистрации' : 'Registration error');
      setError(errorMessage);
    }
  };

  const handleGoogleRegister = async () => {
    try {
      setError('');
      await loginWithGoogle();
      // Редирект произойдет автоматически через OAuth
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка регистрации через Google';
      setError(errorMessage);
    }
  };

  const handleTelegramRegister = async () => {
    try {
      setError('');
      await loginWithTelegram();
      // Редирект произойдет автоматически
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка регистрации через Telegram';
      setError(errorMessage);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center px-4 py-12 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-md w-full relative z-10">
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300"
          >
            <FaArrowLeft className="w-4 h-4" />
            {language === 'ru' ? 'На главную' : 'Back to Home'}
          </Link>
        </div>

        <div className="text-center mb-8">
          <Link href="/" className="text-4xl font-bold text-white">
            <span className="text-red-500">H!</span>VE
          </Link>
          <p className="text-gray-400 mt-2">
            {language === 'ru' ? 'Создайте свой аккаунт' : 'Create your account'}
          </p>
        </div>

        <div className="card-monochrome rounded-3xl form-padding-lg group">
          <div className="relative z-10">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center group-hover:text-red-50 transition-colors duration-300">
              {language === 'ru' ? 'Регистрация' : 'Sign Up'}
            </h1>

            {error && (
              <div className="mb-6 p-4 rounded-xl bg-red-500/10 border border-red-500/30 text-red-400 text-sm">
                <div>{error}</div>
                {(error.includes('провайдер отключен') || error.includes('provider_disabled')) && (
                  <div className="mt-2">
                    <Link
                      href="/auth/setup"
                      className="text-red-300 hover:text-red-200 underline font-medium"
                    >
                      Инструкция по настройке OAuth →
                    </Link>
                  </div>
                )}
              </div>
            )}

            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
              <div className="form-group">
                <label className="form-label">
                  {language === 'ru' ? 'Имя' : 'Name'}
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder={language === 'ru' ? 'Ваше имя' : 'Your name'}
                  className="form-input w-full"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className="form-input w-full"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  {language === 'ru' ? 'Пароль' : 'Password'}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder={language === 'ru' ? 'Минимум 8 символов' : 'Minimum 8 characters'}
                    className="form-input w-full pr-12"
                    required
                    minLength={8}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-300 transition-colors duration-300"
                  >
                    {showPassword ? <FaEyeSlash className="w-5 h-5" /> : <FaEye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">
                  {language === 'ru' ? 'Подтвердите пароль' : 'Confirm Password'}
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder={language === 'ru' ? 'Повторите пароль' : 'Repeat password'}
                    className="form-input w-full pr-12"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-300 transition-colors duration-300"
                  >
                    {showConfirmPassword ? <FaEyeSlash className="w-5 h-5" /> : <FaEye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* User Type Selection */}
              <div className="form-group">
                <label className="form-label">
                  {language === 'ru' ? 'Тип аккаунта' : 'Account Type'}
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <label className={`flex items-center justify-center p-3 rounded-xl border cursor-pointer transition-all duration-300 ${
                    formData.type === 'artist'
                      ? 'border-red-500/50 bg-red-500/10 text-red-400'
                      : 'border-gray-700/50 bg-gray-800/30 text-gray-400 hover:border-gray-600/50'
                  }`}>
                    <input
                      type="radio"
                      name="type"
                      value="artist"
                      checked={formData.type === 'artist'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <FaUser className="w-4 h-4 mr-2" />
                    <span className="font-medium">
                      {language === 'ru' ? 'Артист' : 'Artist'}
                    </span>
                  </label>

                  <label className={`flex items-center justify-center p-3 rounded-xl border cursor-pointer transition-all duration-300 ${
                    formData.type === 'label'
                      ? 'border-red-500/50 bg-red-500/10 text-red-400'
                      : 'border-gray-700/50 bg-gray-800/30 text-gray-400 hover:border-gray-600/50'
                  }`}>
                    <input
                      type="radio"
                      name="type"
                      value="label"
                      checked={formData.type === 'label'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <FaMusic className="w-4 h-4 mr-2" />
                    <span className="font-medium">
                      {language === 'ru' ? 'Лейбл' : 'Label'}
                    </span>
                  </label>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="terms"
                  checked={formData.terms}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-red-500 bg-gray-800 border-gray-600 rounded focus:ring-red-500 focus:ring-2"
                  required
                />
                <label className="ml-2 text-sm text-gray-400">
                  {language === 'ru' ? 'Я согласен с ' : 'I agree to the '}
                  <Link href="/terms" className="text-red-400 hover:text-red-300">
                    {language === 'ru' ? 'условиями использования' : 'terms of service'}
                  </Link>
                </label>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full"
              >
                {loading 
                  ? (language === 'ru' ? 'Регистрация...' : 'Creating account...') 
                  : (language === 'ru' ? 'Зарегистрироваться' : 'Create Account')
                }
              </button>
            </form>

            <div className="relative my-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-700/50"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-gray-900/80 text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                  {language === 'ru' ? 'или зарегистрироваться с помощью' : 'or sign up with'}
                </span>
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', marginBottom: '30px' }}>
              <button
                type="button"
                onClick={handleGoogleRegister}
                disabled={loading}
                className="btn-social w-full flex items-center justify-center gap-3"
              >
                <FaGoogle className="w-5 h-5 text-red-400" />
                <span>
                  {language === 'ru' ? 'Продолжить с Google' : 'Continue with Google'}
                </span>
              </button>

              <button
                type="button"
                onClick={handleTelegramRegister}
                disabled={loading}
                className="btn-social w-full flex items-center justify-center gap-3"
              >
                <FaTelegram className="w-5 h-5 text-blue-400" />
                <span>
                  {language === 'ru' ? 'Продолжить с Telegram' : 'Continue with Telegram'}
                </span>
              </button>
            </div>

            <div className="text-center">
              <span className="text-gray-400">
                {language === 'ru' ? 'Уже есть аккаунт?' : 'Already have an account?'}{' '}
              </span>
              <Link
                href="/login"
                className="text-red-400 hover:text-red-300 font-semibold transition-colors duration-300"
              >
                {language === 'ru' ? 'Войти' : 'Sign in'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
