'use client';

import { useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { FaLock, FaCreditCard, FaCheck, FaArrowLeft } from 'react-icons/fa';
import Link from 'next/link';

const PaymentContent = () => {
  const searchParams = useSearchParams();
  const [language] = useState('ru'); // This would come from context in real app
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    name: '',
    country: 'RU',
  });

  const planId = searchParams.get('plan');

  const planDetails = {
    'artist-starter': {
      name: language === 'ru' ? 'Артист - Стартер' : 'Artist - Starter',
      price: 5000,
      period: language === 'ru' ? '/месяц' : '/month',
    },
    'artist-professional': {
      name: language === 'ru' ? 'Артист - Профессионал' : 'Artist - Professional',
      price: 15000,
      period: language === 'ru' ? '/месяц' : '/month',
    },
    'artist-premium': {
      name: language === 'ru' ? 'Артист - Премиум' : 'Artist - Premium',
      price: 30000,
      period: language === 'ru' ? '/месяц' : '/month',
    },
    'track-basic': {
      name: language === 'ru' ? 'Трек - Базовое продвижение' : 'Track - Basic Promotion',
      price: 500,
      period: language === 'ru' ? '/трек' : '/track',
    },
    'track-professional': {
      name: language === 'ru' ? 'Трек - Профессиональное продвижение' : 'Track - Professional Promotion',
      price: 3000,
      period: language === 'ru' ? '/трек' : '/track',
    },
    'track-premium': {
      name: language === 'ru' ? 'Трек - Премиум продвижение' : 'Track - Premium Promotion',
      price: 5000,
      period: language === 'ru' ? '/трек' : '/track',
    },
  };

  const currentPlan = planId ? planDetails[planId as keyof typeof planDetails] : null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Here you would integrate with Stripe
      // For now, we'll simulate a payment process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to success page
      window.location.href = '/payment/success';
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!currentPlan) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl text-white mb-4">
            {language === 'ru' ? 'План не найден' : 'Plan not found'}
          </h1>
          <Link href="/pricing" className="btn-primary">
            {language === 'ru' ? 'Вернуться к тарифам' : 'Back to Pricing'}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black pt-20">
      <div className="container mx-auto px-6 py-16">
        {/* Back Button */}
        <Link 
          href="/pricing" 
          className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300 mb-8"
        >
          <FaArrowLeft />
          {language === 'ru' ? 'Назад к тарифам' : 'Back to Pricing'}
        </Link>

        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="heading-lg text-white mb-4">
              {language === 'ru' ? 'Оформление заказа' : 'Checkout'}
            </h1>
            <p className="text-gray-400">
              {language === 'ru' ? 'Безопасная оплата через Stripe' : 'Secure payment via Stripe'}
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Order Summary */}
            <div className="card-glow rounded-3xl p-8 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-6">
                {language === 'ru' ? 'Детали заказа' : 'Order Summary'}
              </h2>
              
              <div className="bg-gray-800 rounded-2xl p-6 mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">{currentPlan.name}</h3>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">
                    {language === 'ru' ? 'Стоимость:' : 'Price:'}
                  </span>
                  <span className="text-2xl font-bold text-red-500">
                    ${currentPlan.price.toLocaleString()}{currentPlan.period}
                  </span>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-center gap-3 text-green-500">
                  <FaCheck />
                  <span className="text-gray-300">
                    {language === 'ru' ? 'Безопасная оплата' : 'Secure payment'}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-green-500">
                  <FaCheck />
                  <span className="text-gray-300">
                    {language === 'ru' ? 'Мгновенная активация' : 'Instant activation'}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-green-500">
                  <FaCheck />
                  <span className="text-gray-300">
                    {language === 'ru' ? 'Поддержка 24/7' : '24/7 support'}
                  </span>
                </div>
              </div>

              <div className="border-t border-gray-600 pt-6">
                <div className="flex justify-between items-center text-xl font-bold">
                  <span className="text-white">
                    {language === 'ru' ? 'Итого:' : 'Total:'}
                  </span>
                  <span className="text-red-500">
                    ${currentPlan.price.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Payment Form */}
            <div className="card-glow rounded-3xl p-8 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <FaLock className="text-green-500" />
                {language === 'ru' ? 'Платежная информация' : 'Payment Information'}
              </h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-300 mb-2">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-300 mb-2">
                    {language === 'ru' ? 'Имя на карте' : 'Name on Card'}
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-300 mb-2">
                    {language === 'ru' ? 'Номер карты' : 'Card Number'}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="cardNumber"
                      value={formData.cardNumber}
                      onChange={handleInputChange}
                      placeholder="1234 5678 9012 3456"
                      className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 pr-12 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                      required
                    />
                    <FaCreditCard className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 mb-2">
                      {language === 'ru' ? 'Срок действия' : 'Expiry Date'}
                    </label>
                    <input
                      type="text"
                      name="expiryDate"
                      value={formData.expiryDate}
                      onChange={handleInputChange}
                      placeholder="MM/YY"
                      className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">CVV</label>
                    <input
                      type="text"
                      name="cvv"
                      value={formData.cvv}
                      onChange={handleInputChange}
                      placeholder="123"
                      className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-gray-300 mb-2">
                    {language === 'ru' ? 'Страна' : 'Country'}
                  </label>
                  <select
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                  >
                    <option value="RU">
                      {language === 'ru' ? 'Россия' : 'Russia'}
                    </option>
                    <option value="US">
                      {language === 'ru' ? 'США' : 'United States'}
                    </option>
                    <option value="GB">
                      {language === 'ru' ? 'Великобритания' : 'United Kingdom'}
                    </option>
                    <option value="DE">
                      {language === 'ru' ? 'Германия' : 'Germany'}
                    </option>
                  </select>
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading 
                    ? (language === 'ru' ? 'Обработка...' : 'Processing...') 
                    : (language === 'ru' ? `Оплатить $${currentPlan.price.toLocaleString()}` : `Pay $${currentPlan.price.toLocaleString()}`)
                  }
                </button>
              </form>

              <div className="mt-6 text-center text-sm text-gray-400">
                <p>
                  {language === 'ru' 
                    ? 'Ваши данные защищены 256-битным SSL шифрованием'
                    : 'Your data is protected by 256-bit SSL encryption'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PaymentPage = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Загрузка...</div>
      </div>
    }>
      <PaymentContent />
    </Suspense>
  );
};

export default PaymentPage;
