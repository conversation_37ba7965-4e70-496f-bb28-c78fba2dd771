'use client';

import { useState } from 'react';
import Link from 'next/link';
import { FaCheckCircle, FaDownload, FaEnvelope, FaArrowRight } from 'react-icons/fa';

const PaymentSuccessPage = () => {
  const [language] = useState('ru'); // This would come from context in real app

  return (
    <div className="min-h-screen bg-black flex items-center justify-center px-4">
      <div className="max-w-2xl w-full text-center">
        {/* Success Icon */}
        <div className="mb-8">
          <FaCheckCircle className="text-6xl text-green-500 mx-auto animate-pulse-custom" />
        </div>

        {/* Success Message */}
        <h1 className="heading-lg text-white mb-6">
          {language === 'ru' ? 'Оплата прошла успешно!' : 'Payment Successful!'}
        </h1>
        
        <p className="text-xl text-gray-300 mb-8">
          {language === 'ru' 
            ? 'Спасибо за ваш заказ! Мы уже начали работу над продвижением вашей музыки.'
            : 'Thank you for your order! We have already started working on promoting your music.'
          }
        </p>

        {/* Order Details */}
        <div className="card-glow rounded-3xl p-8 border border-gray-700 mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">
            {language === 'ru' ? 'Детали заказа' : 'Order Details'}
          </h2>
          
          <div className="space-y-4 text-left">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">
                {language === 'ru' ? 'Номер заказа:' : 'Order Number:'}
              </span>
              <span className="text-white font-semibold">#HV-2024-001</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">
                {language === 'ru' ? 'Дата:' : 'Date:'}
              </span>
              <span className="text-white">{new Date().toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">
                {language === 'ru' ? 'Статус:' : 'Status:'}
              </span>
              <span className="text-green-500 font-semibold">
                {language === 'ru' ? 'Активен' : 'Active'}
              </span>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="card-glow rounded-3xl p-8 border border-gray-700 mb-8">
          <h2 className="text-2xl font-bold text-white mb-6">
            {language === 'ru' ? 'Что дальше?' : 'What\'s Next?'}
          </h2>
          
          <div className="space-y-6">
            <div className="flex items-start gap-4 text-left">
              <FaEnvelope className="text-red-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-semibold mb-2">
                  {language === 'ru' ? 'Проверьте email' : 'Check Your Email'}
                </h3>
                <p className="text-gray-400">
                  {language === 'ru' 
                    ? 'Мы отправили подтверждение и инструкции на ваш email'
                    : 'We sent confirmation and instructions to your email'
                  }
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 text-left">
              <FaDownload className="text-red-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-semibold mb-2">
                  {language === 'ru' ? 'Загрузите материалы' : 'Upload Your Materials'}
                </h3>
                <p className="text-gray-400">
                  {language === 'ru' 
                    ? 'Загрузите треки и материалы в личном кабинете'
                    : 'Upload your tracks and materials in your dashboard'
                  }
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 text-left">
              <FaArrowRight className="text-red-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-white font-semibold mb-2">
                  {language === 'ru' ? 'Начинаем работу' : 'We Start Working'}
                </h3>
                <p className="text-gray-400">
                  {language === 'ru' 
                    ? 'Наша команда свяжется с вами в течение 24 часов'
                    : 'Our team will contact you within 24 hours'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/dashboard" className="btn-primary text-lg">
            {language === 'ru' ? 'Перейти в кабинет' : 'Go to Dashboard'}
          </Link>
          
          <Link href="/" className="btn-secondary text-lg">
            {language === 'ru' ? 'На главную' : 'Back to Home'}
          </Link>
        </div>

        {/* Support */}
        <div className="mt-12 text-center">
          <p className="text-gray-400 mb-4">
            {language === 'ru' ? 'Нужна помощь?' : 'Need help?'}
          </p>
          <Link 
            href="/contacts" 
            className="text-red-500 hover:text-red-400 transition-colors duration-300"
          >
            {language === 'ru' ? 'Связаться с поддержкой' : 'Contact Support'}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
