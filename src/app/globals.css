@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Design System */
@import '../styles/design-system.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: #000000;
  color: #ffffff;
  overflow-x: hidden;
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
  letter-spacing: 0.01em;
}

/* Unified spacing system */
.section-padding {
  padding: 8rem 0;
}

.section-padding-sm {
  padding: 4rem 0;
}

/* Dashboard responsive spacing */
.dashboard-container {
  padding-left: 30px;
  padding-right: 30px;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding-left: 16px;
    padding-right: 16px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding-left: 12px;
    padding-right: 12px;
  }
}

.section-padding-lg {
  padding: 12rem 0;
}

/* Unified container system */
.container-wide {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 30px; /* 30px on desktop */
}

.container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 30px; /* 30px on desktop */
}

/* Unified card padding system */
.card-padding {
  padding: 2rem; /* 32px - standard card padding */
}

.card-padding-lg {
  padding: 3rem; /* 48px - large card padding */
}

.form-padding {
  padding: 2rem; /* 32px - standard form padding */
}

.form-padding-lg {
  padding: 3rem; /* 48px - large form padding */
}

/* Mobile-first responsive spacing */
@media (max-width: 768px) {
  .container-wide,
  .container-narrow {
    padding: 0 10px; /* 10px on mobile */
  }

  .section-padding {
    padding: 4rem 0; /* Reduced padding on mobile */
  }

  .section-padding-sm {
    padding: 2rem 0;
  }

  .section-padding-lg {
    padding: 6rem 0;
  }

  .card-padding {
    padding: 1.5rem; /* 24px on mobile */
  }

  .card-padding-lg {
    padding: 2rem; /* 32px on mobile */
  }

  .form-padding {
    padding: 1.5rem; /* 24px on mobile */
  }

  .form-padding-lg {
    padding: 2rem; /* 32px on mobile */
  }
}

/* Unified spacing utilities */
.space-y-standard > * + * {
  margin-top: 1.5rem; /* 24px */
}

.space-y-large > * + * {
  margin-top: 2rem; /* 32px */
}

.space-y-xl > * + * {
  margin-top: 3rem; /* 48px */
}

/* Enhanced form input styles */
.form-input {
  height: 3.5rem; /* 56px - более высокие поля */
  padding: 0 1.25rem; /* 20px горизонтальные отступы */
  background: rgba(31, 41, 55, 0.4); /* Более прозрачный фон */
  border: 1px solid rgba(75, 85, 99, 0.3); /* Тонкая граница */
  border-radius: 0.75rem; /* 12px - меньший радиус */
  color: white;
  font-size: 1rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.form-input::placeholder {
  color: rgba(156, 163, 175, 0.7);
  font-weight: 400;
}

.form-input:focus {
  outline: none;
  background: rgba(31, 41, 55, 0.6);
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input:hover {
  border-color: rgba(75, 85, 99, 0.5);
}

/* Enhanced form labels */
.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: rgba(156, 163, 175, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.form-group:hover .form-label {
  color: rgba(229, 231, 235, 0.9);
}

/* Enhanced buttons */
.btn-primary {
  height: 3.5rem; /* 56px - такая же высота как поля */
  padding: 0 2rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: none;
  transform: none;
  cursor: not-allowed;
}

/* Enhanced social buttons */
.btn-social {
  height: 3.5rem; /* 56px */
  padding: 0 1.5rem;
  background: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 0.75rem;
  color: white;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.btn-social:hover {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.btn-social:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Improved spacing */
.space-y-xl > * + * {
  margin-top: 3rem;
}

/* Modern Style Effects */
.modern-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 25%, #a855f7 50%, #ec4899 75%, #f472b6 100%);
}

.modern-logo-glow {
  filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.3));
}

.modern-button {
  background: linear-gradient(45deg, #8b5cf6, #a855f7);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.space-y-2xl > * + * {
  margin-top: 4rem;
}

.space-y-3xl > * + * {
  margin-top: 6rem;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(239, 68, 68, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes neonGlow {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(239, 68, 68, 0.8),
      0 0 10px rgba(239, 68, 68, 0.8),
      0 0 15px rgba(239, 68, 68, 0.8),
      0 0 20px rgba(239, 68, 68, 0.8);
  }
  50% {
    text-shadow:
      0 0 10px rgba(239, 68, 68, 1),
      0 0 20px rgba(239, 68, 68, 1),
      0 0 30px rgba(239, 68, 68, 1),
      0 0 40px rgba(239, 68, 68, 1);
  }
}

@keyframes slideInScale {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes backgroundShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

.animate-glow {
  animation: glow 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-neon {
  animation: neonGlow 2s ease-in-out infinite;
}

.animate-slideInScale {
  animation: slideInScale 0.8s ease-out;
}

.animate-backgroundShift {
  animation: backgroundShift 8s ease infinite;
  background-size: 400% 400%;
}

/* Button hover effects */
.btn-primary {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 15px rgba(239, 68, 68, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #7c3aed, #6b21a8);
  transform: scale(1.05) translateY(-2px);
  box-shadow:
    0 8px 25px rgba(139, 92, 246, 0.5),
    0 0 20px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  border: 2px solid white;
  color: white;
  background: transparent;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: white;
  color: black;
  transform: scale(1.05);
}

.btn-ghost {
  background: transparent;
  border: 1px solid #4b5563;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-ghost:hover {
  border-color: #8b5cf6;
  color: #8b5cf6;
}

/* Typography */
.heading-xl {
  font-family: var(--font-space-grotesk), sans-serif;
  font-weight: 700;
  font-size: clamp(2.5rem, 8vw, 6rem);
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.heading-lg {
  font-family: var(--font-space-grotesk), sans-serif;
  font-weight: 600;
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.heading-md {
  font-family: var(--font-space-grotesk), sans-serif;
  font-weight: 600;
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  line-height: 1.3;
}

.text-body {
  font-family: var(--font-inter), sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* Card effects */
.card-hover {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
}

.card-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.card-hover:hover::before {
  opacity: 1;
}

.card-hover:hover {
  transform: scale(1.05) translateY(-10px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(239, 68, 68, 0.2);
}

.card-glow {
  background: linear-gradient(145deg,
    rgba(255,255,255,0.1),
    rgba(255,255,255,0.05),
    rgba(239, 68, 68, 0.02)
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255,255,255,0.1);
  position: relative;
  overflow: hidden;
}

.card-glow::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s ease;
  opacity: 0;
}

.card-glow:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

.card-glow:hover {
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow:
    0 20px 40px rgba(0,0,0,0.3),
    0 0 20px rgba(239, 68, 68, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c, #ef4444);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: backgroundShift 4s ease infinite;
}

/* Neon effects */
.neon-border {
  border: 2px solid #ef4444;
  box-shadow:
    0 0 10px rgba(239, 68, 68, 0.5),
    inset 0 0 10px rgba(239, 68, 68, 0.1);
}

.neon-text {
  color: #ef4444;
  text-shadow:
    0 0 5px rgba(239, 68, 68, 0.8),
    0 0 10px rgba(239, 68, 68, 0.8),
    0 0 15px rgba(239, 68, 68, 0.8);
}

/* Glass morphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Modern monochrome cards */
.card-monochrome {
  background: rgba(20, 20, 20, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.card-monochrome:hover {
  backdrop-filter: blur(20px);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(139, 92, 246, 0.1);
  transform: translateY(-5px);
}

.card-monochrome::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-monochrome:hover::before {
  opacity: 1;
}

/* Holographic effect */
.holographic {
  background: linear-gradient(45deg,
    #ef4444, #dc2626, #b91c1c, #991b1b,
    #7f1d1d, #991b1b, #b91c1c, #dc2626, #ef4444
  );
  background-size: 400% 400%;
  animation: backgroundShift 3s ease infinite;
}

/* Cyber grid */
.cyber-grid {
  background-image:
    linear-gradient(rgba(239, 68, 68, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(239, 68, 68, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Pulse ring */
.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(239, 68, 68, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Custom spacing utilities */
.gap-30 {
  gap: 30px !important;
}

.space-y-30 > * + * {
  margin-top: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

/* Dashboard specific spacing */
.dashboard-spacing {
  margin-left: 80px !important; /* 20px sidebar + 30px margin */
  padding-left: 30px !important;
  padding-right: 30px !important;
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #8b5cf6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #7c3aed;
}

/* AI Agent specific styles */
.glow-purple {
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.5), 0 0 40px rgba(139, 92, 246, 0.3), 0 0 60px rgba(139, 92, 246, 0.2);
}

.animate-glow-text {
  animation: glowText 2s ease-in-out infinite alternate;
}

@keyframes glowText {
  from {
    text-shadow: 0 0 20px rgba(239, 68, 68, 0.5), 0 0 40px rgba(239, 68, 68, 0.3), 0 0 60px rgba(239, 68, 68, 0.2);
  }
  to {
    text-shadow: 0 0 30px rgba(239, 68, 68, 0.8), 0 0 60px rgba(239, 68, 68, 0.5), 0 0 90px rgba(239, 68, 68, 0.3);
  }
}


