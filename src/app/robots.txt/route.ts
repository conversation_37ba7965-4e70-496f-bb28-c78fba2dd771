import { NextResponse } from 'next/server';

export async function GET() {
  const robotsTxt = `User-agent: *
Allow: /

# Запрещаем индексацию административных страниц
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/
Disallow: /auth/

# Запрещаем индексацию служебных файлов
Disallow: /_next/
Disallow: /static/
Disallow: /*.json$

# Разрешаем индексацию основных разделов
Allow: /services/
Allow: /blog/
Allow: /portfolio/
Allow: /about/
Allow: /contact/
Allow: /pricing/

# Sitemap
Sitemap: https://hiveagency.com/sitemap.xml

# Специальные правила для поисковых ботов
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Yandex
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 2

# Блокируем агрессивных ботов
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /`;

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400',
    },
  });
}
