'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { FaEye, FaEyeSlash, FaGoogle, FaArrowLeft, FaTelegram } from 'react-icons/fa';

function LoginContent() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [successMessage] = useState('');
  const [language] = useState('ru'); // This would come from context in real app
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');

  const { login, loginWithGoogle, loginWithTelegram, loading } = useAuth();
  const toast = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const message = searchParams.get('message');
    const error = searchParams.get('error');

    if (message === 'password_reset_success') {
      toast.success('Пароль изменен!', 'Теперь вы можете войти с новым паролем.');
    } else if (error === 'auth_failed') {
      toast.error('Ошибка аутентификации', 'Попробуйте снова.');
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await login(formData.email, formData.password);
      router.push('/dashboard');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : (language === 'ru' ? 'Неверный email или пароль' : 'Invalid email or password');
      setError(errorMessage);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setError('');
      await loginWithGoogle();
      // Редирект произойдет автоматически через OAuth
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка входа через Google';
      setError(errorMessage);

      // Если ошибка связана с отключенным провайдером, показываем ссылку на инструкции
      if (errorMessage.includes('провайдер отключен') || errorMessage.includes('provider_disabled')) {
        setError(errorMessage + ' Нужна помощь с настройкой?');
      }
    }
  };

  const handleTelegramLogin = async () => {
    try {
      setError('');
      await loginWithTelegram();
      // Редирект произойдет автоматически
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка входа через Telegram';
      setError(errorMessage);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center px-4 py-12 relative overflow-hidden">
      {/* Background blur effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-md w-full relative z-10">
        {/* Back button */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-300"
          >
            <FaArrowLeft className="w-4 h-4" />
            {language === 'ru' ? 'На главную' : 'Back to Home'}
          </Link>
        </div>

        {/* Logo */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl mb-4">
            <span className="text-2xl font-bold text-white">H!</span>
          </div>
          <h1 className="text-2xl font-semibold text-white mb-2">
            {language === 'ru' ? 'Войдите с H!VE ID' : 'Sign in with H!VE ID'}
          </h1>
        </div>

        {/* Login Form */}
        <div className="card-monochrome rounded-3xl form-padding-lg group">
          <div className="relative z-10">
            {/* Login method tabs */}
            <div className="flex bg-gray-800/50 border border-gray-700/50 rounded-xl p-1 mb-6 group-hover:border-red-500/30 transition-all duration-300">
              <button
                type="button"
                onClick={() => setLoginMethod('email')}
                className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-300 ${
                  loginMethod === 'email'
                    ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
                }`}
              >
                {language === 'ru' ? 'Почта' : 'Email'}
              </button>
              <button
                type="button"
                onClick={() => setLoginMethod('phone')}
                className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-300 ${
                  loginMethod === 'phone'
                    ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
                }`}
              >
                {language === 'ru' ? 'Телефон' : 'Phone'}
              </button>
            </div>

            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
              {/* Success Message */}
              {successMessage && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-3 text-green-400 text-sm">
                  {successMessage}
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-3 text-red-400 text-sm">
                  <div>{error}</div>
                  {(error.includes('провайдер отключен') || error.includes('provider_disabled')) && (
                    <div className="mt-2">
                      <Link
                        href="/auth/setup"
                        className="text-red-300 hover:text-red-200 underline font-medium"
                      >
                        Инструкция по настройке OAuth →
                      </Link>
                    </div>
                  )}
                </div>
              )}

              <div className="form-group">
                <label className="form-label">
                  {loginMethod === 'email'
                    ? (language === 'ru' ? 'Email' : 'Email')
                    : (language === 'ru' ? 'Телефон' : 'Phone')
                  }
                </label>
                <input
                  type={loginMethod === 'email' ? 'email' : 'tel'}
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={loginMethod === 'email'
                    ? (language === 'ru' ? '<EMAIL>' : '<EMAIL>')
                    : (language === 'ru' ? '+7 (999) 123-45-67' : '+****************')
                  }
                  className="form-input w-full"
                  required
                />
              </div>

              {/* Password Field */}
              <div className="form-group">
                <label className="form-label">
                  {language === 'ru' ? 'Пароль' : 'Password'}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder={language === 'ru' ? '••••••••' : '••••••••'}
                    className="form-input w-full pr-12"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-300 transition-colors duration-300"
                  >
                    {showPassword ? <FaEyeSlash className="w-5 h-5" /> : <FaEye className="w-5 h-5" />}
                  </button>
                </div>

                {/* Forgot Password Link */}
                <div className="mt-2 text-right">
                  <Link
                    href="/auth/reset-password"
                    className="text-sm text-gray-400 hover:text-red-400 transition-colors duration-300"
                  >
                    {language === 'ru' ? 'Забыли пароль?' : 'Forgot password?'}
                  </Link>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="btn-primary w-full"
              >
                {loading
                  ? (language === 'ru' ? 'Вход...' : 'Signing in...')
                  : (language === 'ru' ? 'Войти' : 'Sign In')
                }
              </button>

            </form>

            {/* Divider */}
            <div className="relative my-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-700/50"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-gray-900/80 text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                  {language === 'ru' ? 'или войти с помощью' : 'or sign in with'}
                </span>
              </div>
            </div>

            {/* Social Login Buttons */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              {/* Google Login */}
              <button
                type="button"
                onClick={handleGoogleLogin}
                disabled={loading}
                className="btn-social w-full flex items-center justify-center gap-3"
              >
                <FaGoogle className="w-5 h-5 text-red-400" />
                <span>
                  {language === 'ru' ? 'Продолжить с Google' : 'Continue with Google'}
                </span>
              </button>

              {/* Telegram Login */}
              <button
                type="button"
                onClick={handleTelegramLogin}
                disabled={loading}
                className="btn-social w-full flex items-center justify-center gap-3"
              >
                <FaTelegram className="w-5 h-5 text-blue-400" />
                <span>
                  {language === 'ru' ? 'Продолжить с Telegram' : 'Continue with Telegram'}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Registration Link */}
        <div className="mt-8 text-center">
          <span className="text-gray-400 text-sm">
            {language === 'ru' ? 'Нет аккаунта?' : "Don't have an account?"}{' '}
          </span>
          <Link
            href="/register"
            className="text-red-400 hover:text-red-300 font-semibold text-sm transition-colors duration-300"
          >
            {language === 'ru' ? 'Зарегистрироваться' : 'Sign up'}
          </Link>
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-gray-500 text-sm mb-2">
            {language === 'ru' ? 'H!VE ID — ключ от всех сервисов' : 'H!VE ID — key to all services'}
          </p>
          <Link
            href="/help"
            className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
          >
            {language === 'ru' ? 'Узнать больше' : 'Learn more'}
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Загрузка...</div>
      </div>
    }>
      <LoginContent />
    </Suspense>
  );
}
