'use client';

import { useState } from 'react';
import { Crown, Rocket, Check, Music } from 'lucide-react';
import { FaStar, FaUser } from 'react-icons/fa';
import Link from 'next/link';

const PricingPage = () => {
  const [activeTab, setActiveTab] = useState<'artist' | 'track'>('artist');
  const [language] = useState('ru'); // This would come from context in real app

  const artistPlans = [
    {
      id: 'artist-starter',
      name: language === 'ru' ? 'Стартер' : 'Starter',
      price: 5000,
      period: language === 'ru' ? '/месяц' : '/month',
      icon: <FaStar className="text-3xl text-yellow-500" />,
      popular: false,
      description: language === 'ru' ? 'Идеально для начинающих артистов' : 'Perfect for emerging artists',
      features: [
        language === 'ru' ? 'Продвижение на радио (30 станций)' : 'Radio promotion (30 stations)',
        language === 'ru' ? 'Связь с клубами и промоутерами' : 'Club and promoter connections',
        language === 'ru' ? 'Реклама в социальных сетях' : 'Social media advertising',
        language === 'ru' ? 'Email рассылка по базе' : 'Email marketing campaign',
        language === 'ru' ? 'Отчетность и аналитика' : 'Reporting and analytics',
        language === 'ru' ? 'Базовая поддержка' : 'Basic support',
      ],
    },
    {
      id: 'artist-professional',
      name: language === 'ru' ? 'Профессионал' : 'Professional',
      price: 15000,
      period: language === 'ru' ? '/месяц' : '/month',
      icon: <Crown className="text-3xl text-purple-500 animate-float" />,
      popular: true,
      description: language === 'ru' ? 'Для серьезных артистов' : 'For serious artists',
      features: [
        language === 'ru' ? 'Продвижение на радио (80 станций)' : 'Radio promotion (80 stations)',
        language === 'ru' ? 'Связь с клубами и промоутерами' : 'Club and promoter connections',
        language === 'ru' ? 'Реклама на ТВ (региональные каналы)' : 'TV advertising (regional channels)',
        language === 'ru' ? 'Реклама в социальных сетях (расширенная)' : 'Social media advertising (extended)',
        language === 'ru' ? 'Email рассылка по базе' : 'Email marketing campaign',
        language === 'ru' ? 'PR и медиа покрытие' : 'PR and media coverage',
        language === 'ru' ? 'Персональный менеджер' : 'Personal manager',
        language === 'ru' ? 'Приоритетная поддержка' : 'Priority support',
      ],
    },
    {
      id: 'artist-premium',
      name: language === 'ru' ? 'Премиум' : 'Premium',
      price: 30000,
      period: language === 'ru' ? '/месяц' : '/month',
      icon: <Rocket className="text-3xl text-red-500 animate-glow" />,
      popular: false,
      description: language === 'ru' ? 'Максимальный охват и результат' : 'Maximum reach and results',
      features: [
        language === 'ru' ? 'Продвижение на радио (150+ станций)' : 'Radio promotion (150+ stations)',
        language === 'ru' ? 'Связь с клубами и промоутерами' : 'Club and promoter connections',
        language === 'ru' ? 'Реклама на ТВ (федеральные каналы)' : 'TV advertising (federal channels)',
        language === 'ru' ? 'Реклама в социальных сетях (премиум)' : 'Social media advertising (premium)',
        language === 'ru' ? 'Email рассылка по базе' : 'Email marketing campaign',
        language === 'ru' ? 'PR и медиа покрытие (расширенное)' : 'PR and media coverage (extended)',
        language === 'ru' ? 'Персональный менеджер' : 'Personal manager',
        language === 'ru' ? 'Дополнительные ТВ бюджеты' : 'Additional TV budgets',
        language === 'ru' ? 'Эксклюзивные партнерства' : 'Exclusive partnerships',
        language === 'ru' ? '24/7 поддержка' : '24/7 support',
      ],
    },
  ];

  const trackPlans = [
    {
      id: 'track-basic',
      name: language === 'ru' ? 'Базовое продвижение' : 'Basic Promotion',
      price: 500,
      period: language === 'ru' ? '/трек' : '/track',
      description: language === 'ru' ? 'Стартовое продвижение трека' : 'Basic track promotion',
      features: [
        language === 'ru' ? 'Радио ротация (10 станций)' : 'Radio rotation (10 stations)',
        language === 'ru' ? 'Плейлисты стриминговых сервисов' : 'Streaming service playlists',
        language === 'ru' ? 'Социальные сети (базовая)' : 'Social media (basic)',
        language === 'ru' ? 'Отчет о результатах' : 'Results report',
      ],
    },
    {
      id: 'track-professional',
      name: language === 'ru' ? 'Профессиональное продвижение' : 'Professional Promotion',
      price: 3000,
      period: language === 'ru' ? '/трек' : '/track',
      description: language === 'ru' ? 'Комплексное продвижение трека' : 'Comprehensive track promotion',
      features: [
        language === 'ru' ? 'Радио ротация (50 станций)' : 'Radio rotation (50 stations)',
        language === 'ru' ? 'Плейлисты стриминговых сервисов' : 'Streaming service playlists',
        language === 'ru' ? 'Социальные сети (расширенная)' : 'Social media (extended)',
        language === 'ru' ? 'PR и медиа покрытие' : 'PR and media coverage',
        language === 'ru' ? 'Музыкальные блоги' : 'Music blogs',
        language === 'ru' ? 'Детальная аналитика' : 'Detailed analytics',
      ],
    },
    {
      id: 'track-premium',
      name: language === 'ru' ? 'Премиум продвижение' : 'Premium Promotion',
      price: 5000,
      period: language === 'ru' ? '/трек' : '/track',
      description: language === 'ru' ? 'Максимальное продвижение' : 'Maximum promotion',
      features: [
        language === 'ru' ? 'Радио ротация (100+ станций)' : 'Radio rotation (100+ stations)',
        language === 'ru' ? 'ТВ реклама' : 'TV advertising',
        language === 'ru' ? 'Социальные сети (премиум)' : 'Social media (premium)',
        language === 'ru' ? 'PR и медиа покрытие (расширенное)' : 'PR and media coverage (extended)',
        language === 'ru' ? 'Эксклюзивные плейлисты' : 'Exclusive playlists',
        language === 'ru' ? 'Персональный менеджер' : 'Personal manager',
        language === 'ru' ? 'Полная аналитика и отчетность' : 'Full analytics and reporting',
      ],
    },
  ];

  const handleSelectPlan = (planId: string, price: number) => {
    // Redirect to payment page with plan details
    window.location.href = `/payment?plan=${planId}&price=${price}`;
  };

  return (
    <div className="min-h-screen bg-black pt-20">
      {/* Header */}
      <div className="container mx-auto px-6 py-16 text-center">
        <h1 className="heading-xl text-white mb-8">
          {language === 'ru' ? 'Тарифы' : 'Pricing'}
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-16">
          {language === 'ru'
            ? 'Выберите подходящий тариф для продвижения вашей музыки'
            : 'Choose the right plan to promote your music'
          }
        </p>

        {/* Tab Switcher */}
        <div className="flex justify-center mb-20">
          <div className="bg-gray-900 rounded-full p-2 border border-gray-700">
            <button
              onClick={() => setActiveTab('artist')}
              className={`px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-3 ${
                activeTab === 'artist'
                  ? 'bg-red-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <FaUser />
              {language === 'ru' ? 'Артист' : 'Artist'}
            </button>
            <button
              onClick={() => setActiveTab('track')}
              className={`px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-3 ${
                activeTab === 'track'
                  ? 'bg-red-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <Music className="w-5 h-5" />
              {language === 'ru' ? 'Трек' : 'Track'}
            </button>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="container mx-auto px-6 pb-20">
        {/* Artist Plans */}
        {activeTab === 'artist' && (
          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {artistPlans.map((plan) => (
              <div
                key={plan.id}
                className={`relative card-glow rounded-3xl p-8 border-2 card-hover ${
                  plan.popular
                    ? 'border-red-500 bg-gradient-to-b from-red-500/10 to-gray-900/50 animate-glow'
                    : 'border-gray-700 hover:border-red-500/50'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-red-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                      {language === 'ru' ? 'Популярный' : 'Popular'}
                    </span>
                  </div>
                )}

                <div className="text-center mb-10">
                  <div className="mb-6">{plan.icon}</div>
                  <h3 className="text-2xl font-bold text-white mb-4 heading-md">{plan.name}</h3>
                  <p className="text-gray-400 mb-6">{plan.description}</p>
                  <div className="text-4xl font-bold text-red-500 mb-3 gradient-text">
                    ${plan.price.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400">{plan.period}</div>
                </div>

                <ul className="space-y-5 mb-10">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="text-green-500 mt-1 flex-shrink-0 w-5 h-5" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleSelectPlan(plan.id, plan.price)}
                  className="w-full btn-primary text-lg"
                >
                  {language === 'ru' ? 'Выбрать план' : 'Choose Plan'}
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Track Plans */}
        {activeTab === 'track' && (
          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {trackPlans.map((plan) => (
              <div
                key={plan.id}
                className="card-glow rounded-3xl p-8 border-2 border-gray-700 hover:border-red-500/50 card-hover"
              >
                <div className="text-center mb-10">
                  <h3 className="text-2xl font-bold text-white mb-4 heading-md">{plan.name}</h3>
                  <p className="text-gray-400 mb-6">{plan.description}</p>
                  <div className="text-4xl font-bold text-red-500 mb-3 gradient-text">
                    ${plan.price.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400">{plan.period}</div>
                </div>

                <ul className="space-y-5 mb-10">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <Check className="text-green-500 mt-1 flex-shrink-0 w-5 h-5" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleSelectPlan(plan.id, plan.price)}
                  className="w-full btn-primary text-lg"
                >
                  {language === 'ru' ? 'Заказать' : 'Order'}
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Label Release Section */}
      <div className="bg-gray-900 py-20">
        <div className="container mx-auto px-6 text-center">
          <h2 className="heading-lg text-white mb-8">
            {language === 'ru' ? 'Релиз через лейбл' : 'Label Release'}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
            {language === 'ru'
              ? 'Издайте свой трек через наших партнеров-лейблов и увеличьте продажи на 80%'
              : 'Release your track through our partner labels and increase sales by 80%'
            }
          </p>
          <Link href="/contact" className="btn-primary text-lg">
            {language === 'ru' ? 'Узнать подробности' : 'Learn More'}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PricingPage;
