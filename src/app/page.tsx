'use client';

import { LanguageProvider } from "@/contexts/LanguageContext";
import { AuthProvider } from "@/contexts/AuthContext";
import Navigation from '@/components/layout/Navigation';
import SuperAIAgent from '@/components/SuperAIAgent';
import ValueProposition from '@/components/sections/ValueProposition';
import HowItWorks from '@/components/sections/HowItWorks';
import SocialProof from '@/components/sections/SocialProof';
import SimplePricing from '@/components/sections/SimplePricing';
import FinalCTA from '@/components/sections/FinalCTA';
import ModernFooter from '@/components/ModernFooter';

export default function Home() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <div className="min-h-screen bg-black">
          {/* Navigation */}
          <Navigation />

          <main>
            {/* Hero Section - AI Agent */}
            <section id="ai-agent" className="pt-20">
              <SuperAIAgent />
            </section>

            {/* Value Proposition */}
            <section id="value" className="section-spacing">
              <ValueProposition />
            </section>

            {/* How It Works */}
            <section id="how-it-works" className="section-spacing">
              <HowItWorks />
            </section>

            {/* Social Proof */}
            <section id="reviews" className="section-spacing">
              <SocialProof />
            </section>

            {/* Simple Pricing */}
            <section id="pricing" className="section-spacing">
              <SimplePricing />
            </section>

            {/* Final CTA */}
            <section id="cta" className="section-spacing">
              <FinalCTA />
            </section>
          </main>

          {/* Footer */}
          <ModernFooter />
        </div>
      </AuthProvider>
    </LanguageProvider>
  );
}
