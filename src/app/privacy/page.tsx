'use client';

import Link from 'next/link';
import { 
  Shield, 
  Lock, 
  Eye, 
  Database, 
  UserCheck, 
  AlertTriangle, 
  CheckCircle,
  Mail,
  Phone,
  Globe,
  ArrowLeft,
  Calendar,
  FileText,
  Settings,
  Trash2,
  Download,
  Server
} from 'lucide-react';

export default function PrivacyPolicyPage() {
  const lastUpdated = "15 января 2024";

  const sections = [
    {
      id: 'collection',
      title: 'Какие данные мы собираем',
      icon: <Database className="w-5 h-5" />,
      content: [
        'Контактная информация: имя, email, номер телефона',
        'Данные аккаунтов: логины социальных сетей для продвижения',
        'Платежная информация: данные карт обрабатываются через защищенные платежные системы',
        'Техническая информация: IP-адрес, браузер, устройство для улучшения сервиса',
        'Контент для продвижения: музыкальные треки, изображения, видео, тексты'
      ]
    },
    {
      id: 'usage',
      title: 'Как мы используем данные',
      icon: <Settings className="w-5 h-5" />,
      content: [
        'Оказание услуг продвижения согласно заключенным договорам',
        'Коммуникация с клиентами: уведомления, отчеты, консультации',
        'Обработка платежей и ведение финансовой отчетности',
        'Улучшение качества услуг на основе аналитики',
        'Соблюдение законодательных требований и предотвращение мошенничества'
      ]
    },
    {
      id: 'sharing',
      title: 'Передача данных третьим лицам',
      icon: <UserCheck className="w-5 h-5" />,
      content: [
        'Социальные сети и платформы: для размещения контента и рекламы',
        'Платежные системы: для обработки транзакций (Stripe, PayPal, банки)',
        'Аналитические сервисы: Google Analytics, Яндекс.Метрика (анонимно)',
        'Подрядчики: дизайнеры, видеографы, копирайтеры (только необходимые данные)',
        'Государственные органы: только при наличии официальных запросов'
      ]
    },
    {
      id: 'storage',
      title: 'Хранение и защита данных',
      icon: <Lock className="w-5 h-5" />,
      content: [
        'Данные хранятся на защищенных серверах с SSL-шифрованием',
        'Доступ к данным имеют только уполномоченные сотрудники',
        'Регулярное резервное копирование для предотвращения потери данных',
        'Срок хранения: 3 года после окончания сотрудничества',
        'Автоматическое удаление неактивных аккаунтов через 2 года'
      ]
    },
    {
      id: 'rights',
      title: 'Ваши права',
      icon: <Eye className="w-5 h-5" />,
      content: [
        'Право на доступ: запросить копию всех ваших данных',
        'Право на исправление: обновить неточную информацию',
        'Право на удаление: запросить полное удаление ваших данных',
        'Право на ограничение: ограничить обработку в определенных случаях',
        'Право на портируемость: получить данные в машиночитаемом формате'
      ]
    },
    {
      id: 'cookies',
      title: 'Файлы cookie и отслеживание',
      icon: <Server className="w-5 h-5" />,
      content: [
        'Используем необходимые cookie для работы сайта',
        'Аналитические cookie для понимания поведения пользователей',
        'Маркетинговые cookie для персонализации рекламы (с согласия)',
        'Вы можете отключить cookie в настройках браузера',
        'Некоторые функции сайта могут быть недоступны без cookie'
      ]
    },
    {
      id: 'minors',
      title: 'Данные несовершеннолетних',
      icon: <Shield className="w-5 h-5" />,
      content: [
        'Не собираем данные детей младше 13 лет без согласия родителей',
        'Для пользователей 13-18 лет требуется согласие родителей/опекунов',
        'Особая защита данных несовершеннолетних артистов',
        'Родители могут запросить удаление данных своих детей',
        'Соблюдаем требования COPPA и российского законодательства'
      ]
    },
    {
      id: 'international',
      title: 'Международные передачи',
      icon: <Globe className="w-5 h-5" />,
      content: [
        'Данные могут передаваться в страны с адекватным уровнем защиты',
        'Используем стандартные договорные положения ЕС',
        'Соблюдаем требования GDPR для европейских пользователей',
        'Данные российских пользователей хранятся на территории РФ',
        'Уведомляем о любых изменениях в географии обработки данных'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-black" style={{ padding: '30px 30px 60px' }}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center" style={{ marginBottom: '50px' }}>
          <div className="flex items-center justify-center" style={{ gap: '15px', marginBottom: '20px' }}>
            <div className="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-green-400" />
            </div>
            <h1 className="text-4xl font-bold text-white">Privacy Policy</h1>
          </div>
          
          <p className="text-xl text-gray-400" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
            Политика конфиденциальности HIVE Agency
          </p>
          
          <div className="flex items-center justify-center" style={{ gap: '20px' }}>
            <div className="flex items-center" style={{ gap: '8px' }}>
              <Calendar className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-500">Обновлено: {lastUpdated}</span>
            </div>
            <div className="flex items-center" style={{ gap: '8px' }}>
              <Lock className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-400">GDPR совместимо</span>
            </div>
          </div>
        </div>

        {/* Key Points */}
        <div className="bg-green-500/20 border border-green-500/30 rounded-2xl" style={{ padding: '30px', marginBottom: '40px' }}>
          <h2 className="text-xl font-semibold text-white" style={{ marginBottom: '20px' }}>
            🔒 Ключевые принципы защиты данных
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
            <div className="flex items-start" style={{ gap: '12px' }}>
              <CheckCircle className="w-5 h-5 text-green-400" style={{ marginTop: '2px', flexShrink: 0 }} />
              <div>
                <h3 className="text-green-300 font-semibold">Минимизация данных</h3>
                <p className="text-green-200 text-sm">Собираем только необходимые для услуг данные</p>
              </div>
            </div>
            
            <div className="flex items-start" style={{ gap: '12px' }}>
              <CheckCircle className="w-5 h-5 text-green-400" style={{ marginTop: '2px', flexShrink: 0 }} />
              <div>
                <h3 className="text-green-300 font-semibold">Прозрачность</h3>
                <p className="text-green-200 text-sm">Четко объясняем, как используем ваши данные</p>
              </div>
            </div>
            
            <div className="flex items-start" style={{ gap: '12px' }}>
              <CheckCircle className="w-5 h-5 text-green-400" style={{ marginTop: '2px', flexShrink: 0 }} />
              <div>
                <h3 className="text-green-300 font-semibold">Контроль</h3>
                <p className="text-green-200 text-sm">Вы контролируете свои данные и можете их удалить</p>
              </div>
            </div>
            
            <div className="flex items-start" style={{ gap: '12px' }}>
              <CheckCircle className="w-5 h-5 text-green-400" style={{ marginTop: '2px', flexShrink: 0 }} />
              <div>
                <h3 className="text-green-300 font-semibold">Безопасность</h3>
                <p className="text-green-200 text-sm">Используем современные методы защиты</p>
              </div>
            </div>
          </div>
        </div>

        {/* Sections */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
          {sections.map((section, index) => (
            <div
              key={section.id}
              id={section.id}
              className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50"
              style={{ padding: '30px' }}
            >
              <div className="flex items-center" style={{ gap: '12px', marginBottom: '20px' }}>
                <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                  {section.icon}
                </div>
                <h2 className="text-2xl font-bold text-white">
                  {index + 1}. {section.title}
                </h2>
              </div>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {section.content.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-start" style={{ gap: '12px' }}>
                    <div className="w-2 h-2 bg-green-400 rounded-full" style={{ marginTop: '8px', flexShrink: 0 }} />
                    <p className="text-gray-300" style={{ lineHeight: '1.6' }}>
                      {item}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Data Request Actions */}
        <div className="bg-blue-500/20 border border-blue-500/30 rounded-2xl" style={{ padding: '30px', marginTop: '40px' }}>
          <h2 className="text-2xl font-bold text-white" style={{ marginBottom: '20px' }}>
            Управление вашими данными
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3" style={{ gap: '15px', marginBottom: '20px' }}>
            <button className="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-300 rounded-lg transition-colors flex items-center justify-center" style={{ padding: '15px', gap: '8px' }}>
              <Download className="w-4 h-4" />
              Скачать данные
            </button>
            
            <button className="bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 text-yellow-300 rounded-lg transition-colors flex items-center justify-center" style={{ padding: '15px', gap: '8px' }}>
              <Settings className="w-4 h-4" />
              Изменить данные
            </button>
            
            <button className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-300 rounded-lg transition-colors flex items-center justify-center" style={{ padding: '15px', gap: '8px' }}>
              <Trash2 className="w-4 h-4" />
              Удалить данные
            </button>
          </div>
          
          <p className="text-blue-200 text-sm text-center">
            Для выполнения запросов свяжитесь с нами по email: <EMAIL>
          </p>
        </div>

        {/* Contact Information */}
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-2xl border border-gray-800/50" style={{ padding: '30px', marginTop: '30px' }}>
          <h2 className="text-xl font-semibold text-white" style={{ marginBottom: '20px' }}>
            Контакты по вопросам конфиденциальности
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
            <div>
              <h3 className="text-lg font-semibold text-blue-300" style={{ marginBottom: '10px' }}>
                Ответственный за защиту данных:
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Mail className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-200"><EMAIL></span>
                </div>
                <div className="flex items-center" style={{ gap: '8px' }}>
                  <Phone className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-200">+7 (999) 123-45-67</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-green-300" style={{ marginBottom: '10px' }}>
                Время ответа:
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <span className="text-green-200 text-sm">• Запросы на доступ: до 30 дней</span>
                <span className="text-green-200 text-sm">• Запросы на удаление: до 14 дней</span>
                <span className="text-green-200 text-sm">• Срочные вопросы: до 24 часов</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="text-center" style={{ marginTop: '40px' }}>
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap', marginBottom: '20px' }}>
            <Link
              href="/terms"
              className="bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 24px' }}
            >
              Условия использования
            </Link>
            
            <Link
              href="/contact"
              className="bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors font-semibold"
              style={{ padding: '12px 24px' }}
            >
              Связаться с нами
            </Link>
          </div>
          
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            style={{ gap: '8px' }}
          >
            <ArrowLeft className="w-4 h-4" />
            Вернуться на главную
          </Link>
        </div>
      </div>
    </div>
  );
}
