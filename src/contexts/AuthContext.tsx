'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { account, ID } from '@/lib/appwrite';
import { User } from '@/types';
import { AppwriteException, OAuthProvider } from 'appwrite';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string, userType?: 'artist' | 'label') => Promise<void>;
  logout: () => Promise<void>;
  logoutAll: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  updatePassword: (newPassword: string, oldPassword?: string) => Promise<void>;
  updateEmail: (email: string, password: string) => Promise<void>;
  sendEmailVerification: (url?: string) => Promise<void>;
  sendPasswordRecovery: (email: string, url: string) => Promise<void>;
  updatePasswordRecovery: (userId: string, secret: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithTelegram: () => Promise<void>;
  refreshSession: () => Promise<void>;
  getPreferences: () => Promise<Record<string, unknown>>;
  updatePreferences: (prefs: Record<string, unknown>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const isAuthenticated = !!user;

  const checkAuth = async () => {
    try {
      const currentUser = await account.get();
      if (currentUser) {
        // Получаем preferences пользователя
        let userPrefs = {};
        try {
          userPrefs = await account.getPrefs();
        } catch (prefsError) {
          console.warn('Could not fetch user preferences:', prefsError);
        }

        // Convert Appwrite user to our User type
        const userData: User = {
          $id: currentUser.$id,
          name: currentUser.name,
          email: currentUser.email,
          emailVerification: currentUser.emailVerification,
          phoneVerification: currentUser.phoneVerification,
          type: (userPrefs as Record<string, unknown>)?.type as 'artist' | 'label' | 'admin' || 'artist', // Get type from preferences
          created_at: currentUser.$createdAt,
          updated_at: currentUser.$updatedAt,
          prefs: userPrefs,
        };
        setUser(userData);
      }
    } catch (error) {
      if (error instanceof AppwriteException) {
        console.log('No active session:', error.message);
      } else {
        console.error('Auth check error:', error);
      }
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      await account.createEmailPasswordSession(email, password);
      await checkAuth();
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string, userType: 'artist' | 'label' = 'artist') => {
    try {
      setLoading(true);

      // Создаем аккаунт с уникальным ID
      const userId = ID.unique();
      await account.create(userId, email, password, name);

      // Логинимся после регистрации
      await account.createEmailPasswordSession(email, password);

      // Устанавливаем preferences пользователя
      try {
        await account.updatePrefs({ type: userType });
      } catch (prefsError) {
        console.warn('Could not set user preferences:', prefsError);
      }

      // Обновляем состояние пользователя
      await checkAuth();

      // Отправляем письмо для верификации email
      try {
        const verificationUrl = typeof window !== 'undefined'
          ? `${window.location.origin}/auth/verify-email`
          : 'http://localhost:3000/auth/verify-email';
        await account.createVerification(verificationUrl);
      } catch (verificationError) {
        console.warn('Email verification failed:', verificationError);
        // Не прерываем процесс регистрации из-за ошибки верификации
      }
    } catch (error) {
      console.error('Registration error:', error);
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      throw new Error('Неизвестная ошибка при регистрации');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);

      // Проверяем, есть ли активная сессия
      if (account) {
        try {
          await account.deleteSession('current');
        } catch (sessionError) {
          // Если сессия уже недействительна, это нормально
          console.warn('Session already invalid or expired');
        }
      }

      // Очищаем локальное состояние
      setUser(null);

      // Перенаправляем на главную страницу
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } catch (error) {
      console.warn('Logout warning:', error);
      // В любом случае очищаем локальное состояние
      setUser(null);
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } finally {
      setLoading(false);
    }
  };

  const logoutAll = async () => {
    try {
      setLoading(true);

      if (account) {
        try {
          await account.deleteSessions();
        } catch (sessionError) {
          console.warn('Could not delete all sessions:', sessionError);
        }
      }

      // Очищаем локальное состояние
      setUser(null);

      // Перенаправляем на главную страницу
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } catch (error) {
      console.warn('Logout all warning:', error);
      // В любом случае очищаем локальное состояние
      setUser(null);
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      setLoading(true);

      if (data.name) {
        await account.updateName(data.name);
      }

      // Email update requires password, so we handle it separately
      if (data.email && data.password) {
        await account.updateEmail(data.email, data.password);
      }

      // Update preferences if type is provided
      if (data.type) {
        const currentPrefs = await account.getPrefs();
        await account.updatePrefs({ ...currentPrefs, type: data.type });
      }

      await checkAuth();
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updatePassword = async (newPassword: string, oldPassword?: string) => {
    try {
      setLoading(true);
      await account.updatePassword(newPassword, oldPassword);
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateEmail = async (email: string, password: string) => {
    try {
      setLoading(true);
      await account.updateEmail(email, password);
      await checkAuth();
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const sendEmailVerification = async (url?: string) => {
    try {
      const verificationUrl = url || (typeof window !== 'undefined'
        ? `${window.location.origin}/auth/verify-email`
        : 'http://localhost:3000/auth/verify-email');
      await account.createVerification(verificationUrl);
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  const sendPasswordRecovery = async (email: string, url: string) => {
    try {
      await account.createRecovery(email, url);
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  const updatePasswordRecovery = async (userId: string, secret: string, password: string) => {
    try {
      await account.updateRecovery(userId, secret, password);
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    try {
      // Определяем правильные URL для разных окружений
      const baseUrl = typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

      const successUrl = `${baseUrl}/dashboard`;
      const failureUrl = `${baseUrl}/login?error=oauth_failed`;

      console.log('OAuth URLs:', { successUrl, failureUrl });

      // Используем OAuthProvider enum для провайдера
      // createOAuth2Session redirects the browser, so no await needed
      // Appwrite автоматически использует свой redirect URI: https://fra.cloud.appwrite.io/v1/account/sessions/oauth2/callback/google/6872a2e20006222a3ad1
      account.createOAuth2Session(
        OAuthProvider.Google,
        successUrl,  // URL куда перенаправить после успешной авторизации
        failureUrl   // URL куда перенаправить при ошибке
      );
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  const loginWithTelegram = async () => {
    try {
      // Здесь будет интеграция с Telegram Login Widget
      // Пока что просто заглушка
      throw new Error('Telegram login в разработке');
    } catch (error) {
      console.error('Telegram login error:', error);
      throw error;
    }
  };

  const refreshSession = async () => {
    try {
      await account.updateSession('current');
      await checkAuth();
    } catch (error) {
      if (error instanceof AppwriteException) {
        console.error('Session refresh error:', getErrorMessage(error));
      }
      throw error;
    }
  };

  const getPreferences = async () => {
    try {
      return await account.getPrefs();
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  const updatePreferences = async (prefs: Record<string, unknown>) => {
    try {
      await account.updatePrefs(prefs);
      await checkAuth(); // Обновляем пользователя с новыми preferences
    } catch (error) {
      if (error instanceof AppwriteException) {
        throw new Error(getErrorMessage(error));
      }
      throw error;
    }
  };

  // Функция для получения понятных сообщений об ошибках
  const getErrorMessage = (error: AppwriteException): string => {
    console.error('Auth error details:', {
      code: error.code,
      message: error.message,
      type: error.type,
      response: error.response,
      fullError: error
    });

    switch (error.code) {
      case 401:
        if (error.message.includes('Invalid credentials')) {
          return 'Неверный email или пароль';
        }
        if (error.message.includes('user_email_not_confirmed')) {
          return 'Подтвердите email адрес для входа в систему';
        }
        return 'Ошибка авторизации. Проверьте данные для входа';

      case 409:
        if (error.message.includes('user_already_exists')) {
          return 'Пользователь с таким email уже существует';
        }
        return 'Конфликт данных. Попробуйте другой email';

      case 412:
        if (error.message.includes('provider_disabled') || error.message.includes('This provider is disabled')) {
          return 'OAuth провайдер отключен. Включите Google OAuth в консоли Appwrite';
        }
        if (error.message.includes('user_email_not_confirmed')) {
          return 'Подтвердите email адрес для продолжения';
        }
        return 'Сервис временно недоступен. Попробуйте позже';

      case 429:
        return 'Слишком много попыток. Подождите несколько минут и попробуйте снова';

      case 400:
        if (error.message.includes('password')) {
          return 'Пароль должен содержать минимум 8 символов';
        }
        if (error.message.includes('email')) {
          return 'Введите корректный email адрес';
        }
        if (error.message.includes('redirect') || error.message.includes('Invalid URI')) {
          return 'Ошибка настройки OAuth. Добавьте домен в Appwrite Platforms';
        }
        if (error.message.includes('general_argument_invalid')) {
          return 'Неверные параметры запроса. Проверьте настройки приложения';
        }
        return 'Проверьте правильность введенных данных';

      case 404:
        if (error.message.includes('user_not_found')) {
          return 'Пользователь с таким email не найден';
        }
        return 'Запрашиваемый ресурс не найден';

      case 500:
        return 'Ошибка сервера. Попробуйте позже';

      case 503:
        return 'Сервис временно недоступен. Попробуйте позже';

      default:
        // Специальные сообщения для OAuth ошибок
        if (error.message.includes('provider_disabled')) {
          return 'OAuth провайдер отключен. Включите Google OAuth в консоли Appwrite';
        }
        if (error.message.includes('invalid_client')) {
          return 'Неверные настройки OAuth. Проверьте Client ID и Secret';
        }
        if (error.message.includes('redirect_uri_mismatch')) {
          return 'Неверный redirect URI. Проверьте настройки в Google Console';
        }
        if (error.message.includes('access_denied')) {
          return 'Доступ запрещен. Попробуйте войти другим способом';
        }
        if (error.message.includes('Invalid URI')) {
          return 'Добавьте домен в Appwrite Console → Settings → Platforms';
        }

        return error.message || 'Произошла неожиданная ошибка. Попробуйте еще раз';
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    logoutAll,
    updateProfile,
    updatePassword,
    updateEmail,
    sendEmailVerification,
    sendPasswordRecovery,
    updatePasswordRecovery,
    loginWithGoogle,
    loginWithTelegram,
    refreshSession,
    getPreferences,
    updatePreferences,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
