'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'ru' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  ru: {
    // Header
    'nav.about': 'О нас',
    'nav.services': 'Услуги',
    'nav.partners': 'Партнеры',
    'nav.team': 'Команда',
    'nav.contacts': 'Контакты',
    'nav.login': 'Войти',
    
    // Hero
    'hero.case': 'Кейс',
    'hero.radio_promotion': 'Продвижение на радио',
    'hero.social_media': 'Социальные сети',
    'hero.tv_advertising': 'ТВ реклама',
    'hero.view_case': 'Смотреть кейс',
    'hero.start_project': 'Начать проект',
    
    // About
    'about.title': 'О нас',
    'about.description': 'H!VE Agency — ведущее агенство по продвижению артистов и треков в России и СНГ. Мы специализируемся на комплексном продвижении музыки через все доступные каналы.',
    'about.projects': 'Успешных проектов',
    'about.plays': 'Прослушиваний',
    'about.radio_stations': 'Радиостанций',
    'about.support': 'Поддержка',
    'about.mission': 'Наша миссия',
    'about.mission_text': 'Мы помогаем талантливым артистам достичь максимальной аудитории и коммерческого успеха. Наша команда профессионалов использует передовые технологии и проверенные стратегии для продвижения вашей музыки.',
    'about.start_cooperation': 'Начать сотрудничество',
    
    // Services
    'services.title': 'Услуги',
    'services.artist_tab': '#пиар #промошен #бустер',
    'services.track_tab': 'Трек',
    'services.choose_plan': 'Выбрать план',
    'services.order': 'Заказать',
    'services.popular': 'Популярный',
    
    // Partners
    'partners.title': 'Партнеры',
    'partners.description': 'Мы работаем с ведущими лейблами, радиостанциями и медиа-компаниями для максимального охвата вашей аудитории',
    'partners.radio_stations': 'Радиостанций',
    'partners.labels': 'Лейблов',
    'partners.tv_channels': 'ТВ каналов',
    'partners.projects': 'Проектов',
    'partners.become_partner': 'Хотите стать нашим партнером?',
    'partners.contact_us': 'Связаться с нами',
    
    // Team
    'team.title': 'Наша команда',
    'team.description': 'Профессионалы с многолетним опытом в музыкальной индустрии, готовые воплотить ваши амбиции в реальность',
    'team.join': 'Присоединяйтесь к нашей команде!',
    'team.join_description': 'Мы всегда ищем талантливых профессионалов для расширения нашей команды',
    'team.send_resume': 'Отправить резюме',
    
    // Contacts
    'contacts.title': 'Контакты',
    'contacts.description': 'Готовы обсудить ваш проект? Свяжитесь с нами любым удобным способом',
    'contacts.send_message': 'Отправить сообщение',
    'contacts.name': 'Имя',
    'contacts.message': 'Сообщение',
    'contacts.send': 'Отправить',
    'contacts.phone': 'Телефон',
    'contacts.email': 'Email',
    'contacts.address': 'Адрес',
    'contacts.social_media': 'Социальные сети',
    'contacts.working_hours': 'Время работы',
    
    // Footer
    'footer.description': 'Ведущее агенство по продвижению артистов и треков. Мы помогаем талантам достичь новых высот в музыкальной индустрии.',
    'footer.newsletter': 'Подпишитесь на новости',
    'footer.newsletter_description': 'Получайте последние новости музыкальной индустрии',
    'footer.subscribe': 'Подписаться',
    'footer.rights': 'Все права защищены.',
    
    // Common
    'common.loading': 'Загрузка...',
    'common.error': 'Ошибка',
    'common.success': 'Успешно',
  },
  en: {
    // Header
    'nav.about': 'About',
    'nav.services': 'Services',
    'nav.partners': 'Partners',
    'nav.team': 'Team',
    'nav.contacts': 'Contacts',
    'nav.login': 'Login',
    
    // Hero
    'hero.case': 'Case Study',
    'hero.radio_promotion': 'Radio Promotion',
    'hero.social_media': 'Social Media',
    'hero.tv_advertising': 'TV Advertising',
    'hero.view_case': 'View Case',
    'hero.start_project': 'Start Project',
    
    // About
    'about.title': 'About Us',
    'about.description': 'H!VE Agency is a leading agency for promoting artists and tracks in Russia and CIS. We specialize in comprehensive music promotion through all available channels.',
    'about.projects': 'Successful Projects',
    'about.plays': 'Plays',
    'about.radio_stations': 'Radio Stations',
    'about.support': 'Support',
    'about.mission': 'Our Mission',
    'about.mission_text': 'We help talented artists reach maximum audience and commercial success. Our team of professionals uses cutting-edge technologies and proven strategies to promote your music.',
    'about.start_cooperation': 'Start Cooperation',
    
    // Services
    'services.title': 'Services',
    'services.artist_tab': '#PR #Promotion #Booster',
    'services.track_tab': 'Track',
    'services.choose_plan': 'Choose Plan',
    'services.order': 'Order',
    'services.popular': 'Popular',
    
    // Partners
    'partners.title': 'Partners',
    'partners.description': 'We work with leading labels, radio stations and media companies for maximum reach of your audience',
    'partners.radio_stations': 'Radio Stations',
    'partners.labels': 'Labels',
    'partners.tv_channels': 'TV Channels',
    'partners.projects': 'Projects',
    'partners.become_partner': 'Want to become our partner?',
    'partners.contact_us': 'Contact Us',
    
    // Team
    'team.title': 'Our Team',
    'team.description': 'Professionals with years of experience in the music industry, ready to turn your ambitions into reality',
    'team.join': 'Join our team!',
    'team.join_description': 'We are always looking for talented professionals to expand our team',
    'team.send_resume': 'Send Resume',
    
    // Contacts
    'contacts.title': 'Contacts',
    'contacts.description': 'Ready to discuss your project? Contact us in any convenient way',
    'contacts.send_message': 'Send Message',
    'contacts.name': 'Name',
    'contacts.message': 'Message',
    'contacts.send': 'Send',
    'contacts.phone': 'Phone',
    'contacts.email': 'Email',
    'contacts.address': 'Address',
    'contacts.social_media': 'Social Media',
    'contacts.working_hours': 'Working Hours',
    
    // Footer
    'footer.description': 'Leading agency for artist and track promotion. We help talents reach new heights in the music industry.',
    'footer.newsletter': 'Subscribe to Newsletter',
    'footer.newsletter_description': 'Get the latest music industry news',
    'footer.subscribe': 'Subscribe',
    'footer.rights': 'All rights reserved.',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
  },
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider = ({ children }: LanguageProviderProps) => {
  const [language, setLanguage] = useState<Language>('ru');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
