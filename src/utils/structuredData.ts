// Структурированные данные для SEO

export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "HIVE Agency",
  "alternateName": "HIVE Музыкальное Агентство",
  "url": "https://hiveagency.com",
  "logo": "https://hiveagency.com/images/logo.png",
  "description": "Профессиональное музыкальное агентство, специализирующееся на продвижении артистов и треков в социальных сетях, на радио и стриминговых платформах.",
  "foundingDate": "2020",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "******-555-0123",
    "contactType": "customer service",
    "availableLanguage": ["Russian", "English"],
    "areaServed": "RU"
  },
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Тверская улица, 1",
    "addressLocality": "Москва",
    "addressRegion": "Москва",
    "postalCode": "125009",
    "addressCountry": "RU"
  },
  "sameAs": [
    "https://instagram.com/hiveagency",
    "https://t.me/hiveagency",
    "https://vk.com/hiveagency",
    "https://youtube.com/@hiveagency"
  ],
  "serviceArea": {
    "@type": "Country",
    "name": "Russia"
  }
};

export const serviceSchema = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Продвижение музыки",
  "description": "Комплексные услуги по продвижению музыкальных артистов и треков",
  "provider": {
    "@type": "Organization",
    "name": "HIVE Agency"
  },
  "serviceType": "Music Promotion",
  "areaServed": {
    "@type": "Country",
    "name": "Russia"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Услуги продвижения музыки",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Продвижение в Instagram",
          "description": "Таргетированные рекламные кампании и создание контента для Instagram"
        },
        "price": "15000",
        "priceCurrency": "RUB"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "YouTube продвижение",
          "description": "Оптимизация канала, создание контента и продвижение видео"
        },
        "price": "25000",
        "priceCurrency": "RUB"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Радио ротация",
          "description": "Размещение треков на радиостанциях по всей России"
        },
        "price": "35000",
        "priceCurrency": "RUB"
      }
    ]
  }
};

export const breadcrumbSchema = (items: Array<{ name: string; url: string }>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": item.url
  }))
});

export const faqSchema = (faqs: Array<{ question: string; answer: string }>) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

export const reviewSchema = (reviews: Array<{
  author: string;
  rating: number;
  text: string;
  date: string;
}>) => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "HIVE Agency",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length,
    "reviewCount": reviews.length,
    "bestRating": 5,
    "worstRating": 1
  },
  "review": reviews.map(review => ({
    "@type": "Review",
    "author": {
      "@type": "Person",
      "name": review.author
    },
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": review.rating,
      "bestRating": 5,
      "worstRating": 1
    },
    "reviewBody": review.text,
    "datePublished": review.date
  }))
});

export const musicGroupSchema = (artist: {
  name: string;
  description: string;
  image: string;
  genre: string[];
  foundingDate?: string;
  members?: string[];
}) => ({
  "@context": "https://schema.org",
  "@type": "MusicGroup",
  "name": artist.name,
  "description": artist.description,
  "image": artist.image,
  "genre": artist.genre,
  "foundingDate": artist.foundingDate,
  "member": artist.members?.map(member => ({
    "@type": "Person",
    "name": member
  }))
});

export const musicRecordingSchema = (track: {
  name: string;
  artist: string;
  duration: string;
  genre: string;
  recordingOf?: string;
  datePublished?: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "MusicRecording",
  "name": track.name,
  "byArtist": {
    "@type": "MusicGroup",
    "name": track.artist
  },
  "duration": track.duration,
  "genre": track.genre,
  "recordingOf": track.recordingOf,
  "datePublished": track.datePublished
});

export const eventSchema = (event: {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  location: {
    name: string;
    address: string;
  };
  performer: string;
  price?: number;
}) => ({
  "@context": "https://schema.org",
  "@type": "MusicEvent",
  "name": event.name,
  "description": event.description,
  "startDate": event.startDate,
  "endDate": event.endDate,
  "location": {
    "@type": "Place",
    "name": event.location.name,
    "address": event.location.address
  },
  "performer": {
    "@type": "MusicGroup",
    "name": event.performer
  },
  "offers": event.price ? {
    "@type": "Offer",
    "price": event.price,
    "priceCurrency": "RUB"
  } : undefined
});

// Схема для статей блога
export const articleSchema = (article: {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image: string;
  url: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": article.title,
  "description": article.description,
  "author": {
    "@type": "Person",
    "name": article.author
  },
  "publisher": {
    "@type": "Organization",
    "name": "HIVE Agency",
    "logo": {
      "@type": "ImageObject",
      "url": "https://hiveagency.com/images/logo.png"
    }
  },
  "datePublished": article.datePublished,
  "dateModified": article.dateModified || article.datePublished,
  "image": article.image,
  "url": article.url
});
