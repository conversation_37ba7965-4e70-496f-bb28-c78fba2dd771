import { account, ID } from '@/lib/appwrite';

export interface CreateAdminParams {
  email: string;
  password: string;
  name: string;
}

export async function createAdminUser({ email, password, name }: CreateAdminParams) {
  try {
    console.log('Creating admin user...', { email, name });

    // 1. Создаем аккаунт
    const userId = ID.unique();
    const user = await account.create(userId, email, password, name);
    console.log('User created:', user.$id);

    // 2. Логинимся
    await account.createEmailPasswordSession(email, password);
    console.log('Session created');

    // 3. Устанавливаем права администратора
    await account.updatePrefs({
      type: 'admin',
      role: 'administrator',
      permissions: ['admin_panel', 'manage_orders', 'manage_users', 'view_analytics', 'manage_settings'],
      createdAt: new Date().toISOString(),
      createdBy: 'system'
    });
    console.log('Admin preferences set');

    return {
      success: true,
      user: {
        id: user.$id,
        email: user.email,
        name: user.name
      }
    };

  } catch (error) {
    console.error('Error creating admin:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Функция для быстрого создания админа в консоли браузера
export function quickCreateAdmin() {
  const adminData = {
    email: '<EMAIL>',
    password: 'admin123456',
    name: 'HIVE Admin'
  };

  console.log('Creating quick admin with credentials:', {
    email: adminData.email,
    password: adminData.password
  });

  return createAdminUser(adminData);
}

// Экспортируем для использования в консоли браузера
if (typeof window !== 'undefined') {
  (window as any).createAdmin = quickCreateAdmin;
  (window as any).createCustomAdmin = createAdminUser;
}
