import { Client, Account, Databases, Storage, Functions, ID } from 'appwrite';

const client = new Client();

client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '');

export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const functions = new Functions(client);

export { client, ID };

// Database and Collection IDs
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '';

export const COLLECTIONS = {
  USERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_USERS || '',
  ARTISTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ARTISTS || '',
  TRACKS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TRACKS || '',
  SUBSCRIPTIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SUBSCRIPTIONS || '',
  CAMPAIGNS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CAMPAIGNS || '',
  PARTNERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_PARTNERS || '',
  TEAM_MEMBERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_TEAM_MEMBERS || '',
  CASE_STUDIES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CASE_STUDIES || '',
  SERVICES: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_SERVICES || '',
  CONTACTS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_CONTACTS || '',
  ORDERS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_ORDERS || '',
  NOTIFICATIONS: process.env.NEXT_PUBLIC_APPWRITE_COLLECTION_NOTIFICATIONS || '',
};

// Экспорт отдельных констант для обратной совместимости
export const ORDERS_COLLECTION_ID = COLLECTIONS.ORDERS;
export const NOTIFICATIONS_COLLECTION_ID = COLLECTIONS.NOTIFICATIONS;

// Storage Buckets
export const BUCKETS = {
  AVATARS: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_AVATARS || process.env.NEXT_PUBLIC_APPWRITE_BUCKET || '',
  TRACKS: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_TRACKS || process.env.NEXT_PUBLIC_APPWRITE_BUCKET || '',
  IMAGES: process.env.NEXT_PUBLIC_APPWRITE_BUCKET_IMAGES || process.env.NEXT_PUBLIC_APPWRITE_BUCKET || '',
  DEFAULT: process.env.NEXT_PUBLIC_APPWRITE_BUCKET || '',
};
