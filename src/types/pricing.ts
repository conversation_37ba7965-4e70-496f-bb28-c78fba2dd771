// Типы для системы тарифов и платежей

export interface PricingTier {
  id: string;
  name: string;
  type: 'artist' | 'track';
  price: number;
  currency: string;
  duration: number; // в днях
  features: PricingFeature[];
  popular?: boolean;
  discount?: {
    percentage: number;
    validUntil: string;
  };
  limits: {
    socialAccounts: number;
    botActivations: number;
    emailCampaigns: number;
    telegramMembers: number;
    analyticsReports: number;
  };
}

export interface PricingFeature {
  id: string;
  name: string;
  description: string;
  included: boolean;
  limit?: number;
  icon?: string;
}

export interface OrderWithPayment {
  id: string;
  userId: string;
  pricingTierId: string;
  status: 'draft' | 'pending_payment' | 'paid' | 'in_progress' | 'completed' | 'cancelled';
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'card' | 'crypto' | 'sbp';
  paymentId?: string;
  amount: number;
  currency: string;
  createdAt: string;
  paidAt?: string;
  completedAt?: string;
  
  // Данные клиента
  clientInfo: {
    name: string;
    email: string;
    phone?: string;
    telegramUsername?: string;
  };
  
  // Данные для продвижения
  promotionData: {
    artistName?: string;
    trackTitle?: string;
    genre: string;
    targetAudience: string[];
    socialLinks: {
      instagram?: string;
      youtube?: string;
      spotify?: string;
      vk?: string;
      telegram?: string;
    };
    assets: {
      trackUrl?: string;
      videoUrl?: string;
      coverImage?: string;
      additionalFiles?: string[];
    };
  };
  
  // Назначенные ресурсы
  assignedResources?: {
    managerId: string;
    socialAccounts: SocialAccount[];
    activeBots: PromotionBot[];
    emailCampaigns: EmailCampaign[];
    telegramGroups: TelegramGroup[];
  };
}

export interface SocialAccount {
  id: string;
  platform: 'instagram' | 'youtube' | 'tiktok' | 'vk' | 'twitter';
  username: string;
  followers: number;
  engagement: number;
  niche: string[];
  isActive: boolean;
  credentials: {
    accessToken?: string;
    refreshToken?: string;
    apiKey?: string;
  };
  stats: {
    postsCount: number;
    avgLikes: number;
    avgComments: number;
    lastActivity: string;
  };
}

export interface PromotionBot {
  id: string;
  name: string;
  platform: 'instagram' | 'youtube' | 'tiktok' | 'vk' | 'twitter' | 'telegram';
  type: 'engagement' | 'content' | 'outreach' | 'scraper' | 'analytics';
  isActive: boolean;
  config: {
    targetKeywords: string[];
    activityLevel: 'low' | 'medium' | 'high';
    responseStyle: 'casual' | 'professional' | 'trendy';
    workingHours: {
      start: string;
      end: string;
      timezone: string;
    };
    limits: {
      dailyActions: number;
      hourlyActions: number;
    };
  };
  capabilities: BotCapability[];
  stats: {
    totalActions: number;
    successRate: number;
    lastActivity: string;
    generatedLeads: number;
  };
}

export interface BotCapability {
  type: 'comment' | 'like' | 'follow' | 'dm' | 'post' | 'story' | 'scrape' | 'analyze';
  enabled: boolean;
  settings: Record<string, unknown>;
}

export interface EmailCampaign {
  id: string;
  name: string;
  type: 'newsletter' | 'promotion' | 'follow_up' | 'event';
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'paused';
  targetList: string[];
  template: {
    subject: string;
    content: string;
    design: string;
  };
  schedule: {
    sendAt: string;
    timezone: string;
    frequency?: 'once' | 'daily' | 'weekly' | 'monthly';
  };
  stats: {
    sent: number;
    opened: number;
    clicked: number;
    unsubscribed: number;
  };
}

export interface TelegramGroup {
  id: string;
  name: string;
  username: string;
  type: 'channel' | 'group' | 'supergroup';
  membersCount: number;
  isActive: boolean;
  botToken: string;
  settings: {
    autoPost: boolean;
    welcomeMessage: string;
    moderationLevel: 'low' | 'medium' | 'high';
    allowedContentTypes: string[];
  };
  stats: {
    dailyMessages: number;
    activeMembers: number;
    engagement: number;
  };
}

export interface PromotionStrategy {
  id: string;
  name: string;
  type: 'viral' | 'organic' | 'paid' | 'influencer' | 'community';
  platforms: string[];
  timeline: {
    phase: string;
    duration: number;
    actions: StrategyAction[];
  }[];
  budget: {
    total: number;
    allocation: Record<string, number>;
  };
  kpis: {
    metric: string;
    target: number;
    current: number;
  }[];
}

export interface StrategyAction {
  type: string;
  description: string;
  platform: string;
  automated: boolean;
  botId?: string;
  scheduledAt?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
}
