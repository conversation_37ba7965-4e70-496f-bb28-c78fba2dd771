export interface User {
  $id: string;
  name: string;
  email: string;
  emailVerification?: boolean;
  phoneVerification?: boolean;
  phone?: string;
  avatar?: string;
  bio?: string;
  links?: string[];
  type: 'artist' | 'label' | 'admin';
  subscription_plan?: string;
  created_at: string;
  updated_at: string;
  prefs?: Record<string, unknown>; // User preferences from Appwrite
  password?: string; // Only used for updates, not stored
  // Дополнительные поля для дашборда
  verified?: boolean;
  tier?: string;
  followers?: number;
  monthlyListeners?: number;
}

export interface Artist {
  $id: string;
  user_id: string;
  stage_name: string;
  genre: string[];
  social_links: {
    instagram?: string;
    tiktok?: string;
    youtube?: string;
    spotify?: string;
    apple_music?: string;
  };
  bio: string;
  avatar?: string;
  verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface Track {
  $id: string;
  user_id: string;
  title: string;
  artist: string;
  genre: string[];
  release_date: string;
  cover_art?: string;
  audio_file?: string;
  status: 'draft' | 'pending' | 'approved' | 'released';
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  $id: string;
  user_id: string;
  plan_type: '3000' | '5000' | '10000';
  start_date: string;
  end_date: string;
  status: 'active' | 'expired' | 'cancelled';
  features: string[];
  created_at: string;
  updated_at: string;
}

export interface Campaign {
  $id: string;
  user_id: string;
  track_id?: string;
  artist_id?: string;
  type: 'track' | 'artist';
  budget: number;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  metrics: {
    radio_plays?: number;
    social_reach?: number;
    tv_mentions?: number;
    email_opens?: number;
  };
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
}

export interface Partner {
  $id: string;
  name: string;
  logo: string;
  description: string;
  website?: string;
  type: 'label' | 'radio' | 'club' | 'media';
  created_at: string;
  updated_at: string;
}

export interface TeamMember {
  $id: string;
  name: string;
  position: string;
  photo: string;
  bio: string;
  social_links: {
    instagram?: string;
    linkedin?: string;
    twitter?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CaseStudy {
  $id: string;
  title: string;
  description: string;
  artist: string;
  results: {
    metric: string;
    value: string;
  }[];
  images: string[];
  featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface Service {
  $id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
  type: 'track' | 'artist';
  created_at: string;
  updated_at: string;
}

export interface Contact {
  $id: string;
  name: string;
  email: string;
  message: string;
  type: 'general' | 'artist' | 'label' | 'booking';
  status: 'new' | 'in_progress' | 'resolved';
  created_at: string;
  updated_at: string;
}
