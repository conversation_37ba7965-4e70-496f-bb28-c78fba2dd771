export interface NotificationData {
  id: string;
  type: NotificationType;
  category: NotificationCategory;
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  isRead: boolean;
  isImportant: boolean;
  priority: NotificationPriority;
  relatedOrderId?: string;
  relatedUserId?: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
  expiresAt?: string;
  avatar?: string;
  icon?: React.ReactNode;
}

export type NotificationType = 
  | 'order_created'           // Заказ создан
  | 'order_confirmed'         // Заказ подтвержден
  | 'order_in_progress'       // Заказ в работе
  | 'order_completed'         // Заказ выполнен
  | 'order_cancelled'         // Заказ отменен
  | 'order_requires_info'     // Требуется дополнительная информация
  | 'order_deadline_soon'     // Приближается дедлайн
  | 'payment_received'        // Платеж получен
  | 'payment_failed'          // Ошибка платежа
  | 'payment_refunded'        // Возврат средств
  | 'payment_reminder'        // Напоминание об оплате
  | 'system_maintenance'      // Техническое обслуживание
  | 'system_update'           // Обновление системы
  | 'system_announcement'     // Системное объявление
  | 'promo_discount'          // Скидка/акция
  | 'promo_new_service'       // Новая услуга
  | 'promo_referral'          // Реферальная программа
  | 'message_new'             // Новое сообщение
  | 'message_reply'           // Ответ на сообщение
  | 'review_request'          // Запрос на отзыв
  | 'review_received'         // Получен отзыв
  | 'analytics_report'        // Отчет по аналитике
  | 'milestone_reached'       // Достижение цели
  | 'welcome'                 // Приветствие
  | 'profile_incomplete';     // Неполный профиль

export type NotificationCategory = 'info' | 'success' | 'warning' | 'error';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface NotificationTemplate {
  type: NotificationType;
  category: NotificationCategory;
  priority: NotificationPriority;
  isImportant: boolean;
  titleTemplate: string;
  messageTemplate: string;
  actionText?: string;
  actionUrlTemplate?: string;
  icon?: string;
  expiresInHours?: number;
}

// Шаблоны уведомлений
export const NOTIFICATION_TEMPLATES: Record<NotificationType, NotificationTemplate> = {
  // Заказы
  order_created: {
    type: 'order_created',
    category: 'success',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Заказ создан',
    messageTemplate: 'Ваш заказ "{{orderTitle}}" успешно создан. Мы свяжемся с вами в ближайшее время.',
    actionText: 'Посмотреть заказ',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '📦'
  },
  order_confirmed: {
    type: 'order_confirmed',
    category: 'success',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Заказ подтвержден',
    messageTemplate: 'Ваш заказ "{{orderTitle}}" подтвержден. Работы начнутся {{startDate}}.',
    actionText: 'Посмотреть детали',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '✅'
  },
  order_in_progress: {
    type: 'order_in_progress',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Заказ в работе',
    messageTemplate: 'Работы по заказу "{{orderTitle}}" начались. Прогресс: {{progress}}%.',
    actionText: 'Отслеживать прогресс',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '🚀'
  },
  order_completed: {
    type: 'order_completed',
    category: 'success',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Заказ выполнен',
    messageTemplate: 'Ваш заказ "{{orderTitle}}" успешно завершен! Все материалы готовы к использованию.',
    actionText: 'Посмотреть результат',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '🎉'
  },
  order_cancelled: {
    type: 'order_cancelled',
    category: 'warning',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Заказ отменен',
    messageTemplate: 'Заказ "{{orderTitle}}" был отменен. {{reason}}',
    actionText: 'Подробности',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '❌'
  },
  order_requires_info: {
    type: 'order_requires_info',
    category: 'warning',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Требуется дополнительная информация',
    messageTemplate: 'Для продолжения работы над заказом "{{orderTitle}}" нам нужна дополнительная информация.',
    actionText: 'Предоставить информацию',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '❓'
  },
  order_deadline_soon: {
    type: 'order_deadline_soon',
    category: 'warning',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Приближается дедлайн',
    messageTemplate: 'До завершения заказа "{{orderTitle}}" осталось {{daysLeft}} дней.',
    actionText: 'Посмотреть заказ',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}',
    icon: '⏰'
  },

  // Платежи
  payment_received: {
    type: 'payment_received',
    category: 'success',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Платеж получен',
    messageTemplate: 'Платеж на сумму {{amount}} ₽ успешно обработан. Работы начнутся в течение 24 часов.',
    actionText: 'Посмотреть счет',
    actionUrlTemplate: '/dashboard/billing',
    icon: '💳'
  },
  payment_failed: {
    type: 'payment_failed',
    category: 'error',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Ошибка платежа',
    messageTemplate: 'Не удалось обработать платеж на сумму {{amount}} ₽. Попробуйте еще раз.',
    actionText: 'Повторить платеж',
    actionUrlTemplate: '/dashboard/billing/retry/{{paymentId}}',
    icon: '❌'
  },
  payment_refunded: {
    type: 'payment_refunded',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Возврат средств',
    messageTemplate: 'Возврат на сумму {{amount}} ₽ обработан. Средства поступят на ваш счет в течение 3-5 рабочих дней.',
    actionText: 'Подробности',
    actionUrlTemplate: '/dashboard/billing',
    icon: '💰'
  },
  payment_reminder: {
    type: 'payment_reminder',
    category: 'warning',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Напоминание об оплате',
    messageTemplate: 'Не забудьте оплатить заказ "{{orderTitle}}" до {{dueDate}}.',
    actionText: 'Оплатить',
    actionUrlTemplate: '/dashboard/billing/pay/{{orderId}}',
    icon: '💳'
  },

  // Система
  system_maintenance: {
    type: 'system_maintenance',
    category: 'warning',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Техническое обслуживание',
    messageTemplate: 'Плановое техническое обслуживание {{date}} с {{startTime}} до {{endTime}}.',
    actionText: 'Подробнее',
    actionUrlTemplate: '/maintenance',
    icon: '🔧'
  },
  system_update: {
    type: 'system_update',
    category: 'info',
    priority: 'low',
    isImportant: false,
    titleTemplate: 'Обновление системы',
    messageTemplate: 'Добавлены новые функции: {{features}}. Попробуйте прямо сейчас!',
    actionText: 'Что нового',
    actionUrlTemplate: '/updates',
    icon: '🆕'
  },
  system_announcement: {
    type: 'system_announcement',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: '{{title}}',
    messageTemplate: '{{message}}',
    actionText: 'Подробнее',
    actionUrlTemplate: '{{url}}',
    icon: '📢'
  },

  // Промо
  promo_discount: {
    type: 'promo_discount',
    category: 'success',
    priority: 'high',
    isImportant: true,
    titleTemplate: 'Специальное предложение',
    messageTemplate: 'Скидка {{discount}}% на {{services}} до {{endDate}}. Не упустите возможность!',
    actionText: 'Воспользоваться',
    actionUrlTemplate: '/pricing?promo={{promoCode}}',
    icon: '🎉',
    expiresInHours: 168 // 7 дней
  },
  promo_new_service: {
    type: 'promo_new_service',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Новая услуга',
    messageTemplate: 'Теперь доступно: {{serviceName}}. {{description}}',
    actionText: 'Узнать больше',
    actionUrlTemplate: '/services/{{serviceId}}',
    icon: '🆕'
  },
  promo_referral: {
    type: 'promo_referral',
    category: 'success',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Реферальная программа',
    messageTemplate: 'Приглашайте друзей и получайте {{bonus}} ₽ за каждого нового клиента!',
    actionText: 'Пригласить друзей',
    actionUrlTemplate: '/referral',
    icon: '👥'
  },

  // Сообщения
  message_new: {
    type: 'message_new',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Новое сообщение',
    messageTemplate: 'Сообщение от {{senderName}}: {{messagePreview}}',
    actionText: 'Прочитать',
    actionUrlTemplate: '/dashboard/messages/{{messageId}}',
    icon: '💬'
  },
  message_reply: {
    type: 'message_reply',
    category: 'info',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Ответ на сообщение',
    messageTemplate: '{{senderName}} ответил на ваше сообщение: {{messagePreview}}',
    actionText: 'Посмотреть',
    actionUrlTemplate: '/dashboard/messages/{{messageId}}',
    icon: '↩️'
  },

  // Отзывы
  review_request: {
    type: 'review_request',
    category: 'info',
    priority: 'low',
    isImportant: false,
    titleTemplate: 'Оцените нашу работу',
    messageTemplate: 'Как вам наша работа по заказу "{{orderTitle}}"? Ваш отзыв поможет нам стать лучше.',
    actionText: 'Оставить отзыв',
    actionUrlTemplate: '/dashboard/orders/{{orderId}}/review',
    icon: '⭐'
  },
  review_received: {
    type: 'review_received',
    category: 'success',
    priority: 'low',
    isImportant: false,
    titleTemplate: 'Спасибо за отзыв!',
    messageTemplate: 'Ваш отзыв получен. Спасибо за оценку нашей работы!',
    actionText: 'Посмотреть отзыв',
    actionUrlTemplate: '/dashboard/reviews',
    icon: '🙏'
  },

  // Аналитика
  analytics_report: {
    type: 'analytics_report',
    category: 'info',
    priority: 'low',
    isImportant: false,
    titleTemplate: 'Отчет готов',
    messageTemplate: 'Готов отчет по продвижению за {{period}}. Рост аудитории: {{growth}}%.',
    actionText: 'Посмотреть отчет',
    actionUrlTemplate: '/dashboard/analytics/{{reportId}}',
    icon: '📊'
  },
  milestone_reached: {
    type: 'milestone_reached',
    category: 'success',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Цель достигнута!',
    messageTemplate: 'Поздравляем! Достигнута цель: {{milestone}}. {{details}}',
    actionText: 'Посмотреть статистику',
    actionUrlTemplate: '/dashboard/analytics',
    icon: '🎯'
  },

  // Прочее
  welcome: {
    type: 'welcome',
    category: 'success',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Добро пожаловать в HIVE Agency!',
    messageTemplate: 'Спасибо за регистрацию! Изучите наши услуги и создайте первый заказ.',
    actionText: 'Начать',
    actionUrlTemplate: '/dashboard',
    icon: '👋'
  },
  profile_incomplete: {
    type: 'profile_incomplete',
    category: 'warning',
    priority: 'normal',
    isImportant: false,
    titleTemplate: 'Заполните профиль',
    messageTemplate: 'Заполните профиль для получения персональных рекомендаций и лучшего сервиса.',
    actionText: 'Заполнить профиль',
    actionUrlTemplate: '/dashboard/profile',
    icon: '👤'
  }
};
