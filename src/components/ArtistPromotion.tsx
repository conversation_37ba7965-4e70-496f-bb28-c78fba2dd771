'use client';

import { useState } from 'react';
import {
  User,
  Image,
  Link,
  Video,
  Headphones,
  Globe,
  Radio,
  Mail,
  Building,
  Users,
  BarChart3,
  CheckCircle,
  Mic
} from 'lucide-react';
import { FaVk, FaTelegram } from 'react-icons/fa';
import OrderCreatorStub from '@/components/OrderCreatorStub';

const ArtistPromotion = () => {
  const [activeSection, setActiveSection] = useState('profile');
  const [selectedService, setSelectedService] = useState<{id: string; title: string; price: string; description: string} | null>(null);
  const [showOrderCreator, setShowOrderCreator] = useState(false);


  const promotionServices = [
    {
      id: 'youtube-promo',
      title: 'Видео на YouTube',
      description: 'Создание и продвижение видео контента',
      icon: <Video className="w-6 h-6" />,
      features: ['Создание видео', 'SEO оптимизация', 'Продвижение'],
      price: '15,000 ₽'
    },
    {
      id: 'instagram-promo',
      title: 'Продвижение в Instagram',
      description: 'Реклама и органическое продвижение',
      icon: <Image className="w-6 h-6" />,
      features: ['Stories реклама', 'Посты', 'Reels продвижение'],
      price: '12,000 ₽'
    },
    {
      id: 'vk-promo',
      title: 'Продвижение в VK',
      description: 'Реклама и продвижение в VKontakte',
      icon: <FaVk className="w-6 h-6" />,
      features: ['Таргетированная реклама', 'Группы', 'Музыка VK'],
      price: '10,000 ₽'
    },
    {
      id: 'email-campaign',
      title: 'Email рассылка',
      description: 'Рассылка по базе лейблов и промоутеров',
      icon: <Mail className="w-6 h-6" />,
      features: ['База 10,000+ контактов', 'Персонализация', 'Отчеты'],
      price: '8,000 ₽'
    },
    {
      id: 'club-outreach',
      title: 'Рассылка по клубам',
      description: 'Отправка промо материалов в клубы',
      icon: <Building className="w-6 h-6" />,
      features: ['500+ клубов', 'Персональные контакты', 'Follow-up'],
      price: '20,000 ₽'
    },
    {
      id: 'artist-network',
      title: 'Рассылка по артистам',
      description: 'Networking с другими артистами',
      icon: <Users className="w-6 h-6" />,
      features: ['Коллаборации', 'Фичеринги', 'Нетворкинг'],
      price: '15,000 ₽'
    },
    {
      id: 'telegram-group',
      title: 'Группа в Telegram',
      description: 'Создание и продвижение Telegram канала',
      icon: <FaTelegram className="w-6 h-6" />,
      features: ['Создание канала', 'Контент план', 'Продвижение'],
      price: '7,000 ₽'
    },
    {
      id: 'music-social',
      title: 'Музыкальные соцсети',
      description: 'Продвижение на специализированных платформах',
      icon: <Headphones className="w-6 h-6" />,
      features: ['SoundCloud', 'Bandcamp', 'ReverbNation'],
      price: '12,000 ₽'
    },
    {
      id: 'music-forums',
      title: 'Музыкальные форумы',
      description: 'Продвижение на форумах и сообществах',
      icon: <Globe className="w-6 h-6" />,
      features: ['Форумы', 'Reddit', 'Discord сообщества'],
      price: '5,000 ₽'
    }
  ];

  const sections = [
    { id: 'profile', title: 'Профиль артиста', icon: <User className="w-5 h-5" /> },
    { id: 'services', title: 'Услуги продвижения', icon: <BarChart3 className="w-5 h-5" /> },
    { id: 'statistics', title: 'Статистика', icon: <BarChart3 className="w-5 h-5" /> }
  ];

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
      {/* Section Navigation */}
      <div className="flex space-x-4 border-b border-gray-700">
        {sections.map((section) => (
          <button
            key={section.id}
            onClick={() => setActiveSection(section.id)}
            className={`flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors ${
              activeSection === section.id
                ? 'border-red-500 text-red-400'
                : 'border-transparent text-gray-400 hover:text-white'
            }`}
          >
            {section.icon}
            <span>{section.title}</span>
          </button>
        ))}
      </div>

      {/* Profile Section */}
      {activeSection === 'profile' && (
        <div className="grid grid-cols-1 lg:grid-cols-2" style={{ gap: '30px' }}>
          {/* Basic Info */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
              <h3 className="text-lg font-semibold text-white flex items-center" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                <User className="w-5 h-5 text-red-500" style={{ marginRight: '10px' }} />
                Основная информация
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>Имя артиста</label>
                  <input
                    type="text"
                    placeholder="Введите имя артиста"
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    style={{ padding: '15px 20px', lineHeight: '1.4' }}
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>Биография</label>
                  <textarea
                    placeholder="Расскажите о себе..."
                    rows={4}
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    style={{ padding: '15px 20px', lineHeight: '1.6' }}
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
              <h3 className="text-lg font-semibold text-white flex items-center" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                <Image className="w-5 h-5 text-red-500" style={{ marginRight: '10px' }} />
                Добавить аватар
              </h3>
              <div className="border-2 border-dashed border-gray-600 rounded-lg text-center" style={{ padding: '30px' }}>
                <input type="file" accept="image/*" className="hidden" id="avatar-upload" />
                <label htmlFor="avatar-upload" className="cursor-pointer">
                  <div className="text-gray-400" style={{ marginBottom: '15px', lineHeight: '1.6' }}>Загрузите фото профиля</div>
                  <div className="bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors inline-block" style={{ padding: '12px 20px', lineHeight: '1.4' }}>
                    Выбрать файл
                  </div>
                </label>
              </div>
            </div>
          </div>

          {/* Links and Media */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
              <h3 className="text-lg font-semibold text-white flex items-center" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                <Link className="w-5 h-5 text-red-500" style={{ marginRight: '10px' }} />
                Ссылки и контакты
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>Последний релиз (ссылка)</label>
                  <input
                    type="url"
                    placeholder="https://..."
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    style={{ padding: '15px 20px', lineHeight: '1.4' }}
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>Telegram для связи</label>
                  <input
                    type="text"
                    placeholder="@username"
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    style={{ padding: '15px 20px', lineHeight: '1.4' }}
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
              <h3 className="text-lg font-semibold text-white flex items-center" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                <Video className="w-5 h-5 text-red-500" style={{ marginRight: '10px' }} />
                Видео контент
              </h3>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>YouTube видео (ссылка)</label>
                <input
                  type="url"
                  placeholder="https://youtube.com/..."
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '15px 20px', lineHeight: '1.4' }}
                />
              </div>
            </div>

            <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
              <h3 className="text-lg font-semibold text-white flex items-center" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                <Mic className="w-5 h-5 text-red-500" style={{ marginRight: '10px' }} />
                DJ активность
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                <div className="flex items-center" style={{ gap: '15px' }}>
                  <input
                    type="checkbox"
                    id="is-dj"
                    className="w-4 h-4 text-red-500 bg-gray-700 border-gray-600 rounded focus:ring-red-500"
                  />
                  <label htmlFor="is-dj" className="text-white" style={{ lineHeight: '1.4' }}>Вы диджей?</label>
                </div>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '10px', lineHeight: '1.4' }}>Ссылка на микс</label>
                  <input
                    type="url"
                    placeholder="https://..."
                    className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                    style={{ padding: '15px 20px', lineHeight: '1.4' }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Services Section */}
      {activeSection === 'services' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3" style={{ gap: '30px' }}>
          {promotionServices.map((service) => (
            <div key={service.id} className="bg-gray-800/30 rounded-xl border border-gray-700/30 hover:border-red-500/30 transition-colors" style={{ padding: '20px' }}>
              <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                <div className="text-red-500">{service.icon}</div>
                <div className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>{service.price}</div>
              </div>
              <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '15px', lineHeight: '1.4' }}>{service.title}</h3>
              <p className="text-gray-400 text-sm" style={{ marginBottom: '20px', lineHeight: '1.6' }}>{service.description}</p>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginBottom: '20px' }}>
                {service.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-300" style={{ gap: '10px' }}>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span style={{ lineHeight: '1.4' }}>{feature}</span>
                  </div>
                ))}
              </div>

              <button
                onClick={() => {
                  setSelectedService(service);
                  setShowOrderCreator(true);
                }}
                className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                style={{ padding: '15px 20px', lineHeight: '1.4' }}
              >
                Заказать
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Statistics Section */}
      {activeSection === 'statistics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4" style={{ gap: '30px' }}>
          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
              <div className="text-red-500">
                <BarChart3 className="w-8 h-8" />
              </div>
              <div className="text-sm text-green-400" style={{ lineHeight: '1.4' }}>+12%</div>
            </div>
            <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>2.5M</div>
            <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Прослушиваний</div>
          </div>

          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
              <div className="text-red-500">
                <Users className="w-8 h-8" />
              </div>
              <div className="text-sm text-green-400" style={{ lineHeight: '1.4' }}>+8%</div>
            </div>
            <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>125K</div>
            <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Подписчиков</div>
          </div>

          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
              <div className="text-red-500">
                <Radio className="w-8 h-8" />
              </div>
              <div className="text-sm text-green-400" style={{ lineHeight: '1.4' }}>+25%</div>
            </div>
            <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>45</div>
            <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Радиостанций</div>
          </div>

          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
              <div className="text-red-500">
                <Globe className="w-8 h-8" />
              </div>
              <div className="text-sm text-green-400" style={{ lineHeight: '1.4' }}>+15%</div>
            </div>
            <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>12</div>
            <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>Стран</div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end" style={{ gap: '20px', paddingTop: '30px' }}>
        <button className="bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors" style={{ padding: '15px 30px', lineHeight: '1.4' }}>
          Сохранить черновик
        </button>
        <button className="bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors" style={{ padding: '15px 30px', lineHeight: '1.4' }}>
          Запустить кампанию
        </button>
      </div>

      {/* Order Creator Modal */}
      {showOrderCreator && selectedService && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
          <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-gray-900 border-b border-gray-800" style={{ padding: '20px' }}>
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
                  Создание заказа
                </h2>
                <button
                  onClick={() => {
                    setShowOrderCreator(false);
                    setSelectedService(null);
                  }}
                  className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                >
                  ×
                </button>
              </div>
            </div>
            <div style={{ padding: '20px' }}>
              <OrderCreatorStub
                serviceType="artist"
                packageType={selectedService.id}
                title={selectedService.title}
                description={selectedService.description}
                price={parseInt(selectedService.price.replace(/[^\d]/g, ''))}
                onOrderCreated={(orderId: string) => {
                  setShowOrderCreator(false);
                  setSelectedService(null);
                  alert(`Заказ ${orderId} создан успешно!`);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArtistPromotion;
