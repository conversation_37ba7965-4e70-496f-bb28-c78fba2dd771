'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, Target, Zap, Users, Play, Heart, 
  Instagram, Youtube, Music, CheckCircle, ShoppingCart 
} from 'lucide-react';

interface ServiceCard {
  id: string;
  title: string;
  platform: string;
  description: string;
  estimatedReach: string;
  price: number;
  icon: React.ReactNode;
  color: string;
  features: string[];
  estimatedResults: {
    plays?: string;
    followers?: string;
    engagement?: string;
  };
}

interface ServiceRecommendationsProps {
  services: ServiceCard[];
  onPurchase: (selectedServices: ServiceCard[]) => void;
}

const ServiceRecommendations = ({ services, onPurchase }: ServiceRecommendationsProps) => {
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [showDetails, setShowDetails] = useState<string | null>(null);

  const toggleService = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const getTotalPrice = () => {
    return services
      .filter(service => selectedServices.includes(service.id))
      .reduce((total, service) => total + service.price, 0);
  };

  const getTotalReach = () => {
    const selectedServicesList = services.filter(service => selectedServices.includes(service.id));
    if (selectedServicesList.length === 0) return '0';
    
    // Простая логика для подсчета общего охвата
    const totalMin = selectedServicesList.reduce((sum, service) => {
      const reach = service.estimatedReach.split(' - ')[0];
      return sum + parseInt(reach.replace(/[^\d]/g, ''));
    }, 0);
    
    const totalMax = selectedServicesList.reduce((sum, service) => {
      const reach = service.estimatedReach.split(' - ')[1] || service.estimatedReach.split(' - ')[0];
      return sum + parseInt(reach.replace(/[^\d]/g, ''));
    }, 0);
    
    return `${(totalMin / 1000).toFixed(0)}K - ${(totalMax / 1000).toFixed(1)}M`;
  };

  const handlePurchase = () => {
    const selected = services.filter(service => selectedServices.includes(service.id));
    onPurchase(selected);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="py-12"
    >
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold text-white mb-4">
          Рекомендованные <span className="text-red-500">сервисы</span>
        </h3>
        <p className="text-gray-400">
          AI подобрал оптимальный набор для достижения ваших целей
        </p>
      </div>

      {/* Service Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <AnimatePresence>
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className={`relative bg-gray-900/50 backdrop-blur-xl rounded-3xl border transition-all duration-300 cursor-pointer overflow-hidden ${
                selectedServices.includes(service.id)
                  ? 'border-red-500 shadow-lg shadow-red-500/25'
                  : 'border-gray-700/50 hover:border-gray-600'
              }`}
              onClick={() => toggleService(service.id)}
            >
              {/* Selection Indicator */}
              <AnimatePresence>
                {selectedServices.includes(service.id) && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="absolute top-4 right-4 z-10"
                  >
                    <div className="bg-red-500 rounded-full p-1">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-10`}></div>

              <div className="relative p-6">
                {/* Platform Icon */}
                <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${service.color} mb-4`}>
                  {service.icon}
                </div>

                {/* Service Info */}
                <h4 className="text-xl font-bold text-white mb-2">{service.title}</h4>
                <p className="text-gray-400 text-sm mb-4">{service.description}</p>

                {/* Metrics */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Охват:</span>
                    <span className="text-white font-semibold">{service.estimatedReach}</span>
                  </div>
                  
                  {service.estimatedResults.plays && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400 text-sm flex items-center gap-1">
                        <Play className="w-3 h-3" />
                        Прослушивания:
                      </span>
                      <span className="text-green-400 font-semibold">{service.estimatedResults.plays}</span>
                    </div>
                  )}
                  
                  {service.estimatedResults.followers && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400 text-sm flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        Подписчики:
                      </span>
                      <span className="text-blue-400 font-semibold">{service.estimatedResults.followers}</span>
                    </div>
                  )}
                  
                  {service.estimatedResults.engagement && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400 text-sm flex items-center gap-1">
                        <Heart className="w-3 h-3" />
                        Вовлечение:
                      </span>
                      <span className="text-pink-400 font-semibold">{service.estimatedResults.engagement}</span>
                    </div>
                  )}
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-white">
                    {service.price.toLocaleString()} ₽
                  </span>
                  <span className="text-gray-400 text-sm">30 дней</span>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Summary & Purchase */}
      <AnimatePresence>
        {selectedServices.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-xl rounded-3xl border border-gray-700/50 p-8"
          >
            <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
              {/* Summary */}
              <div className="text-center lg:text-left">
                <h4 className="text-2xl font-bold text-white mb-2">
                  Выбрано сервисов: {selectedServices.length}
                </h4>
                <div className="flex flex-col sm:flex-row gap-4 text-gray-300">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-green-400" />
                    <span>Общий охват: {getTotalReach()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="w-5 h-5 text-blue-400" />
                    <span>Прогноз: 1.5M - 2.8M слушателей</span>
                  </div>
                </div>
              </div>

              {/* Price & Purchase */}
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-4">
                  {getTotalPrice().toLocaleString()} ₽
                </div>
                <button
                  onClick={handlePurchase}
                  className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg shadow-red-500/25 flex items-center gap-3"
                >
                  <ShoppingCart className="w-6 h-6" />
                  Купить HIVE
                </button>
                <p className="text-gray-400 text-sm mt-2">
                  Безопасная оплата • Гарантия результата
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ServiceRecommendations;
