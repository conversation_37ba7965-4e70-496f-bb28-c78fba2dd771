'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Bell,
  X,
  Check,
  AlertCircle,
  CheckCircle,
  Info,
  Clock,
  ExternalLink,
  Trash2,
  Mail,
  Settings
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'order' | 'payment' | 'system' | 'promo' | 'message';
  category: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  isRead: boolean;
  isImportant: boolean;
  priority: 'low' | 'normal' | 'high';
  relatedOrderId?: string;
  createdAt: string;
  avatar?: string;
  icon?: React.ReactNode;
}

const NotificationCenter = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'important'>('all');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Моковые данные уведомлений
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'order',
        category: 'success',
        title: 'Заказ выполнен',
        message: 'Ваш заказ "Продвижение артиста John Doe" успешно завершен. Все материалы готовы к использованию.',
        actionUrl: '/dashboard/orders/1',
        actionText: 'Посмотреть результат',
        isRead: false,
        isImportant: true,
        priority: 'high',
        relatedOrderId: 'order_123',
        createdAt: '2024-01-15T10:30:00Z',
        icon: <CheckCircle className="w-5 h-5 text-green-400" />
      },
      {
        id: '2',
        type: 'payment',
        category: 'info',
        title: 'Платеж обработан',
        message: 'Платеж на сумму 35,000 ₽ успешно обработан. Работы по заказу начнутся в течение 24 часов.',
        actionUrl: '/dashboard/billing',
        actionText: 'Посмотреть счет',
        isRead: false,
        isImportant: false,
        priority: 'normal',
        createdAt: '2024-01-15T09:15:00Z',
        icon: <CheckCircle className="w-5 h-5 text-blue-400" />
      },
      {
        id: '3',
        type: 'order',
        category: 'warning',
        title: 'Требуется дополнительная информация',
        message: 'Для продолжения работы над заказом нам нужны ссылки на ваши социальные сети.',
        actionUrl: '/dashboard/orders/2',
        actionText: 'Предоставить информацию',
        isRead: true,
        isImportant: false,
        priority: 'normal',
        relatedOrderId: 'order_124',
        createdAt: '2024-01-14T16:45:00Z',
        icon: <AlertCircle className="w-5 h-5 text-yellow-400" />
      },
      {
        id: '4',
        type: 'system',
        category: 'info',
        title: 'Новые функции',
        message: 'Добавлена возможность отслеживания прогресса в реальном времени и новая аналитика.',
        actionUrl: '/dashboard',
        actionText: 'Попробовать',
        isRead: true,
        isImportant: false,
        priority: 'low',
        createdAt: '2024-01-14T12:00:00Z',
        icon: <Info className="w-5 h-5 text-blue-400" />
      },
      {
        id: '5',
        type: 'promo',
        category: 'success',
        title: 'Специальное предложение',
        message: 'Скидка 20% на все пакеты продвижения до конца месяца. Не упустите возможность!',
        actionUrl: '/pricing',
        actionText: 'Воспользоваться',
        isRead: false,
        isImportant: true,
        priority: 'high',
        createdAt: '2024-01-14T08:30:00Z',
        icon: <CheckCircle className="w-5 h-5 text-green-400" />
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  // Закрытие при клике вне компонента
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const importantCount = notifications.filter(n => n.isImportant && !n.isRead).length;

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.isRead;
      case 'important':
        return notification.isImportant;
      default:
        return true;
    }
  });

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, isRead: true } : n)
    );
  };

  const markAsUnread = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, isRead: false } : n)
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'success': return 'border-green-500/30 bg-green-500/10';
      case 'warning': return 'border-yellow-500/30 bg-yellow-500/10';
      case 'error': return 'border-pink-500/30 bg-pink-500/10';
      default: return 'border-blue-500/30 bg-blue-500/10';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order': return '📦';
      case 'payment': return '💳';
      case 'system': return '⚙️';
      case 'promo': return '🎉';
      case 'message': return '💬';
      default: return '📢';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Только что';
    if (diffInHours < 24) return `${diffInHours}ч назад`;
    if (diffInHours < 48) return 'Вчера';
    return date.toLocaleDateString('ru-RU');
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative w-12 h-12 rounded-xl bg-gray-800/50 border border-gray-700/50 flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700/50 transition-all duration-200"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold animate-pulse">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
        {importantCount > 0 && (
          <span className="absolute -top-1 -left-1 w-3 h-3 bg-yellow-500 rounded-full animate-bounce"></span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-14 w-96 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl z-50 max-h-[600px] overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-700/50">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">Уведомления</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Filter Tabs */}
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'Все', count: notifications.length },
                { key: 'unread', label: 'Новые', count: unreadCount },
                { key: 'important', label: 'Важные', count: importantCount }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    filter === tab.key
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  {tab.label} {tab.count > 0 && `(${tab.count})`}
                </button>
              ))}
            </div>
            
            {/* Actions */}
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="mt-3 text-sm text-blue-400 hover:text-blue-300 transition-colors"
              >
                Отметить все как прочитанные
              </button>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                <p className="text-gray-400">Нет уведомлений</p>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border-b border-gray-700/30 hover:bg-gray-800/30 transition-all duration-200 ${
                    !notification.isRead ? 'bg-gray-800/20' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div className={`w-10 h-10 rounded-xl border flex items-center justify-center flex-shrink-0 ${getCategoryColor(notification.category)}`}>
                      {notification.icon || <span className="text-lg">{getTypeIcon(notification.type)}</span>}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className={`font-semibold text-sm ${!notification.isRead ? 'text-white' : 'text-gray-300'}`}>
                              {notification.title}
                            </h4>
                            {notification.isImportant && (
                              <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                            )}
                            {!notification.isRead && (
                              <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                            )}
                          </div>
                          <p className="text-gray-400 text-sm leading-relaxed mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500 flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {formatTime(notification.createdAt)}
                            </span>
                            {notification.actionUrl && (
                              <a
                                href={notification.actionUrl}
                                className="text-xs text-red-400 hover:text-red-300 flex items-center transition-colors"
                                onClick={() => markAsRead(notification.id)}
                              >
                                {notification.actionText}
                                <ExternalLink className="w-3 h-3 ml-1" />
                              </a>
                            )}
                          </div>
                        </div>
                        
                        {/* Actions */}
                        <div className="flex items-center space-x-1 ml-2">
                          <button
                            onClick={() => notification.isRead ? markAsUnread(notification.id) : markAsRead(notification.id)}
                            className="w-6 h-6 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                            title={notification.isRead ? 'Отметить как непрочитанное' : 'Отметить как прочитанное'}
                          >
                            {notification.isRead ? <Mail className="w-3 h-3" /> : <Check className="w-3 h-3" />}
                          </button>
                          <button
                            onClick={() => deleteNotification(notification.id)}
                            className="w-6 h-6 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-red-400 transition-colors"
                            title="Удалить уведомление"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-700/50">
            <div className="flex items-center justify-between">
              <a
                href="/dashboard/notifications"
                className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
              >
                Посмотреть все уведомления
              </a>
              <button className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
