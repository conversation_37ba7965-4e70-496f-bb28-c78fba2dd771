'use client';

import { useState, useRef, useEffect } from 'react';
import {
  MessageCircle,
  X,
  Bot,
  User as UserIcon,
  Send,
  Minimize2,
  Maximize2,
  Sparkles
} from 'lucide-react';
import OfferCard from './OfferCard';
import { User } from '@/types';

interface AIAssistantProps {
  language?: 'ru' | 'en';
}

interface OfferData {
  type: 'starter' | 'pro' | 'premium' | 'custom';
  title: string;
  price: string;
  originalPrice?: string;
  description: string;
  features: string[];
  platforms: string[];
  results: string;
  popular?: boolean;
}

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  offers?: OfferData[];
  suggestions?: string[];
}

const AIAssistant: React.FC<AIAssistantProps> = ({ language = 'ru' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: language === 'ru'
        ? '👋 Привет! Я AI-помощник HIVE Agency. Помогу вам с продвижением музыки и отвечу на любые вопросы о наших услугах!'
        : '👋 Hi! I\'m HIVE Agency AI assistant. I\'ll help you with music promotion and answer any questions about our services!',
      timestamp: new Date(),
      suggestions: language === 'ru'
        ? ['🎵 Продвижение трека', '💰 Узнать цены', '📊 Аналитика', '📞 Связаться с менеджером']
        : ['🎵 Track promotion', '💰 Check prices', '📊 Analytics', '📞 Contact manager']
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock user data - в реальном приложении получаем из контекста
  const user: User | null = null; // Замените на реальные данные пользователя

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);



  // Генерация предложений
  const generateOffers = (type: 'general' | 'spotify' | 'tiktok' | 'youtube' | 'radio' | 'full'): OfferData[] => {
    const offers: Record<string, OfferData[]> = {
      general: [
        {
          type: 'starter',
          title: language === 'ru' ? 'Стартовый' : 'Starter',
          price: '$299',
          originalPrice: '$399',
          description: language === 'ru' ? 'Идеально для начала' : 'Perfect to start',
          features: language === 'ru'
            ? ['10 плейлистов Spotify', '50K охват', 'Базовая аналитика', 'Поддержка 24/7']
            : ['10 Spotify playlists', '50K reach', 'Basic analytics', '24/7 support'],
          platforms: ['Spotify'],
          results: language === 'ru' ? '+50K прослушиваний' : '+50K streams',
        },
        {
          type: 'pro',
          title: language === 'ru' ? 'Профессиональный' : 'Professional',
          price: '$599',
          originalPrice: '$799',
          description: language === 'ru' ? 'Лучший выбор' : 'Best choice',
          features: language === 'ru'
            ? ['25 плейлистов Spotify', 'TikTok кампания', '200K охват', 'Детальная аналитика', 'Персональный менеджер']
            : ['25 Spotify playlists', 'TikTok campaign', '200K reach', 'Detailed analytics', 'Personal manager'],
          platforms: ['Spotify', 'TikTok'],
          results: language === 'ru' ? '+200K прослушиваний' : '+200K streams',
          popular: true,
        },
        {
          type: 'premium',
          title: language === 'ru' ? 'Премиум' : 'Premium',
          price: '$1299',
          originalPrice: '$1599',
          description: language === 'ru' ? 'Максимальный результат' : 'Maximum result',
          features: language === 'ru'
            ? ['50+ плейлистов', 'Все платформы', '500K+ охват', 'PR кампания', 'Радио ротации', 'VIP поддержка']
            : ['50+ playlists', 'All platforms', '500K+ reach', 'PR campaign', 'Radio rotations', 'VIP support'],
          platforms: ['Spotify', 'TikTok', 'YouTube', 'Instagram', 'Radio'],
          results: language === 'ru' ? '+500K прослушиваний' : '+500K streams',
        }
      ],
      spotify: [
        {
          type: 'starter',
          title: 'Spotify Basic',
          price: '$199',
          description: language === 'ru' ? 'Базовое продвижение' : 'Basic promotion',
          features: language === 'ru'
            ? ['5 плейлистов', '25K охват', 'Органический рост', 'Отчет о результатах']
            : ['5 playlists', '25K reach', 'Organic growth', 'Results report'],
          platforms: ['Spotify'],
          results: language === 'ru' ? '+25K прослушиваний' : '+25K streams',
        },
        {
          type: 'pro',
          title: 'Spotify Pro',
          price: '$399',
          description: language === 'ru' ? 'Расширенное продвижение' : 'Advanced promotion',
          features: language === 'ru'
            ? ['15 плейлистов', '100K охват', 'Таргетинг по жанрам', 'Детальная аналитика']
            : ['15 playlists', '100K reach', 'Genre targeting', 'Detailed analytics'],
          platforms: ['Spotify'],
          results: language === 'ru' ? '+100K прослушиваний' : '+100K streams',
          popular: true,
        }
      ]
    };

    return offers[type] || offers.general;
  };

  // База знаний для AI-помощника
  const getAIResponse = (userMessage: string): Message => {
    const message = userMessage.toLowerCase();
    const userName = (user as User | null)?.name || (language === 'ru' ? 'друг' : 'friend');
    const isLoggedIn = !!user;

    // Ответы для незарегистрированных пользователей
    if (!isLoggedIn && (message.includes('артист') || message.includes('artist'))) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? '🎤 Отлично! Как артист, вы можете получить максимум от наших сервисов:\n\n✨ Что мы предлагаем:\n• Продвижение в Spotify плейлистах\n• Вирусные кампании в TikTok\n• Радио ротации по всему миру\n• YouTube продвижение\n• Пресс-релизы и PR\n\n🚀 Средний результат: +500K прослушиваний\n\nДавайте зарегистрируемся, чтобы я мог предложить персональную стратегию!'
          : '🎤 Great! As an artist, you can get the most from our services:\n\n✨ What we offer:\n• Spotify playlist promotion\n• Viral TikTok campaigns\n• Radio rotations worldwide\n• YouTube promotion\n• Press releases and PR\n\n🚀 Average result: +500K streams\n\nLet\'s register so I can offer a personalized strategy!',
        timestamp: new Date(),
        suggestions: language === 'ru'
          ? ['📝 Зарегистрироваться', '💰 Узнать цены', '📊 Посмотреть кейсы', '📞 Связаться с менеджером']
          : ['📝 Register', '💰 Get pricing', '📊 View cases', '📞 Contact manager']
      };
    }

    if (!isLoggedIn && (message.includes('лейбл') || message.includes('label'))) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? '🏢 Превосходно! Для лейблов у нас есть специальные корпоративные решения:\n\n🎯 Наши услуги для лейблов:\n• Массовое продвижение каталога\n• Аналитика по всем артистам\n• Персональный менеджер\n• Скидки на объемы\n• Приоритетная поддержка\n\n📈 Работаем с лейблами любого размера\n\nЗарегистрируйтесь для получения корпоративного предложения!'
          : '🏢 Excellent! We have special corporate solutions for labels:\n\n🎯 Our services for labels:\n• Mass catalog promotion\n• Analytics for all artists\n• Personal manager\n• Volume discounts\n• Priority support\n\n📈 We work with labels of any size\n\nRegister to get a corporate offer!',
        timestamp: new Date(),
        suggestions: language === 'ru'
          ? ['📝 Корпоративная регистрация', '💼 Узнать о скидках', '📊 Демо для лейблов', '📞 Связаться с менеджером']
          : ['📝 Corporate registration', '💼 Learn about discounts', '📊 Label demo', '📞 Contact manager']
      };
    }

    // Определяем тип запроса и генерируем ответ
    if (message.includes('трек') || message.includes('продвижение') || message.includes('track') || message.includes('promotion')) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: isLoggedIn
          ? (language === 'ru'
            ? `🎵 Отлично, ${userName}! Вот наши лучшие предложения для продвижения вашего трека:\n\n${(user as User | null)?.type === 'artist' ? '🎤 Как артист, вы получите персональную стратегию и скидку 25%!' : '🏢 Для лейблов у нас есть специальные корпоративные пакеты!'}\n\nВыберите подходящий пакет ниже:`
            : `🎵 Great, ${userName}! Here are our best offers for promoting your track:\n\n${(user as User | null)?.type === 'artist' ? '🎤 As an artist, you\'ll get a personalized strategy and 25% discount!' : '🏢 For labels, we have special corporate packages!'}\n\nChoose the right package below:`)
          : (language === 'ru'
            ? '🎵 Отлично! Вот наши лучшие предложения для продвижения трека:\n\n🎯 Выберите подходящий пакет и получите персональную консультацию!\n\n💫 Специальная скидка 25% для новых клиентов!'
            : '🎵 Great! Here are our best offers for track promotion:\n\n🎯 Choose the right package and get personal consultation!\n\n💫 Special 25% discount for new clients!'),
        timestamp: new Date(),
        offers: generateOffers('general'),
        suggestions: isLoggedIn
          ? (language === 'ru'
            ? ['📞 Персональная консультация', '📊 Мои результаты', '🎯 Другие услуги']
            : ['📞 Personal consultation', '📊 My results', '🎯 Other services'])
          : (language === 'ru'
            ? ['📝 Зарегистрироваться', '📞 Связаться с менеджером', '📊 Посмотреть кейсы']
            : ['📝 Register', '📞 Contact manager', '📊 View cases'])
      };
    }

    if (message.includes('цена') || message.includes('стоимость') || message.includes('тариф') || message.includes('price') || message.includes('cost')) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? `💰 ${isLoggedIn ? userName + ', в' : 'В'}от наши актуальные тарифы с персональными скидками:\n\n${isLoggedIn && (user as User | null)?.type === 'artist' ? '🎤 Скидка для артистов: -25%' : isLoggedIn && (user as User | null)?.type === 'label' ? '🏢 Корпоративная скидка: -30%' : '💫 Скидка для новых клиентов: -20%'}\n\n🎯 Выберите подходящий пакет:`
          : `💰 ${isLoggedIn ? userName + ', h' : 'H'}ere are our current rates with personal discounts:\n\n${isLoggedIn && (user as User | null)?.type === 'artist' ? '🎤 Artist discount: -25%' : isLoggedIn && (user as User | null)?.type === 'label' ? '🏢 Corporate discount: -30%' : '💫 New client discount: -20%'}\n\n🎯 Choose the right package:`,
        timestamp: new Date(),
        offers: generateOffers('general'),
        suggestions: language === 'ru'
          ? ['📞 Персональная консультация', '🎵 Spotify продвижение', '📱 TikTok кампания', '📊 Посмотреть кейсы']
          : ['📞 Personal consultation', '🎵 Spotify promotion', '📱 TikTok campaign', '📊 View cases']
      };
    }

    if (message.includes('spotify') || message.includes('спотифай')) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? `🎵 Spotify продвижение - наша специализация!\n\n${isLoggedIn ? `${userName}, для вас` : 'Для вас'} подготовили специальные пакеты Spotify продвижения:\n\n🎯 Работаем с плейлистами 2M+ подписчиков\n📈 Средний результат: +150K прослушиваний\n⚡ Старт кампании: 24 часа`
          : `🎵 Spotify promotion is our specialty!\n\n${isLoggedIn ? `${userName}, we` : 'We'} prepared special Spotify promotion packages for you:\n\n🎯 Working with 2M+ subscriber playlists\n📈 Average result: +150K streams\n⚡ Campaign start: 24 hours`,
        timestamp: new Date(),
        offers: generateOffers('spotify'),
        suggestions: language === 'ru'
          ? ['🎵 Выбрать Spotify пакет', '📊 Посмотреть плейлисты', '💡 Как это работает', '📞 Консультация']
          : ['🎵 Choose Spotify package', '📊 View playlists', '💡 How it works', '📞 Consultation']
      };
    }

    if (message.includes('как работает') || message.includes('процесс') || message.includes('how it works') || message.includes('process')) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? '⚡ Как мы работаем:\n\n1️⃣ Анализ трека\nИзучаем ваш трек, жанр, целевую аудиторию\n\n2️⃣ Стратегия\nСоздаем персональную стратегию продвижения\n\n3️⃣ Запуск\nЗапускаем кампании на всех платформах\n\n4️⃣ Мониторинг\nОтслеживаем результаты 24/7\n\n5️⃣ Оптимизация\nУлучшаем показатели в реальном времени\n\n6️⃣ Отчеты\nПредоставляем детальную аналитику\n\nВесь процесс занимает 2-4 недели 📈'
          : '⚡ How we work:\n\n1️⃣ Track Analysis\nWe study your track, genre, target audience\n\n2️⃣ Strategy\nCreate personalized promotion strategy\n\n3️⃣ Launch\nLaunch campaigns on all platforms\n\n4️⃣ Monitoring\nTrack results 24/7\n\n5️⃣ Optimization\nImprove metrics in real-time\n\n6️⃣ Reports\nProvide detailed analytics\n\nThe whole process takes 2-4 weeks 📈',
        timestamp: new Date(),
        suggestions: language === 'ru' 
          ? ['🚀 Начать сейчас', '📊 Примеры работ', '💬 Задать вопрос']
          : ['🚀 Start now', '📊 View examples', '💬 Ask question']
      };
    }

    if (message.includes('аналитика') || message.includes('статистика') || message.includes('analytics') || message.includes('stats')) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: isLoggedIn
          ? (language === 'ru'
            ? `📊 ${userName}, ваша аналитика включает:\n\n📈 Прослушивания по платформам\n🌍 География слушателей\n👥 Демография аудитории\n⏰ Активность по времени\n🎵 Популярные треки\n💰 Доходы и роялти\n🎯 Эффективность кампаний\n📱 Источники трафика\n\n✨ Все данные обновляются в реальном времени!\n\nВаш дашборд уже настроен и готов к использованию.`
            : `📊 ${userName}, your analytics includes:\n\n📈 Streams by platform\n🌍 Listener geography\n👥 Audience demographics\n⏰ Activity by time\n🎵 Popular tracks\n💰 Revenue and royalties\n🎯 Campaign effectiveness\n📱 Traffic sources\n\n✨ All data updates in real-time!\n\nYour dashboard is already set up and ready to use.`)
          : (language === 'ru'
            ? '📊 Наша аналитика включает:\n\n📈 Прослушивания по платформам\n🌍 География слушателей\n👥 Демография аудитории\n⏰ Активность по времени\n🎵 Популярные треки\n💰 Доходы и роялти\n🎯 Эффективность кампаний\n📱 Источники трафика\n\nВся статистика обновляется в реальном времени и доступна в личном кабинете 24/7\n\nЗарегистрируйтесь для доступа к полной аналитике!'
            : '📊 Our analytics includes:\n\n📈 Streams by platform\n🌍 Listener geography\n👥 Audience demographics\n⏰ Activity by time\n🎵 Popular tracks\n💰 Revenue and royalties\n🎯 Campaign effectiveness\n📱 Traffic sources\n\nAll stats update in real-time and available in dashboard 24/7\n\nRegister for full analytics access!'),
        timestamp: new Date(),
        suggestions: isLoggedIn
          ? (language === 'ru'
            ? ['📊 Открыть дашборд', '📈 Подключить платформы', '💡 Настроить уведомления', '🎯 Создать отчет']
            : ['📊 Open dashboard', '📈 Connect platforms', '💡 Setup notifications', '🎯 Create report'])
          : (language === 'ru'
            ? ['📝 Зарегистрироваться', '👀 Демо дашборд', '💡 Узнать больше']
            : ['📝 Register', '👀 Demo dashboard', '💡 Learn more'])
      };
    }

    // Специальные ответы для зарегистрированных пользователей
    if (isLoggedIn && (message.includes('дашборд') || message.includes('dashboard') || message.includes('кабинет'))) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? `📊 ${userName}, ваш дашборд содержит:\n\n✨ Текущие показатели:\n• 8.9M прослушиваний (+24%)\n• $47K доходов (+18%)\n• 12.5M подписчиков\n\n🎵 Последние треки:\n• PABLO - 2.1M прослушиваний\n• ARISTOCRAT - 1.8M прослушиваний\n\n🎯 Активные кампании: 3\n\nХотите перейти в дашборд или нужна помощь с настройкой?`
          : `📊 ${userName}, your dashboard contains:\n\n✨ Current metrics:\n• 8.9M streams (+24%)\n• $47K revenue (+18%)\n• 12.5M followers\n\n🎵 Recent tracks:\n• PABLO - 2.1M streams\n• ARISTOCRAT - 1.8M streams\n\n🎯 Active campaigns: 3\n\nWant to go to dashboard or need help with setup?`,
        timestamp: new Date(),
        suggestions: language === 'ru'
          ? ['📊 Открыть дашборд', '🎵 Загрузить трек', '🎯 Создать кампанию', '📈 Настроить аналитику']
          : ['📊 Open dashboard', '🎵 Upload track', '🎯 Create campaign', '📈 Setup analytics']
      };
    }

    if (isLoggedIn && (message.includes('кампания') || message.includes('campaign') || message.includes('создать'))) {
      return {
        id: Date.now().toString(),
        type: 'bot',
        content: language === 'ru'
          ? `🎯 ${userName}, давайте создадим кампанию!\n\n🚀 Доступные типы кампаний:\n• Spotify Playlist Push ($299-1299)\n• TikTok Viral Campaign ($199-899)\n• YouTube Promotion ($399-1599)\n• Radio Campaign ($599-2499)\n• Full Package ($999-4999)\n\n✨ Рекомендация для ${(user as User | null)?.type === 'artist' ? 'артиста' : 'лейбла'}:\nНачните с Spotify + TikTok пакета за $499\n\nГотовы создать кампанию?`
          : `🎯 ${userName}, let's create a campaign!\n\n🚀 Available campaign types:\n• Spotify Playlist Push ($299-1299)\n• TikTok Viral Campaign ($199-899)\n• YouTube Promotion ($399-1599)\n• Radio Campaign ($599-2499)\n• Full Package ($999-4999)\n\n✨ Recommendation for ${(user as User | null)?.type === 'artist' ? 'artist' : 'label'}:\nStart with Spotify + TikTok package for $499\n\nReady to create a campaign?`,
        timestamp: new Date(),
        suggestions: language === 'ru'
          ? ['🎯 Создать кампанию', '💰 Выбрать пакет', '📞 Консультация', '📊 Посмотреть результаты']
          : ['🎯 Create campaign', '💰 Choose package', '📞 Consultation', '📊 View results']
      };
    }

    // Общий ответ для неопознанных запросов
    return {
      id: Date.now().toString(),
      type: 'bot',
      content: language === 'ru'
        ? '🤔 Интересный вопрос! Я специализируюсь на музыкальном продвижении. Могу рассказать о:\n\n• Продвижении треков\n• Тарифах и услугах\n• Аналитике и статистике\n• Процессе работы\n• Результатах клиентов\n\nЧто именно вас интересует?'
        : '🤔 Interesting question! I specialize in music promotion. I can tell you about:\n\n• Track promotion\n• Pricing and services\n• Analytics and statistics\n• Work process\n• Client results\n\nWhat specifically interests you?',
      timestamp: new Date(),
      suggestions: language === 'ru' 
        ? ['🎵 Продвижение', '💰 Цены', '📊 Аналитика', '⚡ Как работает']
        : ['🎵 Promotion', '💰 Pricing', '📊 Analytics', '⚡ How it works']
    };
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Симуляция задержки ответа AI
    setTimeout(() => {
      const aiResponse = getAIResponse(inputValue);
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleSuggestionClick = (suggestion: string) => {
    const cleanSuggestion = suggestion.replace(/[🎵📊💰🎯❓👀📈💡🚀💬⚡🎤🏢📝📞💼]/g, '').trim();

    // Специальные действия для определенных кнопок
    if (suggestion.includes('Зарегистрироваться') || suggestion.includes('Register')) {
      window.location.href = '/register';
      return;
    }

    if (suggestion.includes('Открыть дашборд') || suggestion.includes('Open dashboard')) {
      window.location.href = '/dashboard';
      return;
    }

    if (suggestion.includes('Создать кампанию') || suggestion.includes('Create campaign')) {
      if (user) {
        window.location.href = '/dashboard?tab=campaigns';
      } else {
        window.location.href = '/register';
      }
      return;
    }

    if (suggestion.includes('Связаться с менеджером') || suggestion.includes('Contact manager')) {
      // Открываем модальное окно или переходим на страницу контактов
      const message = encodeURIComponent('Здравствуйте! Интересует продвижение музыки через HIVE Agency.');
      window.open(`https://wa.me/79991234567?text=${message}`, '_blank');
      return;
    }

    setInputValue(cleanSuggestion);
    setTimeout(() => handleSendMessage(), 100);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50 animate-bounce">
        <button
          onClick={() => setIsOpen(true)}
          className="group relative bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white p-4 rounded-full shadow-2xl shadow-red-500/25 hover:shadow-red-500/40 transition-all duration-300 hover:scale-110 animate-pulse"
        >
          <MessageCircle className="w-6 h-6" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"></div>

          {/* Floating notification */}
          <div className="absolute -top-12 -left-8 bg-white text-gray-900 px-3 py-2 rounded-lg shadow-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-red-500" />
              <span>{language === 'ru' ? 'AI-помощник готов помочь!' : 'AI Assistant ready to help!'}</span>
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
          </div>

          {/* Ripple effect */}
          <div className="absolute inset-0 rounded-full bg-red-400 opacity-20 animate-ping"></div>
        </button>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 transition-all duration-300 ${isMinimized ? 'w-80' : 'w-96'}`}>
      <div className={`bg-gray-900/95 backdrop-blur-xl border border-gray-800/50 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ${isMinimized ? 'h-16' : 'h-[600px]'}`}>
        {/* Header */}
        <div className="bg-gradient-to-r from-red-500 to-red-600 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <h3 className="text-white font-semibold text-sm">HIVE AI Assistant</h3>
              <p className="text-white/80 text-xs">
                {language === 'ru' ? 'Онлайн • Отвечаю мгновенно' : 'Online • Instant replies'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="text-white/80 hover:text-white p-1 rounded transition-colors"
            >
              {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white/80 hover:text-white p-1 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div className="h-[440px] overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div className={`flex items-start space-x-2 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${message.type === 'user' ? 'bg-red-500' : 'bg-gray-700'}`}>
                        {message.type === 'user' ? (
                          <UserIcon className="w-4 h-4 text-white" />
                        ) : (
                          <Bot className="w-4 h-4 text-white" />
                        )}
                      </div>
                      
                      <div className={`rounded-2xl p-3 ${message.type === 'user' ? 'bg-red-500 text-white' : 'bg-gray-800 text-gray-100'}`}>
                        <p className="text-sm whitespace-pre-line">{message.content}</p>
                        
                        {message.suggestions && (
                          <div className="mt-3 flex flex-wrap gap-2">
                            {message.suggestions.map((suggestion, index) => (
                              <button
                                key={index}
                                onClick={() => handleSuggestionClick(suggestion)}
                                className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-200 px-3 py-1.5 rounded-full transition-colors duration-200"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Offer Cards */}
                    {message.offers && message.offers.length > 0 && (
                      <div className="mt-8 space-y-8">
                        {message.offers.map((offer, index) => (
                          <OfferCard
                            key={index}
                            {...offer}
                            language={language}
                            onSelect={() => {
                              // Обработка выбора пакета
                              if (user) {
                                window.location.href = `/dashboard?package=${offer.type}`;
                              } else {
                                window.location.href = `/register?package=${offer.type}`;
                              }
                            }}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-2">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="bg-gray-800 rounded-2xl p-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t border-gray-800">
              <div className="flex items-center space-x-2">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={language === 'ru' ? 'Напишите ваш вопрос...' : 'Type your question...'}
                    className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-500 transition-colors"
                  />
                </div>
                <button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  className="bg-red-500 hover:bg-red-600 disabled:bg-gray-700 disabled:cursor-not-allowed text-white p-3 rounded-xl transition-colors duration-200"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AIAssistant;
