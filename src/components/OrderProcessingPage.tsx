'use client';

import { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  User, 
  Music, 
  Calendar, 
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  Bot,
  Zap,
  Users,
  Mail,
  MessageSquare,
  ExternalLink,
  Copy,
  Play,
  Pause,
  Settings,
  BarChart3,
  Target,
  Globe,
  Instagram,
  Youtube,
  Twitter,
  Send,
  Download,
  Eye,
  Heart
} from 'lucide-react';
import { OrderWithPayment, SocialAccount, PromotionBot, EmailCampaign, TelegramGroup } from '@/types/pricing';
import { BOT_TEMPLATES } from '@/config/pricing';
import AIBots from './AIBots';
import SocialMediaCards from './SocialMediaCards';
import { BotCard, EmailCampaignCard, TelegramGroupCard } from './OrderProcessingCards';
import RealTimeAnalytics from './RealTimeAnalytics';

interface OrderProcessingPageProps {
  orderId: string;
  onBack: () => void;
}

const OrderProcessingPage: React.FC<OrderProcessingPageProps> = ({ orderId, onBack }) => {
  const [order, setOrder] = useState<OrderWithPayment | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);

  // Состояния для управления ресурсами
  const [socialAccounts, setSocialAccounts] = useState<SocialAccount[]>([]);
  const [activeBots, setActiveBots] = useState<PromotionBot[]>([]);
  const [emailCampaigns, setEmailCampaigns] = useState<EmailCampaign[]>([]);
  const [telegramGroups, setTelegramGroups] = useState<TelegramGroup[]>([]);

  // Модальные окна
  const [showAddSocial, setShowAddSocial] = useState(false);
  const [showAddBot, setShowAddBot] = useState(false);
  const [showAddEmail, setShowAddEmail] = useState(false);
  const [showAddTelegram, setShowAddTelegram] = useState(false);

  useEffect(() => {
    loadOrderData();
  }, [orderId]);

  const loadOrderData = async () => {
    try {
      setLoading(true);
      // Загружаем данные заказа
      const response = await fetch(`/api/admin/orders/${orderId}`);
      const orderData = await response.json();
      setOrder(orderData);

      // Загружаем назначенные ресурсы
      if (orderData.assignedResources) {
        setSocialAccounts(orderData.assignedResources.socialAccounts || []);
        setActiveBots(orderData.assignedResources.activeBots || []);
        setEmailCampaigns(orderData.assignedResources.emailCampaigns || []);
        setTelegramGroups(orderData.assignedResources.telegramGroups || []);
      }
    } catch (error) {
      console.error('Error loading order:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (newStatus: string) => {
    try {
      await fetch(`/api/admin/orders/${orderId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });
      
      if (order) {
        setOrder({ ...order, status: newStatus as any });
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const addSocialAccount = async (accountData: Partial<SocialAccount>) => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/social-accounts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(accountData)
      });

      const result = await response.json();
      if (result.success) {
        setSocialAccounts([...socialAccounts, result.account]);
      }
    } catch (error) {
      console.error('Error adding social account:', error);
    }
  };

  const addEmailCampaign = async (campaignData: any) => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/email-campaigns`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(campaignData)
      });

      const result = await response.json();
      if (result.success) {
        setEmailCampaigns([...emailCampaigns, result.campaign]);
      }
    } catch (error) {
      console.error('Error adding email campaign:', error);
    }
  };

  const addTelegramGroup = async (groupData: any) => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/telegram-groups`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData)
      });

      const result = await response.json();
      if (result.success) {
        setTelegramGroups([...telegramGroups, result.group]);
      }
    } catch (error) {
      console.error('Error adding telegram group:', error);
    }
  };

  const activateBot = async (botConfig: Partial<PromotionBot>) => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/bots`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(botConfig)
      });
      
      const newBot = await response.json();
      setActiveBots([...activeBots, newBot]);
      setShowAddBot(false);
    } catch (error) {
      console.error('Error activating bot:', error);
    }
  };



  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'gray',
      'pending_payment': 'yellow',
      'paid': 'blue',
      'in_progress': 'purple',
      'completed': 'green',
      'cancelled': 'red'
    };
    return colors[status as keyof typeof colors] || 'gray';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      'draft': <Edit className="w-4 h-4" />,
      'pending_payment': <Clock className="w-4 h-4" />,
      'paid': <CheckCircle className="w-4 h-4" />,
      'in_progress': <Zap className="w-4 h-4" />,
      'completed': <CheckCircle className="w-4 h-4" />,
      'cancelled': <AlertCircle className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || <Clock className="w-4 h-4" />;
  };

  const formatCurrency = (amount: number, currency?: string) => {
    const safeCurrency = currency || 'RUB';
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: safeCurrency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Заказ не найден</h3>
        <p className="text-gray-400">Заказ с ID {orderId} не существует</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="bg-gray-900/80 backdrop-blur-xl border-b border-gray-800/50" style={{ padding: '20px 30px' }}>
        <div className="flex items-center justify-between">
          <div className="flex items-center" style={{ gap: '20px' }}>
            <button
              onClick={onBack}
              className="w-10 h-10 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.4' }}>
                Обработка заказа #{order.id}
              </h1>
              <div className="flex items-center" style={{ gap: '15px' }}>
                <div className={`flex items-center text-${getStatusColor(order.status || 'draft')}-400`} style={{ gap: '6px' }}>
                  {getStatusIcon(order.status || 'draft')}
                  <span className="text-sm capitalize">{order.status && typeof order.status === 'string' ? order.status.replace('_', ' ') : 'Неизвестно'}</span>
                </div>
                <span className="text-gray-400 text-sm">
                  {order.clientInfo?.name || 'Неизвестный клиент'} • {formatCurrency(order.amount, order.currency)}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center" style={{ gap: '15px' }}>
            <select
              value={order.status || 'draft'}
              onChange={(e) => updateOrderStatus(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
              style={{ padding: '8px 12px', lineHeight: '1.4' }}
            >
              <option value="paid">Оплачен</option>
              <option value="in_progress">В работе</option>
              <option value="completed">Завершен</option>
              <option value="cancelled">Отменен</option>
            </select>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-gray-900/50 border-r border-gray-800/50 min-h-screen" style={{ padding: '30px 20px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {[
              { id: 'overview', label: 'Обзор', icon: <BarChart3 className="w-5 h-5" /> },
              { id: 'social', label: 'Соц. сети', icon: <Users className="w-5 h-5" /> },
              { id: 'bots', label: 'Боты', icon: <Bot className="w-5 h-5" /> },
              { id: 'ai-bots', label: 'AI Боты', icon: <Zap className="w-5 h-5" /> },
              { id: 'email', label: 'Email', icon: <Mail className="w-5 h-5" /> },
              { id: 'telegram', label: 'Telegram', icon: <MessageSquare className="w-5 h-5" /> },
              { id: 'analytics', label: 'Аналитика', icon: <BarChart3 className="w-5 h-5" /> },
              { id: 'realtime', label: 'Реал-тайм', icon: <Zap className="w-5 h-5" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center rounded-lg transition-colors text-left ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/30'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
                style={{ gap: '12px', padding: '12px 16px', lineHeight: '1.4' }}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1" style={{ padding: '30px' }}>
          {activeTab === 'overview' && (
            <div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Информация о заказе
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2" style={{ gap: '30px' }}>
                {/* Client Info */}
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                    Информация о клиенте
                  </h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <User className="w-5 h-5 text-gray-400" />
                      <span className="text-white">{order.clientInfo?.name || 'Неизвестный клиент'}</span>
                    </div>
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <Mail className="w-5 h-5 text-gray-400" />
                      <span className="text-white">{order.clientInfo?.email || 'Неизвестный email'}</span>
                    </div>
                    {order.clientInfo?.phone && (
                      <div className="flex items-center" style={{ gap: '12px' }}>
                        <span className="text-gray-400">📱</span>
                        <span className="text-white">{order.clientInfo?.phone}</span>
                      </div>
                    )}
                    {order.clientInfo?.telegramUsername && (
                      <div className="flex items-center" style={{ gap: '12px' }}>
                        <MessageSquare className="w-5 h-5 text-gray-400" />
                        <span className="text-white">@{order.clientInfo?.telegramUsername}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Promotion Data */}
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                  <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                    Данные для продвижения
                  </h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {order.promotionData?.artistName && (
                      <div className="flex items-center" style={{ gap: '12px' }}>
                        <User className="w-5 h-5 text-gray-400" />
                        <span className="text-white">{order.promotionData?.artistName}</span>
                      </div>
                    )}
                    {order.promotionData?.trackTitle && (
                      <div className="flex items-center" style={{ gap: '12px' }}>
                        <Music className="w-5 h-5 text-gray-400" />
                        <span className="text-white">{order.promotionData?.trackTitle}</span>
                      </div>
                    )}
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <Target className="w-5 h-5 text-gray-400" />
                      <span className="text-white">{order.promotionData?.genre}</span>
                    </div>
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <Users className="w-5 h-5 text-gray-400" />
                      <span className="text-white">{order.promotionData?.targetAudience?.join(', ')}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Links */}
              {order.promotionData?.socialLinks && Object.keys(order.promotionData.socialLinks).length > 0 && (
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px', marginTop: '30px' }}>
                  <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                    Социальные сети
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3" style={{ gap: '15px' }}>
                    {Object.entries(order.promotionData?.socialLinks || {}).map(([platform, url]) => (
                      url && (
                        <a
                          key={platform}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors"
                          style={{ padding: '12px', gap: '8px' }}
                        >
                          {platform === 'instagram' && <Instagram className="w-5 h-5 text-pink-400" />}
                          {platform === 'youtube' && <Youtube className="w-5 h-5 text-red-400" />}
                          {platform === 'twitter' && <Twitter className="w-5 h-5 text-blue-400" />}
                          <span className="text-white capitalize">{platform}</span>
                          <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
                        </a>
                      )
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'social' && (
            <SocialMediaCards
              accounts={socialAccounts}
              onAddAccount={addSocialAccount}
              onUpdateAccount={(accountId, data) => {
                setSocialAccounts(accounts =>
                  accounts.map(acc => acc.id === accountId ? { ...acc, ...data } : acc)
                );
              }}
              onDeleteAccount={(accountId) => {
                setSocialAccounts(accounts => accounts.filter(acc => acc.id !== accountId));
              }}
              orderId={orderId}
            />
          )}

          {activeTab === 'bots' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">Боты продвижения</h2>
                  <p className="text-gray-400">Автоматизированные инструменты для продвижения ({activeBots.length})</p>
                </div>

                <button
                  onClick={() => setShowAddBot(true)}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-xl font-medium transition-all hover:scale-105 shadow-lg shadow-blue-500/25"
                >
                  <Plus className="w-5 h-5" />
                  Добавить бота
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {activeBots.map((bot) => (
                  <BotCard
                    key={bot.id}
                    bot={bot}
                    onUpdate={(botId, data) => {
                      setActiveBots(bots =>
                        bots.map(b => b.id === botId ? { ...b, ...data } : b)
                      );
                    }}
                    onDelete={(botId) => {
                      setActiveBots(bots => bots.filter(b => b.id !== botId));
                    }}
                  />
                ))}

                {activeBots.length === 0 && (
                  <div className="col-span-full text-center py-12">
                    <Bot className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">Нет активных ботов</h3>
                    <p className="text-gray-500 mb-6">Добавьте ботов для автоматизации продвижения</p>
                    <button
                      onClick={() => setShowAddBot(true)}
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl font-medium hover:scale-105 transition-transform"
                    >
                      Добавить первого бота
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'email' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">Email кампании</h2>
                  <p className="text-gray-400">Управление email рассылками ({emailCampaigns.length})</p>
                </div>

                <button
                  onClick={() => setShowAddEmail(true)}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-xl font-medium transition-all hover:scale-105 shadow-lg shadow-green-500/25"
                >
                  <Plus className="w-5 h-5" />
                  Создать кампанию
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {emailCampaigns.map((campaign) => (
                  <EmailCampaignCard
                    key={campaign.id}
                    campaign={campaign}
                    onUpdate={(campaignId, data) => {
                      setEmailCampaigns(campaigns =>
                        campaigns.map(c => c.id === campaignId ? { ...c, ...data } : c)
                      );
                    }}
                    onDelete={(campaignId) => {
                      setEmailCampaigns(campaigns => campaigns.filter(c => c.id !== campaignId));
                    }}
                  />
                ))}

                {emailCampaigns.length === 0 && (
                  <div className="col-span-full text-center py-12">
                    <Mail className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">Нет активных кампаний</h3>
                    <p className="text-gray-500 mb-6">Создайте email кампанию для продвижения</p>
                    <button
                      onClick={() => setShowAddEmail(true)}
                      className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl font-medium hover:scale-105 transition-transform"
                    >
                      Создать первую кампанию
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'telegram' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">Telegram группы</h2>
                  <p className="text-gray-400">Управление Telegram каналами и группами ({telegramGroups.length})</p>
                </div>

                <button
                  onClick={() => setShowAddTelegram(true)}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-400 to-cyan-500 hover:from-blue-500 hover:to-cyan-600 text-white rounded-xl font-medium transition-all hover:scale-105 shadow-lg shadow-blue-400/25"
                >
                  <Plus className="w-5 h-5" />
                  Добавить группу
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {telegramGroups.map((group) => (
                  <TelegramGroupCard
                    key={group.id}
                    group={group}
                    onUpdate={(groupId, data) => {
                      setTelegramGroups(groups =>
                        groups.map(g => g.id === groupId ? { ...g, ...data } : g)
                      );
                    }}
                    onDelete={(groupId) => {
                      setTelegramGroups(groups => groups.filter(g => g.id !== groupId));
                    }}
                  />
                ))}

                {telegramGroups.length === 0 && (
                  <div className="col-span-full text-center py-12">
                    <MessageSquare className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">Нет подключенных групп</h3>
                    <p className="text-gray-500 mb-6">Добавьте Telegram группы для продвижения</p>
                    <button
                      onClick={() => setShowAddTelegram(true)}
                      className="px-6 py-3 bg-gradient-to-r from-blue-400 to-cyan-500 text-white rounded-xl font-medium hover:scale-105 transition-transform"
                    >
                      Добавить первую группу
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'bots' && (
            <div>
              <div className="flex items-center justify-between" style={{ marginBottom: '25px' }}>
                <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
                  Активные боты ({activeBots.length})
                </h2>
                <button
                  onClick={() => setShowAddBot(true)}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
                  style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                >
                  <Bot className="w-4 h-4" />
                  Активировать бота
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                {activeBots.map((bot) => (
                  <div key={bot.id} className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                    <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <Bot className="w-5 h-5 text-blue-400" />
                        <span className="text-white font-medium">{bot?.name || 'Неизвестный бот'}</span>
                      </div>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <div className={`w-2 h-2 rounded-full ${bot.isActive ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          {bot.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div style={{ marginBottom: '15px' }}>
                      <div className="text-sm text-gray-400" style={{ marginBottom: '5px' }}>
                        Платформа: <span className="text-white capitalize">{bot.platform}</span>
                      </div>
                      <div className="text-sm text-gray-400" style={{ marginBottom: '5px' }}>
                        Тип: <span className="text-white capitalize">{bot.type}</span>
                      </div>
                      <div className="text-sm text-gray-400">
                        Активность: <span className="text-white capitalize">{bot.config.activityLevel}</span>
                      </div>
                    </div>

                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Действий:</span>
                        <span className="text-white">{bot.stats.totalActions.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Успешность:</span>
                        <span className="text-white">{bot.stats.successRate}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Лиды:</span>
                        <span className="text-white">{bot.stats.generatedLeads}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'email' && (
            <div>
              <div className="flex items-center justify-between" style={{ marginBottom: '25px' }}>
                <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
                  Email кампании ({emailCampaigns.length})
                </h2>
                <button
                  onClick={() => setShowAddEmail(true)}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
                  style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                >
                  <Mail className="w-4 h-4" />
                  Создать кампанию
                </button>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                {emailCampaigns.map((campaign) => (
                  <div key={campaign.id} className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                    <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                      <div>
                        <h3 className="text-white font-medium" style={{ marginBottom: '5px', lineHeight: '1.4' }}>
                          {campaign?.name || 'Неизвестная кампания'}
                        </h3>
                        <div className="flex items-center" style={{ gap: '15px' }}>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            campaign.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                            campaign.status === 'sending' ? 'bg-blue-500/20 text-blue-400' :
                            campaign.status === 'scheduled' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {campaign.status}
                          </span>
                          <span className="text-gray-400 text-sm capitalize">{campaign.type}</span>
                        </div>
                      </div>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Edit className="w-4 h-4" />
                        </button>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-green-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Send className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-4" style={{ gap: '20px' }}>
                      <div className="text-center">
                        <div className="text-lg font-bold text-white">{campaign.stats.sent.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Отправлено</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-400">{campaign.stats.opened.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Открыто</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-400">{campaign.stats.clicked.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Кликов</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-400">{campaign.stats.unsubscribed.toLocaleString()}</div>
                        <div className="text-sm text-gray-400">Отписок</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'telegram' && (
            <div>
              <div className="flex items-center justify-between" style={{ marginBottom: '25px' }}>
                <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
                  Telegram группы ({telegramGroups.length})
                </h2>
                <button
                  onClick={() => setShowAddTelegram(true)}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
                  style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                >
                  <MessageSquare className="w-4 h-4" />
                  Добавить группу
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                {telegramGroups.map((group) => (
                  <div key={group.id} className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                    <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <MessageSquare className="w-5 h-5 text-blue-400" />
                        <span className="text-white font-medium">{group?.name || 'Неизвестная группа'}</span>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${group.isActive ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                    </div>

                    <div style={{ marginBottom: '15px' }}>
                      <div className="text-sm text-gray-400" style={{ marginBottom: '5px' }}>
                        @{group.username}
                      </div>
                      <div className="text-sm text-gray-400" style={{ marginBottom: '5px' }}>
                        Тип: <span className="text-white capitalize">{group.type}</span>
                      </div>
                      <div className="text-sm text-gray-400">
                        Участников: <span className="text-white">{group.membersCount.toLocaleString()}</span>
                      </div>
                    </div>

                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Сообщений/день:</span>
                        <span className="text-white">{group.stats.dailyMessages}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Активных:</span>
                        <span className="text-white">{group.stats.activeMembers.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Вовлеченность:</span>
                        <span className="text-white">{group.stats.engagement}%</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between" style={{ marginTop: '15px' }}>
                      <div className="flex items-center" style={{ gap: '5px' }}>
                        {group.settings.autoPost && <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Auto Post</span>}
                        {group.settings.moderationLevel === 'high' && <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded">High Mod</span>}
                      </div>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Settings className="w-4 h-4" />
                        </button>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-red-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div>
              <h2 className="text-xl font-bold text-white" style={{ marginBottom: '25px', lineHeight: '1.4' }}>
                Аналитика продвижения
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4" style={{ gap: '20px', marginBottom: '30px' }}>
                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                  <div className="flex items-center" style={{ gap: '8px', marginBottom: '10px' }}>
                    <Users className="w-5 h-5 text-blue-400" />
                    <span className="text-gray-400 text-sm">Общий охват</span>
                  </div>
                  <div className="text-2xl font-bold text-white">1.2M</div>
                  <div className="text-sm text-green-400">+15% за неделю</div>
                </div>

                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                  <div className="flex items-center" style={{ gap: '8px', marginBottom: '10px' }}>
                    <Zap className="w-5 h-5 text-yellow-400" />
                    <span className="text-gray-400 text-sm">Вовлеченность</span>
                  </div>
                  <div className="text-2xl font-bold text-white">8.5%</div>
                  <div className="text-sm text-green-400">+2.1% за неделю</div>
                </div>

                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                  <div className="flex items-center" style={{ gap: '8px', marginBottom: '10px' }}>
                    <Target className="w-5 h-5 text-green-400" />
                    <span className="text-gray-400 text-sm">Конверсии</span>
                  </div>
                  <div className="text-2xl font-bold text-white">3.2%</div>
                  <div className="text-sm text-green-400">+0.8% за неделю</div>
                </div>

                <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
                  <div className="flex items-center" style={{ gap: '8px', marginBottom: '10px' }}>
                    <Globe className="w-5 h-5 text-purple-400" />
                    <span className="text-gray-400 text-sm">Новые фаны</span>
                  </div>
                  <div className="text-2xl font-bold text-white">15.7K</div>
                  <div className="text-sm text-green-400">+25% за неделю</div>
                </div>
              </div>

              <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                  Прогресс по платформам
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                  {[
                    { platform: 'Instagram', progress: 85, color: 'bg-pink-500' },
                    { platform: 'YouTube', progress: 72, color: 'bg-red-500' },
                    { platform: 'TikTok', progress: 91, color: 'bg-purple-500' },
                    { platform: 'Telegram', progress: 68, color: 'bg-blue-500' },
                    { platform: 'VK', progress: 79, color: 'bg-indigo-500' }
                  ].map((item) => (
                    <div key={item.platform}>
                      <div className="flex justify-between" style={{ marginBottom: '5px' }}>
                        <span className="text-white">{item.platform}</span>
                        <span className="text-gray-400">{item.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${item.color}`}
                          style={{ width: `${item.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'ai-bots' && (
            <div>
              <h2 className="text-xl font-bold text-white mb-6">
                AI Боты для автоматизации
              </h2>
              <AIBots onBotToggle={(botId, isActive) => {
                console.log(`Bot ${botId} is now ${isActive ? 'active' : 'inactive'}`);
              }} />
            </div>
          )}

          {activeTab === 'realtime' && (
            <RealTimeAnalytics orderId={orderId} />
          )}

          {activeTab === 'analytics' && (
            <div className="space-y-8">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">Аналитика и отчеты</h2>
                  <p className="text-gray-400">Детальная аналитика по всем кампаниям и ресурсам</p>
                </div>

                <div className="flex items-center gap-4">
                  <select className="bg-gray-800 border border-gray-700 rounded-xl px-4 py-2 text-white focus:outline-none focus:border-purple-500">
                    <option value="7d">Последние 7 дней</option>
                    <option value="30d">Последние 30 дней</option>
                    <option value="90d">Последние 90 дней</option>
                  </select>

                  <button className="flex items-center gap-2 px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium transition-all">
                    <Download className="w-4 h-4" />
                    Экспорт
                  </button>
                </div>
              </div>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-xl p-6 border border-blue-500/30">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                      <Eye className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-lg font-medium">+15.3%</span>
                  </div>
                  <p className="text-2xl font-bold text-white mb-1">2.4M</p>
                  <p className="text-sm text-gray-400">Общий охват</p>
                </div>

                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl p-6 border border-green-500/30">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-lg font-medium">+8.7%</span>
                  </div>
                  <p className="text-2xl font-bold text-white mb-1">156K</p>
                  <p className="text-sm text-gray-400">Вовлеченность</p>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl p-6 border border-purple-500/30">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                      <Users className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-lg font-medium">+22.1%</span>
                  </div>
                  <p className="text-2xl font-bold text-white mb-1">12.8K</p>
                  <p className="text-sm text-gray-400">Новые подписчики</p>
                </div>

                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-xl p-6 border border-orange-500/30">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                      <Target className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-lg font-medium">+5.2%</span>
                  </div>
                  <p className="text-2xl font-bold text-white mb-1">4.8%</p>
                  <p className="text-sm text-gray-400">Конверсия</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Social Account Modal */}
      {showAddSocial && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
          <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-md w-full" style={{ padding: '30px' }}>
            <h3 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
              Добавить социальный аккаунт
            </h3>

            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              addSocialAccount({
                platform: formData.get('platform') as string,
                username: formData.get('username') as string,
                followers: Number(formData.get('followers')) || 0,
                engagement: Number(formData.get('engagement')) || 0,
                niche: (formData.get('niche') as string).split(',').map(s => s.trim()),
                accessToken: formData.get('accessToken') as string
              });
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', marginBottom: '25px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Платформа</label>
                  <select
                    name="platform"
                    required
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  >
                    <option value="instagram">Instagram</option>
                    <option value="youtube">YouTube</option>
                    <option value="tiktok">TikTok</option>
                    <option value="vk">VKontakte</option>
                    <option value="twitter">Twitter</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Username</label>
                  <input
                    type="text"
                    name="username"
                    required
                    placeholder="username"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  />
                </div>

                <div className="grid grid-cols-2" style={{ gap: '15px' }}>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Подписчики</label>
                    <input
                      type="number"
                      name="followers"
                      placeholder="10000"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px' }}
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Вовлеченность %</label>
                    <input
                      type="number"
                      name="engagement"
                      placeholder="5.5"
                      step="0.1"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px' }}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Ниши (через запятую)</label>
                  <input
                    type="text"
                    name="niche"
                    placeholder="music, entertainment, lifestyle"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Access Token</label>
                  <input
                    type="password"
                    name="accessToken"
                    placeholder="Токен доступа к API"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  />
                </div>
              </div>

              <div className="flex" style={{ gap: '12px' }}>
                <button
                  type="button"
                  onClick={() => setShowAddSocial(false)}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px' }}
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px' }}
                >
                  Добавить
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Bot Modal */}
      {showAddBot && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
          <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-md w-full" style={{ padding: '30px' }}>
            <h3 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
              Активировать бота
            </h3>

            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              activateBot({
                platform: formData.get('platform') as string,
                type: formData.get('type') as string,
                activityLevel: formData.get('activityLevel') as string,
                targetKeywords: (formData.get('keywords') as string).split(',').map(s => s.trim()),
                dailyActions: Number(formData.get('dailyActions')) || 100
              });
            }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', marginBottom: '25px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Платформа</label>
                  <select
                    name="platform"
                    required
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  >
                    <option value="instagram">Instagram</option>
                    <option value="youtube">YouTube</option>
                    <option value="tiktok">TikTok</option>
                    <option value="telegram">Telegram</option>
                    <option value="email">Email</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Тип бота</label>
                  <select
                    name="type"
                    required
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  >
                    <option value="engagement">Вовлечение</option>
                    <option value="content">Контент</option>
                    <option value="outreach">Охват</option>
                    <option value="scraper">Скрапер</option>
                    <option value="analytics">Аналитика</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Уровень активности</label>
                  <select
                    name="activityLevel"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  >
                    <option value="low">Низкий</option>
                    <option value="medium">Средний</option>
                    <option value="high">Высокий</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Ключевые слова</label>
                  <input
                    type="text"
                    name="keywords"
                    placeholder="музыка, хип-хоп, рэп"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px' }}>Действий в день</label>
                  <input
                    type="number"
                    name="dailyActions"
                    placeholder="100"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px' }}
                  />
                </div>
              </div>

              <div className="flex" style={{ gap: '12px' }}>
                <button
                  type="button"
                  onClick={() => setShowAddBot(false)}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px' }}
                >
                  Отмена
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px' }}
                >
                  Активировать
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderProcessingPage;
