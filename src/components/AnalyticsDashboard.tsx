'use client';

import { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Music,
  Radio,
  Instagram,
  Youtube,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface AnalyticsData {
  period: string;
  revenue: number;
  orders: number;
  clients: number;
  satisfaction: number;
  platforms: {
    spotify: number;
    youtube: number;
    instagram: number;
    tiktok: number;
  };
  topServices: Array<{
    name: string;
    orders: number;
    revenue: number;
    growth: number;
  }>;
}

const AnalyticsDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  // Mock данные аналитики
  const analyticsData: AnalyticsData = {
    period: 'Январь 2024',
    revenue: 2400000,
    orders: 156,
    clients: 89,
    satisfaction: 98,
    platforms: {
      spotify: 45,
      youtube: 32,
      instagram: 28,
      tiktok: 15
    },
    topServices: [
      { name: 'Продвижение в Instagram', orders: 45, revenue: 675000, growth: 12 },
      { name: 'Размещение на радио', orders: 32, revenue: 800000, growth: 8 },
      { name: 'YouTube продвижение', orders: 28, revenue: 420000, growth: -3 },
      { name: 'TikTok кампании', orders: 25, revenue: 375000, growth: 25 },
      { name: 'Spotify плейлисты', orders: 26, revenue: 130000, growth: 15 }
    ]
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ru-RU').format(num);
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />;
  };

  return (
    <div>
      {/* Header */}
      <div className="flex items-center justify-between" style={{ marginBottom: '30px' }}>
        <div>
          <h1 className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.4' }}>
            Аналитика
          </h1>
          <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
            Подробная статистика и отчеты по вашим кампаниям
          </p>
        </div>
        
        <div className="flex items-center" style={{ gap: '15px' }}>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
            style={{ padding: '10px 15px', lineHeight: '1.4' }}
          >
            <option value="week">Неделя</option>
            <option value="month">Месяц</option>
            <option value="quarter">Квартал</option>
            <option value="year">Год</option>
          </select>
          
          <button className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center" style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}>
            <RefreshCw className="w-4 h-4" />
            Обновить
          </button>
          
          <button className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center" style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}>
            <Download className="w-4 h-4" />
            Экспорт
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4" style={{ gap: '20px', marginBottom: '30px' }}>
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
          <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
            <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-green-400" />
            </div>
            <div className="flex items-center text-green-400" style={{ gap: '4px' }}>
              <ArrowUp className="w-4 h-4" />
              <span className="text-sm">+12%</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>
            {formatCurrency(analyticsData.revenue)}
          </div>
          <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
            Доход за {analyticsData.period.toLowerCase()}
          </div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
          <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
            <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-400" />
            </div>
            <div className="flex items-center text-green-400" style={{ gap: '4px' }}>
              <ArrowUp className="w-4 h-4" />
              <span className="text-sm">+8%</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>
            {formatNumber(analyticsData.orders)}
          </div>
          <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
            Заказов выполнено
          </div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
          <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
            <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-purple-400" />
            </div>
            <div className="flex items-center text-green-400" style={{ gap: '4px' }}>
              <ArrowUp className="w-4 h-4" />
              <span className="text-sm">+15%</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>
            {formatNumber(analyticsData.clients)}
          </div>
          <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
            Новых клиентов
          </div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
          <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
            <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-orange-400" />
            </div>
            <div className="flex items-center text-green-400" style={{ gap: '4px' }}>
              <ArrowUp className="w-4 h-4" />
              <span className="text-sm">+2%</span>
            </div>
          </div>
          <div className="text-2xl font-bold text-white" style={{ marginBottom: '5px', lineHeight: '1.2' }}>
            {analyticsData.satisfaction}%
          </div>
          <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
            Удовлетворенность
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2" style={{ gap: '30px' }}>
        {/* Platform Distribution */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
          <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
            Распределение по платформам
          </h3>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            {[
              { name: 'Spotify', value: analyticsData.platforms.spotify, color: 'bg-green-500', icon: <Music className="w-4 h-4" /> },
              { name: 'YouTube', value: analyticsData.platforms.youtube, color: 'bg-red-500', icon: <Youtube className="w-4 h-4" /> },
              { name: 'Instagram', value: analyticsData.platforms.instagram, color: 'bg-pink-500', icon: <Instagram className="w-4 h-4" /> },
              { name: 'TikTok', value: analyticsData.platforms.tiktok, color: 'bg-purple-500', icon: <Radio className="w-4 h-4" /> }
            ].map((platform) => (
              <div key={platform.name} className="flex items-center" style={{ gap: '15px' }}>
                <div className={`w-8 h-8 ${platform.color} rounded-lg flex items-center justify-center text-white`}>
                  {platform.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between" style={{ marginBottom: '5px' }}>
                    <span className="text-white font-medium" style={{ lineHeight: '1.4' }}>{platform.name}</span>
                    <span className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>{platform.value}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`${platform.color} h-2 rounded-full transition-all duration-500`}
                      style={{ width: `${platform.value}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Services */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
          <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
            Топ услуги
          </h3>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
            {analyticsData.topServices.map((service, index) => (
              <div key={service.name} className="flex items-center justify-between bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
                <div className="flex items-center" style={{ gap: '12px' }}>
                  <div className="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-white font-medium" style={{ lineHeight: '1.4' }}>
                      {service.name}
                    </div>
                    <div className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                      {service.orders} заказов • {formatCurrency(service.revenue)}
                    </div>
                  </div>
                </div>
                <div className={`flex items-center ${getGrowthColor(service.growth)}`} style={{ gap: '4px' }}>
                  {getGrowthIcon(service.growth)}
                  <span className="text-sm font-medium">{Math.abs(service.growth)}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Chart Placeholder */}
      <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px', marginTop: '30px' }}>
        <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
          <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
            Динамика роста
          </h3>
          <div className="flex items-center" style={{ gap: '10px' }}>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-red-500"
              style={{ padding: '8px 12px', lineHeight: '1.4' }}
            >
              <option value="revenue">Доход</option>
              <option value="orders">Заказы</option>
              <option value="clients">Клиенты</option>
            </select>
          </div>
        </div>
        
        {/* Chart Placeholder */}
        <div className="h-64 bg-gray-700/30 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-500 mx-auto" style={{ marginBottom: '15px' }} />
            <h4 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
              График будет здесь
            </h4>
            <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
              Интерактивный график с данными по выбранной метрике
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
