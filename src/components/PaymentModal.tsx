'use client';

import { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  Bitcoin, 
  X, 
  Check, 
  Loader2,
  Shield,
  Lock,
  AlertCircle,
  Copy,
  QrCode,
  ExternalLink
} from 'lucide-react';
import { stripeService } from '@/services/stripeService';
import { cryptoPaymentService } from '@/services/cryptoPaymentService';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  currency: string;
  orderId: string;
  orderTitle: string;
  onPaymentSuccess: (paymentId: string) => void;
  onPaymentError: (error: string) => void;
}

type PaymentMethod = 'card' | 'crypto' | 'sbp';

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  amount,
  currency,
  orderId,
  orderTitle,
  onPaymentSuccess,
  onPaymentError
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [step, setStep] = useState<'method' | 'details' | 'processing' | 'success'>('method');
  
  // Card payment state
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: ''
  });

  // Crypto payment state
  const [selectedCrypto, setSelectedCrypto] = useState('BTC');
  const [cryptoPayment, setCryptoPayment] = useState<any>(null);
  const [cryptoRates, setCryptoRates] = useState<any[]>([]);

  const paymentMethods = [
    {
      id: 'card' as PaymentMethod,
      name: 'Банковская карта',
      description: 'Visa, MasterCard, МИР',
      icon: <CreditCard className="w-6 h-6" />,
      fee: '2.9%',
      popular: true
    },
    {
      id: 'sbp' as PaymentMethod,
      name: 'СБП',
      description: 'Система быстрых платежей',
      icon: <Smartphone className="w-6 h-6" />,
      fee: '0%',
      popular: false
    },
    {
      id: 'crypto' as PaymentMethod,
      name: 'Криптовалюта',
      description: 'Bitcoin, Ethereum, USDT',
      icon: <Bitcoin className="w-6 h-6" />,
      fee: '1%',
      popular: false
    }
  ];

  useEffect(() => {
    if (selectedMethod === 'crypto') {
      loadCryptoRates();
    }
  }, [selectedMethod]);

  const loadCryptoRates = async () => {
    try {
      const rates = await cryptoPaymentService.getCryptoRates();
      setCryptoRates(rates);
    } catch (error) {
      console.error('Failed to load crypto rates:', error);
    }
  };

  const handleCardPayment = async () => {
    setIsProcessing(true);
    try {
      // Валидация карты
      if (!stripeService.validateCard(cardData.number, cardData.expiry, cardData.cvc)) {
        throw new Error('Неверные данные карты');
      }

      // Создание платежного намерения
      const paymentIntent = await stripeService.createPaymentIntent({
        amount: amount * 100, // Stripe работает с копейками
        currency: currency.toLowerCase(),
        orderId,
        userId: 'current-user-id', // Получить из контекста
        description: orderTitle
      });

      // Подтверждение платежа
      const result = await stripeService.confirmPayment({
        clientSecret: paymentIntent.clientSecret,
        paymentMethodId: 'pm_card_visa', // Здесь должен быть реальный ID
        returnUrl: window.location.origin + '/payment/success'
      });

      if (result) {
        setStep('success');
        onPaymentSuccess(result.id);
      }
    } catch (error) {
      onPaymentError(error instanceof Error ? error.message : 'Ошибка платежа');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCryptoPayment = async () => {
    setIsProcessing(true);
    try {
      const payment = await cryptoPaymentService.createPayment({
        amount,
        currency,
        cryptoCurrency: selectedCrypto,
        orderId,
        userId: 'current-user-id',
        description: orderTitle
      });

      setCryptoPayment(payment);
      setStep('processing');

      // Мониторинг платежа
      cryptoPaymentService.monitorPayment(payment.id, (updatedPayment) => {
        setCryptoPayment(updatedPayment);
        if (updatedPayment.status === 'completed') {
          setStep('success');
          onPaymentSuccess(updatedPayment.id);
        } else if (updatedPayment.status === 'failed' || updatedPayment.status === 'expired') {
          onPaymentError('Платеж не выполнен');
        }
      });
    } catch (error) {
      onPaymentError(error instanceof Error ? error.message : 'Ошибка создания криптоплатежа');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSBPPayment = async () => {
    setIsProcessing(true);
    try {
      // Здесь будет интеграция с СБП
      // Пока что имитируем успешный платеж
      setTimeout(() => {
        setStep('success');
        onPaymentSuccess('sbp_' + Date.now());
        setIsProcessing(false);
      }, 2000);
    } catch (error) {
      onPaymentError(error instanceof Error ? error.message : 'Ошибка СБП платежа');
      setIsProcessing(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
      <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-800" style={{ padding: '20px 30px' }}>
          <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
            Оплата заказа
          </h2>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: '30px' }}>
          {/* Order Info */}
          <div className="bg-gray-800/30 rounded-lg" style={{ padding: '20px', marginBottom: '25px' }}>
            <h3 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
              {orderTitle}
            </h3>
            <div className="flex items-center justify-between">
              <span className="text-gray-400" style={{ lineHeight: '1.6' }}>К оплате:</span>
              <span className="text-white font-bold text-lg" style={{ lineHeight: '1.4' }}>
                {formatAmount(amount, currency)}
              </span>
            </div>
          </div>

          {step === 'method' && (
            <div>
              <h3 className="text-white font-medium" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Выберите способ оплаты
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '25px' }}>
                {paymentMethods.map((method) => (
                  <button
                    key={method.id}
                    onClick={() => setSelectedMethod(method.id)}
                    className={`flex items-center justify-between rounded-lg border transition-all ${
                      selectedMethod === method.id
                        ? 'bg-red-500/20 border-red-500 text-white'
                        : 'bg-gray-800/30 border-gray-700 text-gray-300 hover:border-gray-600'
                    }`}
                    style={{ padding: '16px' }}
                  >
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <div className={`${selectedMethod === method.id ? 'text-red-400' : 'text-gray-400'}`}>
                        {method.icon}
                      </div>
                      <div className="text-left">
                        <div className="font-medium" style={{ lineHeight: '1.4' }}>
                          {method.name}
                          {method.popular && (
                            <span className="bg-red-500 text-white text-xs rounded-full ml-2" style={{ padding: '2px 8px' }}>
                              Популярно
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-400" style={{ lineHeight: '1.6' }}>
                          {method.description}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-400" style={{ lineHeight: '1.6' }}>
                        Комиссия {method.fee}
                      </div>
                      {selectedMethod === method.id && (
                        <Check className="w-5 h-5 text-red-400 mt-1" />
                      )}
                    </div>
                  </button>
                ))}
              </div>

              <button
                onClick={() => setStep('details')}
                className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-medium"
                style={{ padding: '12px', lineHeight: '1.4' }}
              >
                Продолжить
              </button>
            </div>
          )}

          {step === 'details' && selectedMethod === 'card' && (
            <div>
              <h3 className="text-white font-medium" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Данные банковской карты
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', marginBottom: '25px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Номер карты
                  </label>
                  <input
                    type="text"
                    value={cardData.number}
                    onChange={(e) => setCardData(prev => ({ ...prev, number: e.target.value }))}
                    placeholder="1234 5678 9012 3456"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  />
                </div>

                <div className="grid grid-cols-2" style={{ gap: '15px' }}>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Срок действия
                    </label>
                    <input
                      type="text"
                      value={cardData.expiry}
                      onChange={(e) => setCardData(prev => ({ ...prev, expiry: e.target.value }))}
                      placeholder="MM/YY"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      CVC
                    </label>
                    <input
                      type="text"
                      value={cardData.cvc}
                      onChange={(e) => setCardData(prev => ({ ...prev, cvc: e.target.value }))}
                      placeholder="123"
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Имя владельца
                  </label>
                  <input
                    type="text"
                    value={cardData.name}
                    onChange={(e) => setCardData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="IVAN PETROV"
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              <div className="flex items-center bg-gray-800/30 rounded-lg" style={{ padding: '12px', marginBottom: '20px', gap: '8px' }}>
                <Shield className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400" style={{ lineHeight: '1.6' }}>
                  Платеж защищен SSL-шифрованием
                </span>
              </div>

              <div className="flex" style={{ gap: '12px' }}>
                <button
                  onClick={() => setStep('method')}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Назад
                </button>
                <button
                  onClick={handleCardPayment}
                  disabled={isProcessing}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
                  style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Обработка...
                    </>
                  ) : (
                    `Оплатить ${formatAmount(amount, currency)}`
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'details' && selectedMethod === 'crypto' && (
            <div>
              <h3 className="text-white font-medium" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Выберите криптовалюту
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '25px' }}>
                {cryptoPaymentService.getSupportedCryptocurrencies().slice(0, 6).map((crypto) => (
                  <button
                    key={crypto.code}
                    onClick={() => setSelectedCrypto(crypto.code)}
                    className={`flex items-center justify-between rounded-lg border transition-all ${
                      selectedCrypto === crypto.code
                        ? 'bg-red-500/20 border-red-500 text-white'
                        : 'bg-gray-800/30 border-gray-700 text-gray-300 hover:border-gray-600'
                    }`}
                    style={{ padding: '12px' }}
                  >
                    <div className="flex items-center" style={{ gap: '12px' }}>
                      <span className="text-lg">{crypto.symbol}</span>
                      <div>
                        <div className="font-medium" style={{ lineHeight: '1.4' }}>{crypto.name}</div>
                        <div className="text-sm text-gray-400" style={{ lineHeight: '1.6' }}>{crypto.code}</div>
                      </div>
                    </div>
                    {selectedCrypto === crypto.code && (
                      <Check className="w-5 h-5 text-red-400" />
                    )}
                  </button>
                ))}
              </div>

              <div className="flex" style={{ gap: '12px' }}>
                <button
                  onClick={() => setStep('method')}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Назад
                </button>
                <button
                  onClick={handleCryptoPayment}
                  disabled={isProcessing}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
                  style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Создание...
                    </>
                  ) : (
                    'Создать платеж'
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'details' && selectedMethod === 'sbp' && (
            <div>
              <h3 className="text-white font-medium" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Оплата через СБП
              </h3>
              
              <div className="bg-gray-800/30 rounded-lg" style={{ padding: '20px', marginBottom: '25px' }}>
                <div className="flex items-center" style={{ gap: '12px', marginBottom: '15px' }}>
                  <Smartphone className="w-6 h-6 text-blue-400" />
                  <span className="text-white font-medium" style={{ lineHeight: '1.4' }}>
                    Система быстрых платежей
                  </span>
                </div>
                <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                  Вы будете перенаправлены в приложение вашего банка для подтверждения платежа.
                  Комиссия 0%.
                </p>
              </div>

              <div className="flex" style={{ gap: '12px' }}>
                <button
                  onClick={() => setStep('method')}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  style={{ padding: '12px', lineHeight: '1.4' }}
                >
                  Назад
                </button>
                <button
                  onClick={handleSBPPayment}
                  disabled={isProcessing}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-medium flex items-center justify-center"
                  style={{ padding: '12px', gap: '8px', lineHeight: '1.4' }}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Перенаправление...
                    </>
                  ) : (
                    `Оплатить ${formatAmount(amount, currency)}`
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && cryptoPayment && (
            <div className="text-center">
              <h3 className="text-white font-medium" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
                Ожидание платежа
              </h3>
              
              <div className="bg-gray-800/30 rounded-lg" style={{ padding: '20px', marginBottom: '20px' }}>
                <div className="text-center" style={{ marginBottom: '15px' }}>
                  <img 
                    src={cryptoPaymentService.generatePaymentQR(cryptoPayment.address, cryptoPayment.cryptoAmount, selectedCrypto)}
                    alt="QR Code"
                    className="mx-auto rounded-lg"
                    style={{ width: '150px', height: '150px' }}
                  />
                </div>
                
                <div style={{ marginBottom: '15px' }}>
                  <div className="text-sm text-gray-400" style={{ marginBottom: '5px', lineHeight: '1.6' }}>
                    Адрес для перевода:
                  </div>
                  <div className="flex items-center bg-gray-700/50 rounded-lg" style={{ padding: '8px' }}>
                    <span className="flex-1 text-white text-sm font-mono" style={{ lineHeight: '1.4' }}>
                      {cryptoPayment.address}
                    </span>
                    <button
                      onClick={() => copyToClipboard(cryptoPayment.address)}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div style={{ marginBottom: '15px' }}>
                  <div className="text-sm text-gray-400" style={{ marginBottom: '5px', lineHeight: '1.6' }}>
                    Сумма к переводу:
                  </div>
                  <div className="text-white font-bold text-lg" style={{ lineHeight: '1.4' }}>
                    {cryptoPaymentService.formatCryptoAmount(cryptoPayment.cryptoAmount, selectedCrypto)} {selectedCrypto}
                  </div>
                </div>

                <div className="text-sm text-gray-400" style={{ lineHeight: '1.6' }}>
                  Подтверждений: {cryptoPayment.confirmations} / {cryptoPayment.requiredConfirmations}
                </div>
              </div>

              <div className="flex items-center justify-center bg-yellow-500/20 text-yellow-400 rounded-lg" style={{ padding: '12px', gap: '8px' }}>
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm" style={{ lineHeight: '1.6' }}>
                  Переведите точную сумму на указанный адрес
                </span>
              </div>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                <Check className="w-8 h-8 text-green-400" />
              </div>
              
              <h3 className="text-white font-bold text-xl" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
                Платеж успешно выполнен!
              </h3>
              
              <p className="text-gray-400" style={{ marginBottom: '25px', lineHeight: '1.6' }}>
                Ваш заказ принят в работу. Мы отправили подтверждение на вашу почту.
              </p>

              <button
                onClick={onClose}
                className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-medium"
                style={{ padding: '12px', lineHeight: '1.4' }}
              >
                Закрыть
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
