'use client';

import React, { useState } from 'react';

interface PricingTier {
  id: string;
  name: string;
  price: number;
  duration: number;
  popular: boolean;
  features: string[];
}

function SimplePricing() {
  const [activeTab, setActiveTab] = useState<'artist' | 'track'>('artist');

  const artistTiers: PricingTier[] = [
    {
      id: '1',
      name: 'Старт',
      price: 25000,
      duration: 30,
      popular: false,
      features: [
        'Управление 3 социальными аккаунтами',
        '2 базовых бота для автоматизации',
        '5 email кампаний',
        'Привлечение 1000 подписчиков в Telegram',
        'Базовая аналитика',
        'Email поддержка'
      ]
    },
    {
      id: '2',
      name: 'Профи',
      price: 50000,
      duration: 30,
      popular: true,
      features: [
        'Управление 8 социальными аккаунтами',
        '6 продвинутых ИИ ботов',
        'Неограниченные email кампании',
        'Привлечение 5000 подписчиков в Telegram',
        'Создание контента с ИИ',
        'Работа с инфлюенсерами',
        'Продвинутая аналитика',
        'Приоритетная поддержка 24/7'
      ]
    },
    {
      id: '3',
      name: 'Энтерпрайз',
      price: 100000,
      duration: 30,
      popular: false,
      features: [
        'Неограниченные социальные аккаунты',
        'ИИ боты премиум с GPT-4',
        'Вирусные стратегии продвижения',
        'Персональный менеджер',
        'Массовый охват и скрапинг',
        'Кастомные боты под задачи',
        'White Label решения'
      ]
    }
  ];

  const trackTiers: PricingTier[] = [
    {
      id: '1',
      name: 'Базовый',
      price: 15000,
      duration: 14,
      popular: false,
      features: [
        'Размещение в 10+ плейлистах',
        'Продвижение в Instagram, TikTok, VK',
        '2 базовых бота для автолайков',
        'Email рассылка о релизе',
        'Посты в 5 Telegram каналах'
      ]
    },
    {
      id: '2',
      name: 'Вирусный',
      price: 35000,
      duration: 21,
      popular: true,
      features: [
        'Вирусная кампания с трендовым контентом',
        'Сеть из 20+ инфлюенсеров',
        'ИИ генерация мемов и видео',
        'Массовое вовлечение через ботов',
        'Размещение в 50+ Telegram каналах',
        'Продвижение на радиостанциях',
        'Пресс-релизы в музыкальных СМИ'
      ]
    },
    {
      id: '3',
      name: 'Платиновый',
      price: 75000,
      duration: 30,
      popular: false,
      features: [
        'Платиновая кампания с максимальным охватом',
        'Сеть знаменитостей для продвижения',
        'Армия из сотен умных ботов',
        'Глобальное международное продвижение',
        'Сеть из 100+ Telegram каналов',
        'Освещение во всех СМИ',
        'Продвижение в топы стриминговых сервисов',
        'Выделенная команда из 5+ специалистов'
      ]
    }
  ];

  const tiers = activeTab === 'artist' ? artistTiers : trackTiers;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const handleSelectPlan = (tier: PricingTier) => {
    const formattedPrice = formatPrice(tier.price);
    alert(`Выбран тариф: ${tier.name} за ${formattedPrice}\n\nДля оформления заказа свяжитесь с нами!`);
  };

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Выберите свой <span className="text-red-500">тариф</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            Профессиональное продвижение музыки с гарантированным результатом.
            Выберите подходящий тариф и начните свой путь к успеху уже сегодня.
          </p>

          {/* Tab Switcher */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800/50 rounded-2xl p-2 backdrop-blur-xl border border-gray-700/50">
              <button
                onClick={() => setActiveTab('artist')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'artist'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                👥 Продвижение артистов
              </button>
              <button
                onClick={() => setActiveTab('track')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'track'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                🎵 Продвижение треков
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              className={`relative bg-gray-900/50 backdrop-blur-xl rounded-3xl border transition-all duration-300 hover:scale-105 hover:shadow-2xl p-8 ${
                tier.popular
                  ? 'border-red-500/50 shadow-lg shadow-red-500/25'
                  : 'border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              {/* Popular Badge */}
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-red-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                    ⭐ Популярный
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                <div className="mb-2">
                  <div className="text-4xl font-bold text-white">
                    {formatPrice(tier.price)}
                  </div>
                </div>
                <p className="text-gray-400">за {tier.duration} дней</p>
              </div>

              {/* Features */}
              <div className="mb-8">
                <h4 className="text-white font-semibold mb-4">Что включено:</h4>
                <ul className="space-y-3">
                  {tier.features.slice(0, 6).map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="text-red-400 mt-0.5">✅</div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </li>
                  ))}
                  {tier.features.length > 6 && (
                    <li className="text-gray-400 text-sm">
                      +{tier.features.length - 6} дополнительных функций
                    </li>
                  )}
                </ul>
              </div>

              {/* CTA Button */}
              <button
                onClick={() => handleSelectPlan(tier)}
                className={`w-full py-4 rounded-xl font-semibold transition-all duration-300 ${
                  tier.popular
                    ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg shadow-red-500/25'
                    : 'bg-gray-700 hover:bg-gray-600 text-white'
                }`}
              >
                Выбрать тариф
              </button>

              {/* Payment Methods */}
              <div className="mt-4 flex justify-center items-center gap-2 text-gray-400 text-xs">
                💳 ₿ 📱 Карта • Крипто • СБП
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-gray-400 mb-4">
            Все тарифы включают бесплатную консультацию и гарантию результата
          </p>
          <div className="flex justify-center items-center gap-8 text-sm text-gray-500">
            <span>✅ Безопасные платежи</span>
            <span>✅ Гарантия результата</span>
            <span>👥 150+ успешных кампаний</span>
          </div>
        </div>
      </div>
    </section>
  );
}

export default SimplePricing;
