'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  variant?: 'default' | 'premium' | 'glass' | 'solid';
  hover?: boolean;
  className?: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  hover = true,
  className = '',
  onClick
}) => {
  const baseClasses = 'rounded-3xl transition-all duration-300';
  
  const variants = {
    default: 'bg-black/20 backdrop-blur-xl border border-purple-500/20 hover:border-purple-500/40',
    premium: 'bg-gradient-to-br from-black/30 to-purple-900/20 backdrop-blur-xl border-2 border-purple-500/50 shadow-lg shadow-purple-500/20',
    glass: 'bg-black/10 backdrop-blur-2xl border border-white/10 hover:border-white/20',
    solid: 'bg-gray-900 border border-gray-700 hover:border-gray-600'
  };

  const hoverEffects = hover ? 'hover:transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20' : '';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      onClick={onClick}
      className={`${baseClasses} ${variants[variant]} ${hoverEffects} ${className} ${onClick ? 'cursor-pointer' : ''}`}
    >
      {children}
    </motion.div>
  );
};

export default Card;
