'use client';

import { useState, useEffect } from 'react';
import {
  Package,
  Clock,
  CheckCircle,
  AlertCircle,
  X,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
  Download,
  MoreHorizontal,
  Calendar,
  DollarSign,
  User,
  Music,
  Mic
} from 'lucide-react';
import OrderEditor from '@/components/OrderEditor';

interface Order {
  id: string;
  type: 'artist' | 'track';
  clientName: string;
  clientEmail: string;
  serviceName: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  price: number;
  progress: number;
  createdAt: string;
  dueDate?: string;
  assignedTo?: string;
  description?: string;
  requirements?: Record<string, any>;
}

interface OrdersManagerProps {
  orders?: Order[];
  onOrderUpdate?: (orderId: string, updates: Partial<Order>) => void;
  onOrderDelete?: (orderId: string) => void;
  showActions?: boolean;
}

const OrdersManager: React.FC<OrdersManagerProps> = ({
  orders = [],
  onOrderUpdate,
  onOrderDelete,
  showActions = true
}) => {
  const [filteredOrders, setFilteredOrders] = useState<Order[]>(orders);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [showOrderEditor, setShowOrderEditor] = useState(false);
  const [editingOrder, setEditingOrder] = useState<Order | undefined>(undefined);
  const [editorMode, setEditorMode] = useState<'create' | 'edit' | 'view'>('create');

  // Mock data если заказы не переданы
  const mockOrders: Order[] = [
    {
      id: 'ORD-001',
      type: 'track',
      clientName: 'MORGENSHTERN',
      clientEmail: '<EMAIL>',
      serviceName: 'Базовый пакет продвижения трека',
      status: 'in_progress',
      priority: 'high',
      price: 25000,
      progress: 65,
      createdAt: '2024-01-15T10:30:00Z',
      dueDate: '2024-02-15T10:30:00Z',
      assignedTo: 'Анна Петрова',
      description: 'Продвижение нового трека в социальных сетях и на радиостанциях'
    },
    {
      id: 'ORD-002',
      type: 'artist',
      clientName: 'Элджей',
      clientEmail: '<EMAIL>',
      serviceName: 'Премиум пакет продвижения артиста',
      status: 'pending',
      priority: 'medium',
      price: 100000,
      progress: 0,
      createdAt: '2024-01-16T14:20:00Z',
      dueDate: '2024-03-16T14:20:00Z',
      description: 'Полное продвижение артиста на всех платформах'
    },
    {
      id: 'ORD-003',
      type: 'track',
      clientName: 'Скриптонит',
      clientEmail: '<EMAIL>',
      serviceName: 'Расширенное продвижение трека',
      status: 'completed',
      priority: 'low',
      price: 35000,
      progress: 100,
      createdAt: '2024-01-10T09:15:00Z',
      dueDate: '2024-02-10T09:15:00Z',
      assignedTo: 'Михаил Иванов',
      description: 'Продвижение трека с созданием промо контента'
    }
  ];

  const displayOrders = orders.length > 0 ? orders : mockOrders;

  useEffect(() => {
    let filtered = displayOrders;

    // Фильтр по статусу
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Фильтр по типу
    if (typeFilter !== 'all') {
      filtered = filtered.filter(order => order.type === typeFilter);
    }

    // Поиск
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  }, [displayOrders, statusFilter, typeFilter, searchQuery]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'confirmed': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'in_progress': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'completed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'cancelled': return 'bg-pink-500/20 text-pink-400 border-pink-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <Package className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <X className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-pink-500/20 text-pink-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-green-500/20 text-green-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = () => {
    if (selectedOrders.length === filteredOrders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(filteredOrders.map(order => order.id));
    }
  };

  const handleCreateOrder = () => {
    setEditingOrder(undefined);
    setEditorMode('create');
    setShowOrderEditor(true);
  };

  const handleEditOrder = (order: Order) => {
    setEditingOrder(order);
    setEditorMode('edit');
    setShowOrderEditor(true);
  };

  const handleViewOrder = (order: Order) => {
    setEditingOrder(order);
    setEditorMode('view');
    setShowOrderEditor(true);
  };

  const handleSaveOrder = (orderData: Order) => {
    if (editorMode === 'create') {
      // Логика создания нового заказа
      console.log('Creating new order:', orderData);
    } else if (editorMode === 'edit') {
      // Логика обновления заказа
      console.log('Updating order:', orderData);
      if (onOrderUpdate && editingOrder) {
        onOrderUpdate(editingOrder.id, orderData);
      }
    }
    setShowOrderEditor(false);
  };

  return (
    <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '25px' }}>
      {/* Header */}
      <div className="flex items-center justify-between" style={{ marginBottom: '25px' }}>
        <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
          Управление заказами
        </h2>
        
        {showActions && (
          <div className="flex items-center" style={{ gap: '10px' }}>
            <button className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center" style={{ padding: '8px 12px', gap: '6px', lineHeight: '1.4' }}>
              <Download className="w-4 h-4" />
              <span className="text-sm">Экспорт</span>
            </button>
            <button
              onClick={handleCreateOrder}
              className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
              style={{ padding: '8px 12px', gap: '6px', lineHeight: '1.4' }}
            >
              <Package className="w-4 h-4" />
              <span className="text-sm">Новый заказ</span>
            </button>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center justify-between" style={{ gap: '15px', marginBottom: '20px' }}>
        <div className="flex items-center" style={{ gap: '15px' }}>
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Поиск заказов..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
              style={{ paddingLeft: '35px', paddingRight: '12px', paddingTop: '8px', paddingBottom: '8px', lineHeight: '1.4', minWidth: '200px' }}
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-red-500"
            style={{ padding: '8px 12px', lineHeight: '1.4' }}
          >
            <option value="all">Все статусы</option>
            <option value="pending">Ожидание</option>
            <option value="confirmed">Подтвержден</option>
            <option value="in_progress">В работе</option>
            <option value="completed">Выполнен</option>
            <option value="cancelled">Отменен</option>
          </select>

          {/* Type Filter */}
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-red-500"
            style={{ padding: '8px 12px', lineHeight: '1.4' }}
          >
            <option value="all">Все типы</option>
            <option value="artist">Артисты</option>
            <option value="track">Треки</option>
          </select>
        </div>

        <div className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
          Найдено: {filteredOrders.length} из {displayOrders.length}
        </div>
      </div>

      {/* Orders Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700/50">
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>
                <input
                  type="checkbox"
                  checked={selectedOrders.length === filteredOrders.length && filteredOrders.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-600 bg-gray-700 text-red-500 focus:ring-red-500"
                />
              </th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>ID / Клиент</th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Услуга</th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Статус</th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Приоритет</th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Прогресс</th>
              <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Сумма</th>
              {showActions && (
                <th className="text-left text-sm font-semibold text-gray-300" style={{ padding: '12px 8px' }}>Действия</th>
              )}
            </tr>
          </thead>
          <tbody>
            {filteredOrders.map((order) => (
              <tr key={order.id} className="border-b border-gray-700/30 hover:bg-gray-700/20 transition-colors">
                <td style={{ padding: '12px 8px' }}>
                  <input
                    type="checkbox"
                    checked={selectedOrders.includes(order.id)}
                    onChange={() => handleSelectOrder(order.id)}
                    className="rounded border-gray-600 bg-gray-700 text-red-500 focus:ring-red-500"
                  />
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <div>
                    <div className="flex items-center" style={{ gap: '8px', marginBottom: '2px' }}>
                      <span className="text-white font-medium text-sm" style={{ lineHeight: '1.4' }}>{order.id}</span>
                      {order.type === 'artist' ? (
                        <Mic className="w-3 h-3 text-red-500" />
                      ) : (
                        <Music className="w-3 h-3 text-blue-500" />
                      )}
                    </div>
                    <div className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>{order.clientName}</div>
                    <div className="text-gray-500 text-xs" style={{ lineHeight: '1.6' }}>{formatDate(order.createdAt)}</div>
                  </div>
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <div className="text-white text-sm" style={{ lineHeight: '1.4' }}>{order.serviceName}</div>
                  {order.description && (
                    <div className="text-gray-400 text-xs" style={{ lineHeight: '1.6', marginTop: '2px' }}>
                      {order.description.length > 50 ? `${order.description.substring(0, 50)}...` : order.description}
                    </div>
                  )}
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <span className={`inline-flex items-center border rounded-full text-xs ${getStatusColor(order.status)}`} style={{ gap: '4px', padding: '4px 8px' }}>
                    {getStatusIcon(order.status)}
                    <span className="capitalize">{order.status.replace('_', ' ')}</span>
                  </span>
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <span className={`inline-flex rounded-full text-xs ${getPriorityColor(order.priority)}`} style={{ padding: '4px 8px', lineHeight: '1.4' }}>
                    {order.priority}
                  </span>
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <div className="flex items-center" style={{ gap: '8px' }}>
                    <div className="flex-1 bg-gray-700 rounded-full h-2 max-w-[80px]">
                      <div 
                        className="bg-red-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${order.progress}%` }}
                      ></div>
                    </div>
                    <span className="text-gray-400 text-xs" style={{ lineHeight: '1.4' }}>{order.progress}%</span>
                  </div>
                </td>
                
                <td style={{ padding: '12px 8px' }}>
                  <div className="text-white font-semibold text-sm" style={{ lineHeight: '1.4' }}>
                    {order.price.toLocaleString()} ₽
                  </div>
                </td>
                
                {showActions && (
                  <td style={{ padding: '12px 8px' }}>
                    <div className="flex items-center" style={{ gap: '4px' }}>
                      <button
                        onClick={() => handleViewOrder(order)}
                        className="w-7 h-7 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                        title="Просмотр"
                      >
                        <Eye className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => handleEditOrder(order)}
                        className="w-7 h-7 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                        title="Редактировать"
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                      <button 
                        className="w-7 h-7 rounded-lg bg-gray-700 hover:bg-red-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                        title="Удалить"
                        onClick={() => onOrderDelete?.(order.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredOrders.length === 0 && (
        <div className="text-center" style={{ padding: '40px 20px' }}>
          <Package className="w-12 h-12 text-gray-600 mx-auto" style={{ marginBottom: '15px' }} />
          <h3 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
            Заказы не найдены
          </h3>
          <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
            Попробуйте изменить фильтры или создать новый заказ
          </p>
        </div>
      )}

      {/* Order Editor Modal */}
      <OrderEditor
        isOpen={showOrderEditor}
        onClose={() => setShowOrderEditor(false)}
        order={editingOrder}
        onSave={handleSaveOrder}
        mode={editorMode}
      />
    </div>
  );
};

export default OrdersManager;
