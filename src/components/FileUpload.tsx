'use client';

import { useState, useRef, useCallback } from 'react';
import { 
  Upload, 
  X, 
  File, 
  Image, 
  Music, 
  Video,
  Check,
  AlertCircle,
  Trash2,
  Eye,
  Download
} from 'lucide-react';

interface FileUploadProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // в MB
  maxFiles?: number;
  onFilesChange?: (files: File[]) => void;
  className?: string;
  label?: string;
  description?: string;
}

interface UploadedFile {
  file: File;
  id: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  preview?: string;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  accept = '*/*',
  multiple = false,
  maxSize = 10, // 10MB по умолчанию
  maxFiles = 5,
  onFilesChange,
  className = '',
  label = 'Загрузить файлы',
  description = 'Перетащите файлы сюда или нажмите для выбора'
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substring(2, 15);

  const getFileIcon = (file: File) => {
    const type = file.type;
    if (type.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (type.startsWith('audio/')) return <Music className="w-5 h-5" />;
    if (type.startsWith('video/')) return <Video className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize * 1024 * 1024) {
      return `Файл слишком большой. Максимальный размер: ${maxSize}MB`;
    }
    
    if (accept !== '*/*' && !accept.split(',').some(type => 
      file.type.match(type.trim().replace('*', '.*'))
    )) {
      return 'Неподдерживаемый тип файла';
    }

    return null;
  };

  const createFilePreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  const simulateUpload = (fileId: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, progress: 100, status: 'completed' as const }
            : f
        ));
      } else {
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId 
            ? { ...f, progress: Math.round(progress) }
            : f
        ));
      }
    }, 200);
  };

  const handleFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    
    if (!multiple && fileArray.length > 1) {
      alert('Можно загрузить только один файл');
      return;
    }

    if (uploadedFiles.length + fileArray.length > maxFiles) {
      alert(`Максимальное количество файлов: ${maxFiles}`);
      return;
    }

    const newFiles: UploadedFile[] = [];

    for (const file of fileArray) {
      const error = validateFile(file);
      const id = generateId();
      const preview = await createFilePreview(file);

      const uploadedFile: UploadedFile = {
        file,
        id,
        progress: 0,
        status: error ? 'error' : 'uploading',
        preview,
        error
      };

      newFiles.push(uploadedFile);

      if (!error) {
        // Симулируем загрузку
        setTimeout(() => simulateUpload(id), 100);
      }
    }

    setUploadedFiles(prev => [...prev, ...newFiles]);
    
    if (onFilesChange) {
      const allFiles = [...uploadedFiles.map(f => f.file), ...newFiles.map(f => f.file)];
      onFilesChange(allFiles);
    }
  }, [uploadedFiles, multiple, maxFiles, onFilesChange]);

  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(f => f.id !== id);
      if (onFilesChange) {
        onFilesChange(updated.map(f => f.file));
      }
      return updated;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Upload Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        className={`border-2 border-dashed rounded-xl cursor-pointer transition-all duration-300 ${
          isDragOver
            ? 'border-red-500 bg-red-500/10'
            : 'border-gray-600 hover:border-gray-500 hover:bg-gray-800/30'
        }`}
        style={{ padding: '40px 20px' }}
      >
        <div className="text-center">
          <div className="flex justify-center" style={{ marginBottom: '15px' }}>
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              isDragOver ? 'bg-red-500/20 text-red-400' : 'bg-gray-800 text-gray-400'
            }`}>
              <Upload className="w-6 h-6" />
            </div>
          </div>
          
          <h3 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
            {label}
          </h3>
          
          <p className="text-gray-400 text-sm" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
            {description}
          </p>
          
          <div className="text-xs text-gray-500" style={{ lineHeight: '1.6' }}>
            Максимальный размер: {maxSize}MB • Максимум файлов: {maxFiles}
            {accept !== '*/*' && (
              <div style={{ marginTop: '5px' }}>
                Поддерживаемые форматы: {accept}
              </div>
            )}
          </div>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <h4 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
            Загруженные файлы ({uploadedFiles.length})
          </h4>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            {uploadedFiles.map((uploadedFile) => (
              <div
                key={uploadedFile.id}
                className="bg-gray-800/30 border border-gray-700/50 rounded-lg"
                style={{ padding: '15px' }}
              >
                <div className="flex items-center" style={{ gap: '12px' }}>
                  {/* File Preview/Icon */}
                  <div className="w-10 h-10 rounded-lg bg-gray-700 flex items-center justify-center overflow-hidden">
                    {uploadedFile.preview ? (
                      <img 
                        src={uploadedFile.preview} 
                        alt={uploadedFile.file.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-gray-400">
                        {getFileIcon(uploadedFile.file)}
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center" style={{ gap: '8px', marginBottom: '4px' }}>
                      <p className="text-white font-medium truncate" style={{ lineHeight: '1.4' }}>
                        {uploadedFile.file.name}
                      </p>
                      {uploadedFile.status === 'completed' && (
                        <Check className="w-4 h-4 text-green-400" />
                      )}
                      {uploadedFile.status === 'error' && (
                        <AlertCircle className="w-4 h-4 text-red-400" />
                      )}
                    </div>
                    
                    <div className="flex items-center" style={{ gap: '10px' }}>
                      <span className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                        {formatFileSize(uploadedFile.file.size)}
                      </span>
                      
                      {uploadedFile.status === 'uploading' && (
                        <span className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
                          {uploadedFile.progress}%
                        </span>
                      )}
                      
                      {uploadedFile.status === 'error' && uploadedFile.error && (
                        <span className="text-red-400 text-sm" style={{ lineHeight: '1.6' }}>
                          {uploadedFile.error}
                        </span>
                      )}
                    </div>

                    {/* Progress Bar */}
                    {uploadedFile.status === 'uploading' && (
                      <div className="w-full bg-gray-700 rounded-full h-1" style={{ marginTop: '8px' }}>
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${uploadedFile.progress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center" style={{ gap: '8px' }}>
                    {uploadedFile.status === 'completed' && uploadedFile.preview && (
                      <button
                        className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                        title="Просмотр"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => removeFile(uploadedFile.id)}
                      className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-red-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                      title="Удалить"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
