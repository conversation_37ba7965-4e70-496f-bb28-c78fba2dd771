'use client';

import { useState, useEffect } from 'react';
import { Bo<PERSON>, <PERSON>, Settings, Play, Pause, BarChart3, MessageCircle, Target, Zap, AlertCircle, CheckCircle, Clock, RefreshCw } from 'lucide-react';

interface TelegramBotStats {
  totalSubscribers: number;
  newSubscribers: number;
  activeChannels: number;
  messagesPerDay: number;
  conversionRate: number;
}

interface BotChannel {
  id: string;
  name: string;
  username: string;
  subscribers: number;
  isActive: boolean;
  lastActivity: string;
}

const TelegramBot = () => {
  const [isActive, setIsActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<TelegramBotStats>({
    totalSubscribers: 0,
    newSubscribers: 0,
    activeChannels: 0,
    messagesPerDay: 0,
    conversionRate: 0
  });
  const [channels, setChannels] = useState<BotChannel[]>([]);
  const [botToken, setBotToken] = useState('');
  const [targetChannels, setTargetChannels] = useState('');
  const [welcomeMessage, setWelcomeMessage] = useState('Добро пожаловать в наш канал! 🎵');
  const [autoReply, setAutoReply] = useState(true);

  useEffect(() => {
    loadBotData();
  }, []);

  const loadBotData = async () => {
    try {
      setLoading(true);
      // Здесь будет загрузка данных из API
      // Пока используем моковые данные
      setStats({
        totalSubscribers: 1247,
        newSubscribers: 23,
        activeChannels: 3,
        messagesPerDay: 156,
        conversionRate: 12.5
      });
      
      setChannels([
        {
          id: '1',
          name: 'HIVE Music Promo',
          username: '@hive_music_promo',
          subscribers: 523,
          isActive: true,
          lastActivity: '2 минуты назад'
        },
        {
          id: '2',
          name: 'Artist Promotion Hub',
          username: '@artist_promo_hub',
          subscribers: 724,
          isActive: true,
          lastActivity: '15 минут назад'
        }
      ]);
    } catch (error) {
      console.error('Error loading bot data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleBot = async () => {
    try {
      setLoading(true);
      setIsActive(!isActive);
      // Здесь будет API вызов для включения/выключения бота
      await new Promise(resolve => setTimeout(resolve, 1000)); // Имитация API вызова
    } catch (error) {
      console.error('Error toggling bot:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveBotSettings = async () => {
    try {
      setLoading(true);
      // Здесь будет API вызов для сохранения настроек
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Telegram Bot</h2>
            <p className="text-gray-400">Автоматический сбор аудитории</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
            isActive ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
          }`}>
            {isActive ? <CheckCircle className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
            {isActive ? 'Активен' : 'Неактивен'}
          </div>
          <button
            onClick={toggleBot}
            disabled={loading}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
              isActive 
                ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30' 
                : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : isActive ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isActive ? 'Остановить' : 'Запустить'}
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Users className="w-6 h-6 text-blue-400" />
            <span className="text-sm text-green-400">+{stats.newSubscribers}</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.totalSubscribers.toLocaleString()}</div>
          <div className="text-sm text-gray-400">Всего подписчиков</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Target className="w-6 h-6 text-purple-400" />
            <span className="text-sm text-blue-400">{stats.activeChannels}</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.activeChannels}</div>
          <div className="text-sm text-gray-400">Активных каналов</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <MessageCircle className="w-6 h-6 text-green-400" />
            <span className="text-sm text-purple-400">{stats.messagesPerDay}</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.messagesPerDay}</div>
          <div className="text-sm text-gray-400">Сообщений в день</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <BarChart3 className="w-6 h-6 text-yellow-400" />
            <span className="text-sm text-green-400">+2.1%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.conversionRate}%</div>
          <div className="text-sm text-gray-400">Конверсия</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Zap className="w-6 h-6 text-orange-400" />
            <span className="text-sm text-yellow-400">Активно</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">24/7</div>
          <div className="text-sm text-gray-400">Работа бота</div>
        </div>
      </div>

      {/* Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Bot Configuration */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center gap-3 mb-6">
            <Settings className="w-5 h-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">Настройки бота</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Токен бота
              </label>
              <input
                type="password"
                value={botToken}
                onChange={(e) => setBotToken(e.target.value)}
                placeholder="Введите токен Telegram бота"
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Целевые каналы
              </label>
              <textarea
                value={targetChannels}
                onChange={(e) => setTargetChannels(e.target.value)}
                placeholder="@channel1, @channel2, @channel3"
                rows={3}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Приветственное сообщение
              </label>
              <textarea
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-300">Автоответы</div>
                <div className="text-xs text-gray-400">Автоматические ответы на сообщения</div>
              </div>
              <button
                onClick={() => setAutoReply(!autoReply)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoReply ? 'bg-purple-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoReply ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <button
              onClick={saveBotSettings}
              disabled={loading}
              className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-600 hover:to-blue-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Сохранение...' : 'Сохранить настройки'}
            </button>
          </div>
        </div>

        {/* Active Channels */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center gap-3 mb-6">
            <Target className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">Активные каналы</h3>
          </div>
          
          <div className="space-y-4">
            {channels.map((channel) => (
              <div key={channel.id} className="bg-gray-700/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      channel.isActive ? 'bg-green-400' : 'bg-gray-400'
                    }`} />
                    <div>
                      <div className="font-medium text-white">{channel.name}</div>
                      <div className="text-sm text-gray-400">{channel.username}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-white">{channel.subscribers.toLocaleString()}</div>
                    <div className="text-xs text-gray-400">подписчиков</div>
                  </div>
                </div>
                <div className="text-xs text-gray-400">
                  Последняя активность: {channel.lastActivity}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TelegramBot;
