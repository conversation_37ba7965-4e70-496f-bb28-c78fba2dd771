'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  Clock,
  Play,
  Pause,
  Square,
  AlertTriangle,
  TrendingUp,
  BarChart3,
  Calendar,
  Target,
  Award,
  Zap,
  Users,
  Heart,
  Share2,
  Eye,
  Download,
  Star
} from 'lucide-react';

interface CampaignStatusManagerProps {
  campaign: any;
  onStatusChange: (newStatus: string, data?: any) => void;
  type: 'email' | 'social' | 'bot' | 'telegram';
}

const CampaignStatusManager = ({ campaign, onStatusChange, type }: CampaignStatusManagerProps) => {
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [completionData, setCompletionData] = useState({
    finalStats: {},
    achievements: [],
    nextSteps: [],
    feedback: ''
  });

  const statusConfig = {
    draft: {
      name: 'Черновик',
      color: 'text-gray-400',
      bgColor: 'bg-gray-500/20',
      icon: Clock,
      actions: ['start', 'edit', 'delete']
    },
    active: {
      name: 'Активна',
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      icon: Play,
      actions: ['pause', 'complete', 'edit']
    },
    paused: {
      name: 'Приостановлена',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20',
      icon: Pause,
      actions: ['resume', 'complete', 'cancel']
    },
    completed: {
      name: 'Завершена',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      icon: CheckCircle,
      actions: ['view-report', 'archive']
    },
    cancelled: {
      name: 'Отменена',
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      icon: Square,
      actions: ['restart', 'archive']
    }
  };

  const currentStatus = statusConfig[campaign.status] || statusConfig.draft;
  const StatusIcon = currentStatus.icon;

  const handleStatusChange = (newStatus: string) => {
    if (newStatus === 'completed') {
      setShowCompletionModal(true);
    } else {
      onStatusChange(newStatus);
    }
  };

  const handleComplete = () => {
    const finalData = {
      status: 'completed',
      completedAt: new Date().toISOString(),
      finalStats: completionData.finalStats,
      achievements: completionData.achievements,
      nextSteps: completionData.nextSteps,
      feedback: completionData.feedback,
      performance: calculatePerformance()
    };
    
    onStatusChange('completed', finalData);
    setShowCompletionModal(false);
  };

  const calculatePerformance = () => {
    // Расчет общей эффективности кампании
    const stats = campaign.stats || {};
    let score = 0;
    let factors = [];

    if (type === 'email') {
      const openRate = (stats.opened / stats.sent) * 100 || 0;
      const clickRate = (stats.clicked / stats.opened) * 100 || 0;
      score = (openRate * 0.6 + clickRate * 0.4);
      factors = [
        { name: 'Open Rate', value: openRate, target: 25 },
        { name: 'Click Rate', value: clickRate, target: 15 }
      ];
    } else if (type === 'social') {
      const engagementRate = stats.engagement || 0;
      const reachGrowth = stats.reachGrowth || 0;
      score = (engagementRate * 0.7 + reachGrowth * 0.3);
      factors = [
        { name: 'Engagement Rate', value: engagementRate, target: 5 },
        { name: 'Reach Growth', value: reachGrowth, target: 20 }
      ];
    }

    return {
      score: Math.min(100, Math.max(0, score)),
      factors,
      grade: score >= 80 ? 'A' : score >= 60 ? 'B' : score >= 40 ? 'C' : 'D'
    };
  };

  const getAchievements = () => {
    const performance = calculatePerformance();
    const achievements = [];

    if (performance.score >= 80) {
      achievements.push({
        title: 'Отличная производительность',
        description: 'Кампания показала выдающиеся результаты',
        icon: Award,
        color: 'text-yellow-400'
      });
    }

    if (campaign.stats?.viral_coefficient > 1.5) {
      achievements.push({
        title: 'Вирусный эффект',
        description: 'Контент получил вирусное распространение',
        icon: Zap,
        color: 'text-purple-400'
      });
    }

    if (campaign.stats?.new_followers > 1000) {
      achievements.push({
        title: 'Рост аудитории',
        description: 'Привлечено более 1000 новых подписчиков',
        icon: Users,
        color: 'text-blue-400'
      });
    }

    return achievements;
  };

  return (
    <div className="space-y-4">
      {/* Current Status */}
      <div className={`flex items-center gap-3 px-4 py-3 rounded-xl ${currentStatus.bgColor} border border-gray-700/30`}>
        <StatusIcon className={`w-5 h-5 ${currentStatus.color}`} />
        <div className="flex-1">
          <p className={`font-medium ${currentStatus.color}`}>{currentStatus.name}</p>
          {campaign.progress !== undefined && (
            <div className="mt-2">
              <div className="flex items-center justify-between text-sm text-gray-400 mb-1">
                <span>Прогресс</span>
                <span>{campaign.progress}%</span>
              </div>
              <div className="w-full bg-gray-800 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    campaign.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${campaign.progress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        {currentStatus.actions.map((action) => {
          const actionConfig = {
            start: { label: 'Запустить', color: 'bg-green-500 hover:bg-green-600', icon: Play },
            pause: { label: 'Приостановить', color: 'bg-yellow-500 hover:bg-yellow-600', icon: Pause },
            resume: { label: 'Возобновить', color: 'bg-blue-500 hover:bg-blue-600', icon: Play },
            complete: { label: 'Завершить', color: 'bg-purple-500 hover:bg-purple-600', icon: CheckCircle },
            cancel: { label: 'Отменить', color: 'bg-red-500 hover:bg-red-600', icon: Square },
            edit: { label: 'Редактировать', color: 'bg-gray-600 hover:bg-gray-700', icon: Target },
            delete: { label: 'Удалить', color: 'bg-red-500 hover:bg-red-600', icon: Square },
            'view-report': { label: 'Отчет', color: 'bg-indigo-500 hover:bg-indigo-600', icon: BarChart3 },
            archive: { label: 'Архивировать', color: 'bg-gray-600 hover:bg-gray-700', icon: Download },
            restart: { label: 'Перезапустить', color: 'bg-green-500 hover:bg-green-600', icon: Play }
          };

          const config = actionConfig[action];
          if (!config) return null;

          const ActionIcon = config.icon;

          return (
            <button
              key={action}
              onClick={() => handleStatusChange(action === 'resume' ? 'active' : action)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-white text-sm font-medium transition-all hover:scale-105 ${config.color}`}
            >
              <ActionIcon className="w-4 h-4" />
              {config.label}
            </button>
          );
        })}
      </div>

      {/* Performance Metrics */}
      {campaign.status === 'active' && campaign.stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <Eye className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-gray-400">Охват</span>
            </div>
            <p className="text-lg font-bold text-white">{campaign.stats.reach || '0'}</p>
          </div>
          
          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <Heart className="w-4 h-4 text-red-400" />
              <span className="text-xs text-gray-400">Лайки</span>
            </div>
            <p className="text-lg font-bold text-white">{campaign.stats.likes || '0'}</p>
          </div>
          
          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <Share2 className="w-4 h-4 text-green-400" />
              <span className="text-xs text-gray-400">Репосты</span>
            </div>
            <p className="text-lg font-bold text-white">{campaign.stats.shares || '0'}</p>
          </div>
          
          <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-purple-400" />
              <span className="text-xs text-gray-400">Рост</span>
            </div>
            <p className="text-lg font-bold text-green-400">+{campaign.stats.growth || '0'}%</p>
          </div>
        </div>
      )}

      {/* Completion Modal */}
      <AnimatePresence>
        {showCompletionModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowCompletionModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-2xl border border-gray-800 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Завершение кампании</h3>
                
                {/* Performance Summary */}
                <div className="mb-8">
                  <h4 className="text-lg font-semibold text-white mb-4">Итоговая производительность</h4>
                  <div className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-gray-300">Общая оценка</span>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-white">{calculatePerformance().grade}</span>
                        <span className="text-sm text-gray-400">({calculatePerformance().score.toFixed(1)}%)</span>
                      </div>
                    </div>
                    
                    {calculatePerformance().factors.map((factor, index) => (
                      <div key={index} className="flex items-center justify-between py-2">
                        <span className="text-gray-400">{factor.name}</span>
                        <div className="flex items-center gap-2">
                          <span className={`font-semibold ${factor.value >= factor.target ? 'text-green-400' : 'text-yellow-400'}`}>
                            {factor.value.toFixed(1)}%
                          </span>
                          <span className="text-xs text-gray-500">({factor.target}% цель)</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                <div className="mb-8">
                  <h4 className="text-lg font-semibold text-white mb-4">Достижения</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {getAchievements().map((achievement, index) => {
                      const AchievementIcon = achievement.icon;
                      return (
                        <div key={index} className="bg-gray-800/30 rounded-xl p-4 border border-gray-700/30">
                          <div className="flex items-center gap-3 mb-2">
                            <AchievementIcon className={`w-6 h-6 ${achievement.color}`} />
                            <h5 className="font-semibold text-white">{achievement.title}</h5>
                          </div>
                          <p className="text-sm text-gray-400">{achievement.description}</p>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Feedback */}
                <div className="mb-8">
                  <label className="block text-sm font-medium text-gray-300 mb-3">Комментарии и заметки</label>
                  <textarea
                    value={completionData.feedback}
                    onChange={(e) => setCompletionData(prev => ({ ...prev, feedback: e.target.value }))}
                    placeholder="Добавьте комментарии о результатах кампании..."
                    className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                    rows={4}
                  />
                </div>

                {/* Actions */}
                <div className="flex gap-4">
                  <button
                    onClick={() => setShowCompletionModal(false)}
                    className="flex-1 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl font-medium transition-colors"
                  >
                    Отмена
                  </button>
                  <button
                    onClick={handleComplete}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium transition-all"
                  >
                    Завершить кампанию
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CampaignStatusManager;
