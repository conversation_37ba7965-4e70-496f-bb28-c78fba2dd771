'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Send, <PERSON>rk<PERSON>, Users, Music, ArrowRight, Clock, DollarSign } from 'lucide-react';
import { AIService } from '@/services/aiService';

// Local interfaces for the component's data structures
interface LocalCampaignProposal {
  services: LocalServiceTemplate[];
  totalPrice: number;
  totalDuration: number;
  expectedResults: {
    reach: string;
    engagement: string;
    conversion: string;
  };
  analysis: string;
}

interface LocalServiceTemplate {
  $id: string;
  template_id?: string;
  category: 'artist' | 'track';
  service_type?: string;
  platform: string;
  name: string;
  description: string;
  icon?: string;
  color?: string;
  base_price: number;
  duration_days: number;
  features: string[];
  metrics?: any;
  requirements?: string[];
  active?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface LocalAIPrompt {
  $id: string;
  prompt_id: string;
  category: 'artist' | 'track';
  title: string;
  description: string;
  prompt_text: string;
  tags: string[];
  popularity: number;
  active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const AIPromoHelper = () => {
  const [activeCategory, setActiveCategory] = useState<'artist' | 'track'>('artist');
  const [userQuery, setUserQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [prompts, setPrompts] = useState<LocalAIPrompt[]>([]);
  const [proposal, setProposal] = useState<LocalCampaignProposal | null>(null);
  const [showProposal, setShowProposal] = useState(false);

  // Загружаем промпты при изменении категории
  useEffect(() => {
    loadPrompts();
  }, [activeCategory]);

  const loadPrompts = async () => {
    try {
      // Моковые данные для демонстрации
      const mockPrompts: LocalAIPrompt[] = activeCategory === 'artist' ? [
        {
          $id: '1',
          prompt_id: 'artist-new-release',
          category: 'artist',
          title: 'Продвижение нового релиза',
          description: 'Как эффективно продвинуть новый трек или альбом',
          prompt_text: 'Мне нужно продвинуть новый трек/альбом. Помоги составить стратегию продвижения.',
          tags: ['релиз', 'продвижение', 'стратегия'],
          popularity: 95
        },
        {
          $id: '2',
          prompt_id: 'artist-social-growth',
          category: 'artist',
          title: 'Рост в социальных сетях',
          description: 'Увеличение подписчиков и вовлеченности',
          prompt_text: 'Хочу увеличить количество подписчиков в Instagram, TikTok и YouTube. Какие сервисы посоветуешь?',
          tags: ['соцсети', 'подписчики', 'рост'],
          popularity: 88
        },
        {
          $id: '3',
          prompt_id: 'artist-brand-building',
          category: 'artist',
          title: 'Построение бренда артиста',
          description: 'Создание узнаваемого образа и стиля',
          prompt_text: 'Помоги создать сильный бренд артиста. Нужна стратегия для узнаваемости.',
          tags: ['бренд', 'имидж', 'узнаваемость'],
          popularity: 75
        }
      ] : [
        {
          $id: '4',
          prompt_id: 'track-viral-strategy',
          category: 'track',
          title: 'Вирусная стратегия для трека',
          description: 'Как сделать трек вирусным в TikTok и других платформах',
          prompt_text: 'Хочу сделать свой трек вирусным в TikTok и других соцсетях. Какую стратегию посоветуешь?',
          tags: ['вирус', 'tiktok', 'тренды'],
          popularity: 92
        },
        {
          $id: '5',
          prompt_id: 'track-streaming-boost',
          category: 'track',
          title: 'Увеличение прослушиваний',
          description: 'Рост стримов на Spotify, Apple Music, Яндекс.Музыка',
          prompt_text: 'Нужно увеличить количество прослушиваний трека на стриминговых платформах. Что делать?',
          tags: ['стриминг', 'spotify', 'прослушивания'],
          popularity: 85
        },
        {
          $id: '6',
          prompt_id: 'track-playlist-placement',
          category: 'track',
          title: 'Попадание в плейлисты',
          description: 'Размещение трека в популярных плейлистах',
          prompt_text: 'Как попасть в популярные плейлисты на Spotify и других платформах?',
          tags: ['плейлисты', 'кураторы', 'размещение'],
          popularity: 80
        }
      ];

      setPrompts(mockPrompts);
    } catch (error) {
      console.error('Error loading prompts:', error);
    }
  };

  const generateProposal = async (query: string) => {
    setIsLoading(true);

    try {
      // Симуляция AI анализа
      await new Promise(resolve => setTimeout(resolve, 2000));
      const mockServices: LocalServiceTemplate[] = activeCategory === 'artist' ? [
        {
          $id: '1',
          category: 'artist',
          platform: 'Instagram',
          name: 'Продвижение в Instagram',
          description: 'Органический рост подписчиков, лайков и комментариев в Instagram',
          base_price: 15000,
          duration_days: 30,
          features: [
            'Органический рост подписчиков',
            'Увеличение лайков и комментариев',
            'Создание контента'
          ]
        },
        {
          $id: '2',
          template_id: 'artist-tiktok-viral',
          category: 'artist',
          service_type: 'social_media',
          platform: 'TikTok',
          name: 'Вирусное продвижение в TikTok',
          description: 'Создание вирусного контента и продвижение в TikTok',
          icon: 'tiktok',
          color: '#000000',
          base_price: 20000,
          duration_days: 21,
          features: [
            'Создание вирусного контента',
            'Работа с трендами',
            'Коллаборации с тиктокерами',
            'Хештег-челленджи',
            'Продвижение через алгоритмы'
          ],
          metrics: {
            views: '+100K-1M',
            followers: '+1000-5000',
            viral_potential: '85%'
          },
          requirements: ['Музыкальный контент', 'Готовность к экспериментам']
        },
        {
          $id: '3',
          template_id: 'artist-youtube-channel',
          category: 'artist',
          service_type: 'content',
          platform: 'YouTube',
          name: 'Развитие YouTube канала',
          description: 'Комплексное развитие YouTube канала артиста',
          icon: 'youtube',
          color: '#FF0000',
          base_price: 35000,
          duration_days: 60,
          features: [
            'Оптимизация канала',
            'Создание видеоконтента',
            'SEO оптимизация',
            'Продвижение видео',
            'Монетизация канала'
          ],
          metrics: {
            subscribers: '+2000-10000',
            views: '+50K-500K',
            watch_time: '+200%'
          },
          requirements: ['Регулярный контент', 'Качественное видео']
        }
      ] : [
        {
          $id: '4',
          template_id: 'track-spotify-promotion',
          category: 'track',
          service_type: 'streaming',
          platform: 'Spotify',
          name: 'Продвижение на Spotify',
          description: 'Увеличение прослушиваний и попадание в плейлисты Spotify',
          icon: 'spotify',
          color: '#1DB954',
          base_price: 12000,
          duration_days: 14,
          features: [
            'Размещение в плейлистах',
            'Органические прослушивания',
            'Работа с кураторами',
            'Spotify for Artists оптимизация',
            'Аналитика и отчеты'
          ],
          metrics: {
            streams: '+10K-100K',
            monthly_listeners: '+500-5000',
            playlist_adds: '10-50'
          },
          requirements: ['Трек на Spotify', 'Качественная музыка']
        },
        {
          $id: '5',
          template_id: 'track-tiktok-trend',
          category: 'track',
          service_type: 'social_media',
          platform: 'TikTok',
          name: 'Создание тренда в TikTok',
          description: 'Превращение трека в TikTok тренд с помощью челленджей',
          icon: 'tiktok',
          color: '#000000',
          base_price: 25000,
          duration_days: 21,
          features: [
            'Создание челленджа',
            'Работа с инфлюенсерами',
            'Вирусные видео',
            'Хештег кампании',
            'Мониторинг трендов'
          ],
          metrics: {
            challenge_views: '+1M-10M',
            user_videos: '+1000-10000',
            viral_coefficient: '90%'
          },
          requirements: ['Запоминающийся отрывок', 'Танцевальный потенциал']
        },
        {
          $id: '6',
          template_id: 'track-radio-campaign',
          category: 'track',
          service_type: 'pr',
          platform: 'Radio',
          name: 'Радиокампания',
          description: 'Продвижение трека на радиостанциях',
          icon: 'radio',
          color: '#FF6B35',
          base_price: 40000,
          duration_days: 30,
          features: [
            'Размещение на радиостанциях',
            'Интервью с артистом',
            'Ротация в эфире',
            'Региональное покрытие',
            'Отчеты по эфирам'
          ],
          metrics: {
            radio_stations: '20-50',
            airplay_hours: '100-500',
            audience_reach: '500K-2M'
          },
          requirements: ['Радиоформат трека', 'Пресс-кит артиста']
        }
      ];

      const totalPrice = mockServices.reduce((sum, service) => sum + service.base_price, 0);
      const maxDuration = Math.max(...mockServices.map(service => service.duration_days));

      const mockProposal: LocalCampaignProposal = {
        services: mockServices,
        totalPrice,
        totalDuration: maxDuration,
        expectedResults: {
          reach: activeCategory === 'artist' ? '500K-2M' : '1M-10M',
          engagement: '+150-300%',
          conversion: '5-15%'
        },
        analysis: `На основе вашего запроса "${query}" я рекомендую комплексную стратегию ${activeCategory === 'artist' ? 'продвижения артиста' : 'продвижения трека'}. Эта кампания охватит основные каналы и обеспечит максимальный охват вашей целевой аудитории.`
      };

      setProposal(mockProposal);
      setShowProposal(true);
    } catch (error) {
      console.error('Error generating proposal:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePromptClick = (prompt: LocalAIPrompt) => {
    setUserQuery(prompt.prompt_text);
    generateProposal(prompt.prompt_text);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userQuery.trim()) {
      
      generateProposal(userQuery);
    }
  };

  const formatPrice = (price: number) => {
    return AIService.formatPrice(price);
  };

  const getPlatformIcon = (platform: string) => {
    return AIService.getPlatformIcon(platform);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              AI-Помощник <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">HIVE</span>
            </h2>
            <Sparkles className="w-8 h-8 text-yellow-400 animate-pulse" />
          </div>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            Умный помощник для продвижения музыки. Опишите свои цели, и я подберу оптимальную стратегию с расчетом стоимости и времени.
          </p>

          {/* Category Switcher */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800/50 rounded-2xl p-2 backdrop-blur-xl border border-gray-700/50">
              <button
                onClick={() => setActiveCategory('artist')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeCategory === 'artist'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Users className="w-5 h-5 inline-block mr-2" />
                Продвижение артистов
              </button>
              <button
                onClick={() => setActiveCategory('track')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeCategory === 'track'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Music className="w-5 h-5 inline-block mr-2" />
                Продвижение треков
              </button>
            </div>
          </div>
        </div>

        {!showProposal ? (
          <div className="max-w-4xl mx-auto">
            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="mb-12">
              <div className="relative">
                <textarea
                  value={userQuery}
                  onChange={(e) => setUserQuery(e.target.value)}
                  placeholder={`Опишите ваши цели по продвижению ${activeCategory === 'artist' ? 'артиста' : 'трека'}...`}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-2xl px-6 py-4 text-white placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500 transition-colors"
                  rows={4}
                />
                <button
                  type="submit"
                  disabled={!userQuery.trim() || isLoading}
                  className="absolute bottom-4 right-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-xl hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
            </form>

            {/* Quick Prompts */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-6 text-center">
                Или выберите готовый запрос:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {prompts.map((prompt) => (
                  <div
                    key={prompt.$id}
                    onClick={() => handlePromptClick(prompt)}
                    className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 cursor-pointer hover:border-blue-500 hover:bg-gray-800/70 transition-all duration-300 group"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">
                        {prompt.title}
                      </h4>
                      <div className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">
                        {prompt.popularity}%
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">
                      {prompt.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {prompt.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded-full"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // Proposal Display
          proposal && (
            <div className="max-w-6xl mx-auto">
              {/* AI Analysis */}
              <div className="bg-gradient-to-r from-blue-500/10 to-purple-600/10 border border-blue-500/30 rounded-2xl p-8 mb-8">
                <div className="flex items-center gap-3 mb-4">
                  <Bot className="w-6 h-6 text-blue-400" />
                  <h3 className="text-xl font-bold text-white">Анализ AI-помощника</h3>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  {proposal.analysis}
                </p>
              </div>

              {/* Services Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {proposal.services.map((service, index) => (
                  <div
                    key={service.$id}
                    className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 hover:border-blue-500 transition-all duration-300 transform hover:scale-105 animate-fadeInUp"
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <div
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl"
                        style={{ backgroundColor: service.color + '20' }}
                      >
                        {getPlatformIcon(service.platform)}
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-white">{service.name}</h4>
                        <p className="text-sm text-gray-400">{service.platform}</p>
                      </div>
                    </div>

                    <p className="text-gray-300 text-sm mb-4">{service.description}</p>

                    <div className="space-y-2 mb-4">
                      {service.features.slice(0, 3).map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2 text-sm text-gray-400">
                          <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                          {feature}
                        </div>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                      <div className="flex items-center gap-2 text-sm text-gray-400">
                        <Clock className="w-4 h-4" />
                        {service.duration_days} дней
                      </div>
                      <div className="text-lg font-bold text-white">
                        {formatPrice(service.base_price)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Summary */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <DollarSign className="w-6 h-6 text-green-400" />
                      <h4 className="text-lg font-semibold text-white">Общая стоимость</h4>
                    </div>
                    <p className="text-3xl font-bold text-green-400">
                      {formatPrice(proposal.totalPrice)}
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Clock className="w-6 h-6 text-blue-400" />
                      <h4 className="text-lg font-semibold text-white">Время кампании</h4>
                    </div>
                    <p className="text-3xl font-bold text-blue-400">
                      {proposal.totalDuration} дней
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Sparkles className="w-6 h-6 text-purple-400" />
                      <h4 className="text-lg font-semibold text-white">Ожидаемый охват</h4>
                    </div>
                    <p className="text-3xl font-bold text-purple-400">
                      {proposal.expectedResults.reach}
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => setShowProposal(false)}
                    className="px-8 py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-colors"
                  >
                    Изменить запрос
                  </button>
                  <button
                    onClick={() => {
                      // Здесь будет логика создания заказа
                      alert('Предложение добавлено в дашборд! Менеджер свяжется с вами в ближайшее время.');
                    }}
                    className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl transition-all duration-300 flex items-center gap-2 justify-center"
                  >
                    Заказать кампанию
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </section>
  );
};

export default AIPromoHelper;
