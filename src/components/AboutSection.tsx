'use client';

import React, { useState } from 'react';
import { Target, Users, TrendingUp, Zap, Radio, Tv, Share2, Mail, Music, Clock, Rocket } from 'lucide-react';

const AboutSection = () => {
  const [language] = useState('ru'); // This would come from context in real app

  const features = [
    {
      icon: <Radio className="text-4xl text-red-500 animate-pulse" />,
      title: language === 'ru' ? 'Радио продвижение' : 'Radio Promotion',
      description: language === 'ru'
        ? 'Размещение на топ радиостанциях России и СНГ'
        : 'Placement on top radio stations in Russia and CIS',
    },
    {
      icon: <Tv className="text-4xl text-red-500 animate-float" />,
      title: language === 'ru' ? 'ТВ реклама' : 'TV Advertising',
      description: language === 'ru'
        ? 'Реклама на федеральных и региональных каналах'
        : 'Advertising on federal and regional channels',
    },
    {
      icon: <Share2 className="text-4xl text-red-500 animate-glow" />,
      title: language === 'ru' ? 'Социальные сети' : 'Social Media',
      description: language === 'ru'
        ? 'Продвижение в Instagram, TikTok, YouTube'
        : 'Promotion on Instagram, TikTok, YouTube',
    },
    {
      icon: <Mail className="text-4xl text-red-500 animate-pulse" />,
      title: language === 'ru' ? 'Email маркетинг' : 'Email Marketing',
      description: language === 'ru'
        ? 'Рассылки по базе артистов и лейблов'
        : 'Mailings to artists and labels database',
    },
  ];

  const stats = [
    {
      number: '500+',
      label: language === 'ru' ? 'Успешных проектов' : 'Successful Projects',
    },
    {
      number: '50M+',
      label: language === 'ru' ? 'Прослушиваний' : 'Total Plays',
    },
    {
      number: '100+',
      label: language === 'ru' ? 'Радиостанций' : 'Radio Stations',
    },
    {
      number: '24/7',
      label: language === 'ru' ? 'Поддержка' : 'Support',
    },
  ];

  return (
    <section id="about" className="py-16 md:py-32 bg-gray-900">
      <div className="container-wide">
        {/* Header */}
        <div className="text-center mb-16 md:mb-32">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-8 md:mb-12">
            {language === 'ru' ? 'О нас' : 'About Us'}
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            {language === 'ru'
              ? 'H!VE Agency — ведущее агентство по продвижению артистов и треков в России и СНГ. Мы специализируемся на комплексном продвижении музыки через все доступные каналы.'
              : 'H!VE Agency is a leading artist and track promotion agency in Russia and CIS. We specialize in comprehensive music promotion through all available channels.'
            }
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-30 mb-20 md:mb-40">
          {stats.map((stat, index) => {
            const icons = [TrendingUp, Users, Radio, Clock];
            const IconComponent = icons[index];
            return (
              <div key={index} className="text-center group">
                <div className="card-monochrome card-padding rounded-2xl p-6 md:p-8">
                  <div className="relative z-10">
                    <div className="mb-8 flex justify-center">
                      <div className="p-4 rounded-xl bg-gray-800/50 border border-gray-700/50 group-hover:border-red-500/30 transition-colors duration-300">
                        <IconComponent className="w-8 h-8 md:w-12 md:h-12 text-gray-400 group-hover:text-red-400 transition-colors duration-300" />
                      </div>
                    </div>
                    <div className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-6 text-center">
                      {stat.number}
                    </div>
                    <div className="text-gray-400 font-medium text-sm md:text-lg group-hover:text-gray-300 transition-colors duration-300 text-center">
                      {stat.label}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-30 mb-16 md:mb-32">
          {features.map((feature, index) => (
            <div
              key={index}
              className="card-monochrome card-padding-lg rounded-3xl group p-6 md:p-8"
            >
              <div className="relative z-10">
                <div className="mb-10 text-center">
                  <div className="inline-block p-4 rounded-2xl bg-gray-800/50 border border-gray-700/50 group-hover:border-red-500/30 transition-all duration-300">
                    <div className="text-gray-400 group-hover:text-red-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                </div>
                <h3 className="text-xl md:text-2xl font-bold text-white mb-8 text-center heading-md group-hover:text-red-50 transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-400 leading-relaxed text-center text-body group-hover:text-gray-300 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Mission Statement */}
        <div className="holographic p-2 rounded-3xl">
          <div className="bg-black/90 p-12 md:p-16 rounded-3xl border border-red-500/30">
            <div className="max-w-5xl mx-auto text-center">
              <div className="flex items-center justify-center gap-6 mb-12">
                <Rocket className="text-red-500 w-12 h-12 animate-float" />
                <Music className="text-6xl text-red-500 animate-float pulse-ring" />
                <Target className="text-red-500 w-12 h-12 animate-float" style={{ animationDelay: '1s' }} />
              </div>
              <h3 className="text-4xl md:text-6xl font-bold text-white mb-8 heading-lg gradient-text">
                {language === 'ru' ? 'Наша миссия' : 'Our Mission'}
              </h3>
              <p className="text-2xl text-gray-300 leading-relaxed mb-12 text-body">
                {language === 'ru'
                  ? 'Мы помогаем талантливым артистам достичь максимальной аудитории и коммерческого успеха. Наша команда профессионалов использует передовые технологии и проверенные стратегии для продвижения вашей музыки.'
                  : 'We help talented artists reach maximum audience and commercial success. Our team of professionals uses cutting-edge technologies and proven strategies to promote your music.'
                }
              </p>
              <button className="btn-primary text-xl px-12 py-6 inline-flex items-center gap-4 group">
                <Zap className="w-6 h-6 group-hover:animate-pulse" />
                {language === 'ru' ? 'Начать сотрудничество' : 'Start Collaboration'}
                <Rocket className="w-6 h-6 group-hover:animate-float" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
