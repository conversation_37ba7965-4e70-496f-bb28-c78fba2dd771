'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Headphones, Sparkles, Zap, Users, CreditCard, MessageCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';

const Navigation = () => {
  const { user, login } = useAuth();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'AI Агент', href: '#ai-agent', icon: Headphones },
    { name: 'Как работает', href: '#how-it-works', icon: Sparkles },
    { name: 'Тарифы', href: '#pricing', icon: CreditCard },
    { name: 'Отзывы', href: '#reviews', icon: Users },
  ];

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
      className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/10 transition-all duration-300"
    >
      <div className="container-hive">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center gap-3"
          >
            <div className="relative">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center"
              >
                <Headphones className="w-6 h-6 text-white" />
              </motion.div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                <Sparkles className="w-2 h-2 text-white" />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold gradient-text">H!VE</h1>
              <p className="text-xs text-gray-400 -mt-1">AI Agency</p>
            </div>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            {navItems.map((item, index) => (
              <motion.a
                key={item.name}
                href={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors duration-200 group"
              >
                <item.icon className="w-4 h-4 text-purple-400 group-hover:text-pink-400 transition-colors duration-200" />
                {item.name}
              </motion.a>
            ))}
          </nav>

          {/* Auth Section */}
          <div className="hidden md:flex items-center gap-4">
            {user ? (
              <div className="flex items-center gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => router.push('/dashboard')}
                  className="flex items-center gap-3 bg-black/20 backdrop-blur-xl rounded-2xl px-4 py-2 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 cursor-pointer"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white font-medium">{user.name}</span>
                </motion.button>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Button variant="ghost" size="sm" onClick={() => router.push('/login')}>
                  Войти
                </Button>
                <Button variant="primary" size="sm" onClick={login}>
                  Начать бесплатно
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center text-white"
          >
            {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-black/90 backdrop-blur-xl border-t border-purple-500/20"
          >
            <div className="container-hive py-6">
              <nav className="space-y-4">
                {navItems.map((item, index) => (
                  <motion.a
                    key={item.name}
                    href={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors duration-200 py-3 px-4 rounded-xl hover:bg-purple-500/10"
                  >
                    <item.icon className="w-5 h-5 text-purple-400" />
                    {item.name}
                  </motion.a>
                ))}
              </nav>

              <div className="mt-6 pt-6 border-t border-purple-500/20">
                {user ? (
                  <div className="space-y-4">
                    <motion.button
                      whileTap={{ scale: 0.95 }}
                      onClick={() => {
                        router.push('/dashboard');
                        setIsMenuOpen(false);
                      }}
                      className="w-full flex items-center gap-3 bg-black/20 backdrop-blur-xl rounded-2xl px-4 py-3 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300"
                    >
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-white font-medium">{user.name}</span>
                    </motion.button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Button variant="ghost" size="md" className="w-full" onClick={() => {
                      router.push('/login');
                      setIsMenuOpen(false);
                    }}>
                      Войти
                    </Button>
                    <Button variant="primary" size="md" className="w-full" onClick={() => {
                      login();
                      setIsMenuOpen(false);
                    }}>
                      Начать бесплатно
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Navigation;
