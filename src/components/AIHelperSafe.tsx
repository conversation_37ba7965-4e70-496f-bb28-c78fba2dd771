'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Используем next/dynamic с ssr: false согласно официальной документации Next.js
// Это отключает серверный рендеринг для проблемных компонентов
const AIHelperClient = dynamic(() => import('./AIHelperClient'), {
  ssr: false,
  loading: () => (
    <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
      <div className="container mx-auto px-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-white mt-4">Загрузка AI-помощника...</p>
        </div>
      </div>
    </section>
  )
});

// Создаем безопасную обертку для AI Helper
const AIHelperSafe = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Возвращаем loading на сервере
  if (!isClient) {
    return (
      <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="container mx-auto px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-white mt-4">Загрузка AI-помощника...</p>
          </div>
        </div>
      </section>
    );
  }

  return <AIHelperClient />;
};

export default AIHelperSafe;
