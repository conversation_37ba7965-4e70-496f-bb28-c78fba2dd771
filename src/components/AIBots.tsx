'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bo<PERSON>,
  Play,
  Pause,
  Settings,
  TrendingUp,
  Users,
  Music,
  Radio,
  Instagram,
  Youtube,
  Zap,
  Target,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign
} from 'lucide-react';

interface AIBot {
  id: string;
  name: string;
  type: 'spotify' | 'youtube' | 'instagram' | 'radio';
  description: string;
  features: string[];
  isActive: boolean;
  isConnected: boolean;
  stats: {
    totalActions: number;
    successRate: number;
    dailyReach: number;
    cost: number;
  };
  icon: any;
  color: string;
  platforms: string[];
}

const aiBotsData: AIBot[] = [
  {
    id: 'spotify-bot',
    name: 'Spotify Promotion Bot',
    type: 'spotify',
    description: 'Автоматическое продвижение треков в Spotify через плейлисты, алгоритмы и органический рост. Использует машинное обучение для анализа 50,000+ плейлистов и автоматически отправляет персонализированные питчи кураторам.',
    features: [
      'Автоматическое добавление в 2000+ плейлистов',
      'ML оптимизация под алгоритм Spotify Discovery',
      'Таргетинг по 150+ жанрам и настроениям',
      'Анализ конкурентов и копирование стратегий',
      'Автоматические релизы и питчинг с 87% успешностью'
    ],
    isActive: false,
    isConnected: false,
    stats: {
      totalActions: 15420,
      successRate: 87,
      dailyReach: 25000,
      cost: 2500
    },
    icon: Music,
    color: 'green',
    platforms: ['Spotify', 'Apple Music', 'Deezer']
  },
  {
    id: 'youtube-bot',
    name: 'YouTube Growth Bot',
    type: 'youtube',
    description: 'Комплексное продвижение на YouTube с использованием Computer Vision и TensorFlow. Автоматически создает вирусный контент, оптимизирует SEO и взаимодействует с 10,000+ каналов ежедневно.',
    features: [
      'AI SEO оптимизация с 92% эффективностью',
      'Автоматические взаимодействия (50,000+ в день)',
      'Анализ трендов с предсказанием вирусности',
      'Кросс-промо с 5,000+ партнерских каналов',
      'Автоматическое создание YouTube Shorts с AI'
    ],
    isActive: false,
    isConnected: false,
    stats: {
      totalActions: 8750,
      successRate: 92,
      dailyReach: 45000,
      cost: 3200
    },
    icon: Youtube,
    color: 'red',
    platforms: ['YouTube', 'YouTube Music', 'YouTube Shorts']
  },
  {
    id: 'instagram-bot',
    name: 'Instagram Engagement Bot',
    type: 'instagram',
    description: 'Умное продвижение в Instagram с использованием Image Recognition и Sentiment Analysis. Автоматически создает вирусный контент и взаимодействует с 25,000+ аккаунтов целевой аудитории.',
    features: [
      'AI создание Stories и Reels (100+ в день)',
      'Таргетированные взаимодействия (15,000+ в день)',
      'ML анализ оптимального времени публикации',
      'Автоматическая оптимизация хештегов',
      'Коллаборации с 500+ микро-инфлюенсерами'
    ],
    isActive: false,
    isConnected: false,
    stats: {
      totalActions: 12300,
      successRate: 89,
      dailyReach: 35000,
      cost: 2800
    },
    icon: Instagram,
    color: 'pink',
    platforms: ['Instagram', 'Instagram Reels', 'Instagram Stories']
  },
  {
    id: 'radio-bot',
    name: 'Radio Promotion Bot',
    type: 'radio',
    description: 'Автоматическое продвижение на 2000+ радиостанциях и подкастах по всему миру. Использует Audio Fingerprinting и Broadcast Monitoring для отслеживания эфирного времени в реальном времени.',
    features: [
      'Автоматический питчинг на 2000+ радиостанций',
      'AI анализ радиочартов и предсказание ротации',
      'Размещение в 500+ музыкальных подкастах',
      'Геотаргетинг по 45+ странам',
      'Реальный мониторинг эфира 24/7'
    ],
    isActive: false,
    isConnected: false,
    stats: {
      totalActions: 5680,
      successRate: 76,
      dailyReach: 180000,
      cost: 4500
    },
    icon: Radio,
    color: 'blue',
    platforms: ['FM Radio', 'Internet Radio', 'Podcasts']
  }
];

interface AIBotsProps {
  onBotToggle?: (botId: string, isActive: boolean) => void;
}

export default function AIBots({ onBotToggle }: AIBotsProps) {
  const [bots, setBots] = useState<AIBot[]>(aiBotsData);
  const [selectedBot, setSelectedBot] = useState<string | null>(null);

  const handleBotToggle = (botId: string) => {
    setBots(prev => prev.map(bot => {
      if (bot.id === botId) {
        const newActiveState = !bot.isActive;
        onBotToggle?.(botId, newActiveState);
        return { ...bot, isActive: newActiveState };
      }
      return bot;
    }));
  };

  const handleConnect = (botId: string) => {
    setBots(prev => prev.map(bot => 
      bot.id === botId ? { ...bot, isConnected: !bot.isConnected } : bot
    ));
  };

  const getColorClasses = (color: string) => {
    const colors = {
      green: {
        bg: 'from-green-500/20 to-emerald-500/20',
        border: 'border-green-500/30',
        text: 'text-green-400',
        button: 'from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
      },
      red: {
        bg: 'from-red-500/20 to-pink-500/20',
        border: 'border-red-500/30',
        text: 'text-red-400',
        button: 'from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600'
      },
      pink: {
        bg: 'from-pink-500/20 to-purple-500/20',
        border: 'border-pink-500/30',
        text: 'text-pink-400',
        button: 'from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600'
      },
      blue: {
        bg: 'from-blue-500/20 to-cyan-500/20',
        border: 'border-blue-500/30',
        text: 'text-blue-400',
        button: 'from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600'
      }
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
          AI Боты для автоматизации
        </h2>
        <p className="text-gray-400 max-w-3xl mx-auto mb-6">
          Подключите умных ботов для автоматического продвижения ваших треков на всех платформах.
          Каждый бот использует передовые AI алгоритмы для максимального результата.
        </p>

        {/* Info Panel */}
        <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-xl border border-blue-500/30 p-6 max-w-4xl mx-auto">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center justify-center gap-2">
            <Zap className="w-6 h-6 text-blue-400" />
            Как работают AI боты
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
            <div>
              <h4 className="text-blue-400 font-semibold mb-2">🎯 Автоматизация</h4>
              <p className="text-gray-300 text-sm">
                Боты работают 24/7, выполняя тысячи действий ежедневно: лайки, комментарии,
                добавления в плейлисты, отправка треков на радио.
              </p>
            </div>
            <div>
              <h4 className="text-purple-400 font-semibold mb-2">🧠 Машинное обучение</h4>
              <p className="text-gray-300 text-sm">
                Используют ML для анализа трендов, оптимизации контента и предсказания
                вирусности. Постоянно учатся на результатах.
              </p>
            </div>
            <div>
              <h4 className="text-pink-400 font-semibold mb-2">🛡️ Безопасность</h4>
              <p className="text-gray-300 text-sm">
                Имитируют естественное поведение, используют proxy-серверы и соблюдают
                все правила платформ для защиты от блокировок.
              </p>
            </div>
            <div>
              <h4 className="text-green-400 font-semibold mb-2">📊 Аналитика</h4>
              <p className="text-gray-300 text-sm">
                Предоставляют детальную статистику в реальном времени: охват,
                конверсии, ROI, прогнозы роста.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {bots.map((bot, index) => {
          const colors = getColorClasses(bot.color);
          
          return (
            <motion.div
              key={bot.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`bg-gradient-to-br ${colors.bg} backdrop-blur-xl rounded-2xl border ${colors.border} p-6 hover:scale-105 transition-all duration-300`}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 bg-gradient-to-r ${colors.button} rounded-xl flex items-center justify-center shadow-lg`}>
                    <bot.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-lg">{bot.name}</h3>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${bot.isConnected ? 'bg-green-400' : 'bg-gray-400'}`} />
                      <span className="text-sm text-gray-400">
                        {bot.isConnected ? 'Подключен' : 'Не подключен'}
                      </span>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => handleBotToggle(bot.id)}
                  disabled={!bot.isConnected}
                  className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all ${
                    bot.isActive 
                      ? `bg-gradient-to-r ${colors.button} shadow-lg` 
                      : 'bg-gray-700 hover:bg-gray-600'
                  } ${!bot.isConnected ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {bot.isActive ? (
                    <Pause className="w-5 h-5 text-white" />
                  ) : (
                    <Play className="w-5 h-5 text-white" />
                  )}
                </button>
              </div>

              {/* Description */}
              <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                {bot.description}
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className={`text-xl font-bold ${colors.text}`}>
                    {bot.stats.dailyReach.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-400">Охват/день</div>
                </div>
                <div className="text-center">
                  <div className={`text-xl font-bold ${colors.text}`}>
                    {bot.stats.successRate}%
                  </div>
                  <div className="text-xs text-gray-400">Успешность</div>
                </div>
              </div>

              {/* Platforms */}
              <div className="mb-4">
                <div className="text-xs text-gray-400 mb-2">Платформы:</div>
                <div className="flex flex-wrap gap-1">
                  {bot.platforms.map((platform) => (
                    <span
                      key={platform}
                      className="text-xs px-2 py-1 bg-black/30 rounded-full text-gray-300"
                    >
                      {platform}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleConnect(bot.id)}
                  className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all ${
                    bot.isConnected
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : `bg-gradient-to-r ${colors.button} text-white shadow-lg`
                  }`}
                >
                  {bot.isConnected ? 'Отключить' : 'Подключить'}
                </button>
                
                <button
                  onClick={() => setSelectedBot(selectedBot === bot.id ? null : bot.id)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                >
                  <Settings className="w-4 h-4 text-gray-300" />
                </button>
              </div>

              {/* Expanded Details */}
              <AnimatePresence>
                {selectedBot === bot.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 pt-4 border-t border-gray-700/50"
                  >
                    <h4 className="text-white font-semibold mb-2">Возможности:</h4>
                    <ul className="space-y-1">
                      {bot.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-2 text-sm text-gray-300">
                          <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <div className="mt-4 p-3 bg-black/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400">Стоимость в месяц:</span>
                        <span className={`font-bold ${colors.text}`}>
                          {bot.stats.cost.toLocaleString()} ₽
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </div>

      {/* Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-2xl border border-purple-500/30 p-6"
      >
        <h3 className="text-xl font-bold text-white mb-4">Общая статистика</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              {bots.filter(bot => bot.isActive).length}
            </div>
            <div className="text-sm text-gray-400">Активных ботов</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {bots.reduce((sum, bot) => sum + (bot.isActive ? bot.stats.dailyReach : 0), 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Общий охват</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {Math.round(bots.reduce((sum, bot) => sum + (bot.isActive ? bot.stats.successRate : 0), 0) / Math.max(bots.filter(bot => bot.isActive).length, 1))}%
            </div>
            <div className="text-sm text-gray-400">Средняя успешность</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-400">
              {bots.reduce((sum, bot) => sum + (bot.isActive ? bot.stats.cost : 0), 0).toLocaleString()} ₽
            </div>
            <div className="text-sm text-gray-400">Общая стоимость</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
