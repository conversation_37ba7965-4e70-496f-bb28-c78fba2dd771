'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import Link from 'next/link';
import { Shield, Crown, AlertCircle, Loader2 } from 'lucide-react';

interface AdminRouteProps {
  children: ReactNode;
  requiredPermission?: 'canAccessAdminPanel' | 'canManageOrders' | 'canManageUsers' | 'canViewAnalytics' | 'canManageSettings';
  fallback?: ReactNode;
}

export default function AdminRoute({ 
  children, 
  requiredPermission = 'canAccessAdminPanel',
  fallback 
}: AdminRouteProps) {
  const { user, loading } = useAuth();
  const permissions = useAdminAuth();

  // Показываем загрузку пока проверяем авторизацию
  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-purple-500 mx-auto mb-4 animate-spin" />
          <p className="text-gray-400">Проверка прав доступа...</p>
        </div>
      </div>
    );
  }

  // Если пользователь не авторизован
  if (!user) {
    return fallback || (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto" style={{ padding: '30px' }}>
          <Shield className="w-16 h-16 text-purple-500 mx-auto mb-6" />
          
          <h1 className="text-3xl font-bold text-white mb-4">
            Требуется авторизация
          </h1>
          
          <p className="text-gray-400 mb-8" style={{ lineHeight: '1.6' }}>
            Для доступа к панели администратора необходимо войти в систему
          </p>
          
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link
              href="/auth/login"
              className="bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors font-semibold"
              style={{ padding: '12px 24px' }}
            >
              Войти в систему
            </Link>
            
            <Link
              href="/auth/create-admin"
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 24px' }}
            >
              Создать админа
            </Link>
          </div>
          
          <div className="mt-8 pt-6 border-t border-gray-800">
            <Link 
              href="/" 
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              ← Вернуться на главную
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Если у пользователя нет необходимых прав
  if (!permissions[requiredPermission]) {
    return fallback || (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto" style={{ padding: '30px' }}>
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-6" />
          
          <h1 className="text-3xl font-bold text-white mb-4">
            Доступ запрещен
          </h1>
          
          <p className="text-gray-400 mb-6" style={{ lineHeight: '1.6' }}>
            У вас нет прав для доступа к этому разделу панели администратора
          </p>
          
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg mb-8" style={{ padding: '16px' }}>
            <div className="text-left">
              <p className="text-red-400 text-sm mb-2">
                <strong>Ваша роль:</strong> {user.type || 'Не определена'}
              </p>
              <p className="text-red-400 text-sm">
                <strong>Требуется:</strong> Права администратора
              </p>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link
              href="/dashboard"
              className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors font-semibold"
              style={{ padding: '12px 24px' }}
            >
              В личный кабинет
            </Link>
            
            <Link
              href="/auth/create-admin"
              className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors font-medium"
              style={{ padding: '12px 24px' }}
            >
              Создать админа
            </Link>
          </div>
          
          <div className="mt-8 pt-6 border-t border-gray-800">
            <Link 
              href="/" 
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              ← Вернуться на главную
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Если все проверки пройдены, показываем контент
  return <>{children}</>;
}

// Компонент для отображения информации о правах пользователя
export function AdminUserInfo() {
  const { user } = useAuth();
  const permissions = useAdminAuth();

  if (!user) return null;

  return (
    <div className="bg-gray-800/50 rounded-lg" style={{ padding: '16px' }}>
      <div className="flex items-center mb-3" style={{ gap: '8px' }}>
        <Crown className="w-5 h-5 text-purple-400" />
        <h3 className="text-white font-semibold">Права администратора</h3>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm">Доступ к админ панели</span>
          <span className={`text-sm ${permissions.canAccessAdminPanel ? 'text-green-400' : 'text-red-400'}`}>
            {permissions.canAccessAdminPanel ? '✓' : '✗'}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm">Управление заказами</span>
          <span className={`text-sm ${permissions.canManageOrders ? 'text-green-400' : 'text-red-400'}`}>
            {permissions.canManageOrders ? '✓' : '✗'}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm">Управление пользователями</span>
          <span className={`text-sm ${permissions.canManageUsers ? 'text-green-400' : 'text-red-400'}`}>
            {permissions.canManageUsers ? '✓' : '✗'}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm">Просмотр аналитики</span>
          <span className={`text-sm ${permissions.canViewAnalytics ? 'text-green-400' : 'text-red-400'}`}>
            {permissions.canViewAnalytics ? '✓' : '✗'}
          </span>
        </div>
      </div>
      
      <div className="mt-4 pt-3 border-t border-gray-700">
        <p className="text-xs text-gray-500">
          Роль: <span className="text-purple-400">{user.type || 'Не определена'}</span>
        </p>
      </div>
    </div>
  );
}
