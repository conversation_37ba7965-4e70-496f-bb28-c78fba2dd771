'use client';

import { useState, useEffect } from 'react';
import { 
  Package, 
  User, 
  Music, 
  Calendar, 
  DollarSign,
  FileText,
  Upload,
  X,
  Plus,
  Trash2,
  Save,
  Send,
  Mic,
  Radio,
  Instagram,
  Youtube,
  Globe,
  Building,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Eye,
  MessageSquare
} from 'lucide-react';
import MediaManager from '@/components/MediaManager';

interface Order {
  id?: string;
  type: 'artist' | 'track';
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  serviceName: string;
  packageType: string;
  status: 'draft' | 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  price: number;
  currency: string;
  progress: number;
  createdAt?: string;
  dueDate?: string;
  assignedTo?: string;
  description?: string;
  requirements?: Record<string, any>;
  attachments?: string[];
  notes?: string[];
  tags?: string[];
}

interface OrderEditorProps {
  isOpen: boolean;
  onClose: () => void;
  order?: Order;
  onSave?: (order: Order) => void;
  mode?: 'create' | 'edit' | 'view';
}

const OrderEditor: React.FC<OrderEditorProps> = ({
  isOpen,
  onClose,
  order,
  onSave,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<Order>({
    type: 'track',
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    serviceName: '',
    packageType: '',
    status: 'draft',
    priority: 'medium',
    price: 0,
    currency: 'RUB',
    progress: 0,
    description: '',
    requirements: {},
    attachments: [],
    notes: [],
    tags: []
  });

  const [activeTab, setActiveTab] = useState('general');
  const [showMediaManager, setShowMediaManager] = useState(false);
  const [newNote, setNewNote] = useState('');
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    if (order) {
      setFormData(order);
    }
  }, [order]);

  const serviceOptions = {
    artist: [
      { value: 'instagram-promotion', label: 'Продвижение в Instagram', price: 15000 },
      { value: 'youtube-channel', label: 'Развитие YouTube канала', price: 25000 },
      { value: 'radio-promotion', label: 'Размещение на радио', price: 35000 },
      { value: 'pr-campaign', label: 'PR кампания', price: 50000 },
      { value: 'full-promotion', label: 'Полное продвижение', price: 100000 }
    ],
    track: [
      { value: 'basic-promotion', label: 'Базовое продвижение', price: 10000 },
      { value: 'social-media', label: 'Продвижение в соцсетях', price: 20000 },
      { value: 'playlist-placement', label: 'Размещение в плейлистах', price: 15000 },
      { value: 'radio-rotation', label: 'Радио ротация', price: 30000 },
      { value: 'premium-package', label: 'Премиум пакет', price: 50000 }
    ]
  };

  const statusOptions = [
    { value: 'draft', label: 'Черновик', color: 'gray' },
    { value: 'pending', label: 'Ожидание', color: 'yellow' },
    { value: 'confirmed', label: 'Подтвержден', color: 'blue' },
    { value: 'in_progress', label: 'В работе', color: 'purple' },
    { value: 'completed', label: 'Выполнен', color: 'green' },
    { value: 'cancelled', label: 'Отменен', color: 'red' }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Низкий', color: 'green' },
    { value: 'medium', label: 'Средний', color: 'yellow' },
    { value: 'high', label: 'Высокий', color: 'red' }
  ];

  const handleInputChange = (field: keyof Order, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleServiceChange = (serviceValue: string) => {
    const services = serviceOptions[formData.type];
    const selectedService = services.find(s => s.value === serviceValue);
    
    if (selectedService) {
      setFormData(prev => ({
        ...prev,
        serviceName: selectedService.label,
        packageType: serviceValue,
        price: selectedService.price
      }));
    }
  };

  const handleAddNote = () => {
    if (newNote.trim()) {
      setFormData(prev => ({
        ...prev,
        notes: [...(prev.notes || []), `${new Date().toLocaleString()}: ${newNote}`]
      }));
      setNewNote('');
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(formData);
    }
    onClose();
  };

  const isReadOnly = mode === 'view';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
      <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-800" style={{ padding: '20px 30px' }}>
          <div>
            <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
              {mode === 'create' ? 'Создать заказ' : mode === 'edit' ? 'Редактировать заказ' : 'Просмотр заказа'}
            </h2>
            {order?.id && (
              <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                ID: {order.id}
              </p>
            )}
          </div>
          <div className="flex items-center" style={{ gap: '15px' }}>
            {!isReadOnly && (
              <>
                <button
                  onClick={handleSave}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
                  style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                >
                  <Save className="w-4 h-4" />
                  Сохранить
                </button>
                {mode === 'edit' && (
                  <button
                    className="bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors flex items-center"
                    style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                  >
                    <Send className="w-4 h-4" />
                    Отправить
                  </button>
                )}
              </>
            )}
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-800">
          <div className="flex" style={{ padding: '0 30px' }}>
            {[
              { id: 'general', label: 'Основное', icon: <Package className="w-4 h-4" /> },
              { id: 'client', label: 'Клиент', icon: <User className="w-4 h-4" /> },
              { id: 'requirements', label: 'Требования', icon: <FileText className="w-4 h-4" /> },
              { id: 'attachments', label: 'Файлы', icon: <Upload className="w-4 h-4" /> },
              { id: 'notes', label: 'Заметки', icon: <MessageSquare className="w-4 h-4" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-red-500 text-red-400'
                    : 'border-transparent text-gray-400 hover:text-white'
                }`}
                style={{ padding: '15px 20px', gap: '8px', lineHeight: '1.4' }}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)', padding: '30px' }}>
          {activeTab === 'general' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* Service Type */}
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Тип услуги
                </label>
                <div className="flex" style={{ gap: '10px' }}>
                  <button
                    onClick={() => !isReadOnly && handleInputChange('type', 'artist')}
                    disabled={isReadOnly}
                    className={`flex items-center rounded-lg border transition-colors ${
                      formData.type === 'artist'
                        ? 'bg-red-500/20 border-red-500 text-red-400'
                        : 'bg-gray-800/50 border-gray-700 text-gray-400 hover:text-white'
                    } ${isReadOnly ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                    style={{ padding: '12px 16px', gap: '8px', lineHeight: '1.4' }}
                  >
                    <Mic className="w-4 h-4" />
                    Артист
                  </button>
                  <button
                    onClick={() => !isReadOnly && handleInputChange('type', 'track')}
                    disabled={isReadOnly}
                    className={`flex items-center rounded-lg border transition-colors ${
                      formData.type === 'track'
                        ? 'bg-red-500/20 border-red-500 text-red-400'
                        : 'bg-gray-800/50 border-gray-700 text-gray-400 hover:text-white'
                    } ${isReadOnly ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                    style={{ padding: '12px 16px', gap: '8px', lineHeight: '1.4' }}
                  >
                    <Music className="w-4 h-4" />
                    Трек
                  </button>
                </div>
              </div>

              {/* Service Selection */}
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Услуга
                </label>
                <select
                  value={formData.packageType}
                  onChange={(e) => !isReadOnly && handleServiceChange(e.target.value)}
                  disabled={isReadOnly}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                >
                  <option value="">Выберите услугу</option>
                  {serviceOptions[formData.type].map((service) => (
                    <option key={service.value} value={service.value}>
                      {service.label} - {service.price.toLocaleString()} ₽
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                {/* Status */}
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Статус
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => !isReadOnly && handleInputChange('status', e.target.value)}
                    disabled={isReadOnly}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  >
                    {statusOptions.map((status) => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Приоритет
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => !isReadOnly && handleInputChange('priority', e.target.value)}
                    disabled={isReadOnly}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  >
                    {priorityOptions.map((priority) => (
                      <option key={priority.value} value={priority.value}>
                        {priority.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                {/* Price */}
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Стоимость
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => !isReadOnly && handleInputChange('price', Number(e.target.value))}
                      disabled={isReadOnly}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                      style={{ padding: '12px 16px', paddingRight: '50px', lineHeight: '1.4' }}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">₽</span>
                  </div>
                </div>

                {/* Due Date */}
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Срок выполнения
                  </label>
                  <input
                    type="date"
                    value={formData.dueDate?.split('T')[0] || ''}
                    onChange={(e) => !isReadOnly && handleInputChange('dueDate', e.target.value)}
                    disabled={isReadOnly}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  />
                </div>
              </div>

              {/* Progress */}
              {mode !== 'create' && (
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Прогресс: {formData.progress}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={formData.progress}
                    onChange={(e) => !isReadOnly && handleInputChange('progress', Number(e.target.value))}
                    disabled={isReadOnly}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
                    style={{ background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${formData.progress}%, #374151 ${formData.progress}%, #374151 100%)` }}
                  />
                </div>
              )}

              {/* Description */}
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Описание
                </label>
                <textarea
                  value={formData.description || ''}
                  onChange={(e) => !isReadOnly && handleInputChange('description', e.target.value)}
                  disabled={isReadOnly}
                  rows={4}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 disabled:opacity-50"
                  style={{ padding: '12px 16px', lineHeight: '1.6' }}
                  placeholder="Подробное описание заказа..."
                />
              </div>
            </div>
          )}

          {activeTab === 'client' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: '20px' }}>
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Имя клиента
                  </label>
                  <input
                    type="text"
                    value={formData.clientName}
                    onChange={(e) => !isReadOnly && handleInputChange('clientName', e.target.value)}
                    disabled={isReadOnly}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 disabled:opacity-50"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    placeholder="Введите имя клиента"
                  />
                </div>

                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.clientEmail}
                    onChange={(e) => !isReadOnly && handleInputChange('clientEmail', e.target.value)}
                    disabled={isReadOnly}
                    className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 disabled:opacity-50"
                    style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Телефон
                </label>
                <input
                  type="tel"
                  value={formData.clientPhone || ''}
                  onChange={(e) => !isReadOnly && handleInputChange('clientPhone', e.target.value)}
                  disabled={isReadOnly}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 disabled:opacity-50"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  placeholder="+7 (999) 123-45-67"
                />
              </div>

              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Ответственный
                </label>
                <select
                  value={formData.assignedTo || ''}
                  onChange={(e) => !isReadOnly && handleInputChange('assignedTo', e.target.value)}
                  disabled={isReadOnly}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500 disabled:opacity-50"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                >
                  <option value="">Выберите ответственного</option>
                  <option value="anna-petrova">Анна Петрова</option>
                  <option value="mikhail-ivanov">Михаил Иванов</option>
                  <option value="elena-sidorova">Елена Сидорова</option>
                  <option value="dmitry-kozlov">Дмитрий Козлов</option>
                </select>
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* Add Note */}
              {!isReadOnly && (
                <div>
                  <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Добавить заметку
                  </label>
                  <div className="flex" style={{ gap: '10px' }}>
                    <input
                      type="text"
                      value={newNote}
                      onChange={(e) => setNewNote(e.target.value)}
                      className="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                      placeholder="Введите заметку..."
                      onKeyPress={(e) => e.key === 'Enter' && handleAddNote()}
                    />
                    <button
                      onClick={handleAddNote}
                      className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center"
                      style={{ padding: '12px', lineHeight: '1.4' }}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}

              {/* Notes List */}
              <div>
                <h3 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                  Заметки ({formData.notes?.length || 0})
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                  {formData.notes?.map((note, index) => (
                    <div key={index} className="bg-gray-800/30 border border-gray-700/50 rounded-lg" style={{ padding: '15px' }}>
                      <div className="text-white" style={{ lineHeight: '1.6' }}>{note}</div>
                      {!isReadOnly && (
                        <button
                          onClick={() => {
                            const updatedNotes = formData.notes?.filter((_, i) => i !== index) || [];
                            handleInputChange('notes', updatedNotes);
                          }}
                          className="text-red-400 hover:text-red-300 text-sm transition-colors"
                          style={{ marginTop: '8px', lineHeight: '1.4' }}
                        >
                          Удалить
                        </button>
                      )}
                    </div>
                  )) || (
                    <div className="text-center text-gray-400" style={{ padding: '40px 20px', lineHeight: '1.6' }}>
                      Заметок пока нет
                    </div>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Теги
                </label>
                {!isReadOnly && (
                  <div className="flex" style={{ gap: '10px', marginBottom: '15px' }}>
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      className="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '8px 12px', lineHeight: '1.4' }}
                      placeholder="Добавить тег..."
                      onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                    />
                    <button
                      onClick={handleAddTag}
                      className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center"
                      style={{ padding: '8px 12px', lineHeight: '1.4' }}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                )}
                <div className="flex flex-wrap" style={{ gap: '8px' }}>
                  {formData.tags?.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-red-500/20 text-red-400 rounded-full flex items-center"
                      style={{ padding: '4px 12px', gap: '6px', lineHeight: '1.4' }}
                    >
                      {tag}
                      {!isReadOnly && (
                        <button
                          onClick={() => handleRemoveTag(tag)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      )}
                    </span>
                  )) || (
                    <span className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                      Тегов нет
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'attachments' && (
            <div>
              <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
                <h3 className="text-white font-medium" style={{ lineHeight: '1.4' }}>
                  Прикрепленные файлы ({formData.attachments?.length || 0})
                </h3>
                {!isReadOnly && (
                  <button
                    onClick={() => setShowMediaManager(true)}
                    className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
                    style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
                  >
                    <Upload className="w-4 h-4" />
                    Добавить файлы
                  </button>
                )}
              </div>

              <div className="text-center text-gray-400" style={{ padding: '40px 20px', lineHeight: '1.6' }}>
                {formData.attachments?.length ? 'Файлы будут отображены здесь' : 'Файлов пока нет'}
              </div>
            </div>
          )}
        </div>

        {/* Media Manager */}
        <MediaManager
          isOpen={showMediaManager}
          onClose={() => setShowMediaManager(false)}
          allowMultiple={true}
          onSelectFile={(file) => {
            const updatedAttachments = [...(formData.attachments || []), file.id];
            handleInputChange('attachments', updatedAttachments);
          }}
        />
      </div>
    </div>
  );
};

export default OrderEditor;
