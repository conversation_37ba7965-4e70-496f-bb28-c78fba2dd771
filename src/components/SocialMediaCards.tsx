'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Instagram,
  Youtube,
  Twitter,
  Music,
  Users,
  Heart,
  MessageCircle,
  Share2,
  TrendingUp,
  Eye,
  Play,
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  Copy,
  CheckCircle,
  AlertCircle,
  Zap,
  Target,
  BarChart3,
  Settings,
  Link,
  Globe
} from 'lucide-react';
import { SocialAccount } from '@/types/pricing';

interface SocialMediaCardsProps {
  accounts: SocialAccount[];
  onAddAccount: (accountData: Partial<SocialAccount>) => void;
  onUpdateAccount: (accountId: string, data: Partial<SocialAccount>) => void;
  onDeleteAccount: (accountId: string) => void;
  orderId: string;
}

const SocialMediaCards = ({ 
  accounts, 
  onAddAccount, 
  onUpdateAccount, 
  onDeleteAccount,
  orderId 
}: SocialMediaCardsProps) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<SocialAccount | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');

  const platformConfig = {
    instagram: {
      name: 'Instagram',
      icon: Instagram,
      color: 'from-pink-500 to-purple-600',
      bgColor: 'bg-gradient-to-br from-pink-500/10 to-purple-600/10',
      borderColor: 'border-pink-500/30',
      textColor: 'text-pink-400',
      hoverColor: 'hover:border-pink-400'
    },
    youtube: {
      name: 'YouTube',
      icon: Youtube,
      color: 'from-red-500 to-red-600',
      bgColor: 'bg-gradient-to-br from-red-500/10 to-red-600/10',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      hoverColor: 'hover:border-red-400'
    },
    tiktok: {
      name: 'TikTok',
      icon: Music,
      color: 'from-black to-pink-500',
      bgColor: 'bg-gradient-to-br from-gray-900/50 to-pink-500/10',
      borderColor: 'border-pink-500/30',
      textColor: 'text-pink-400',
      hoverColor: 'hover:border-pink-400'
    },
    twitter: {
      name: 'Twitter/X',
      icon: Twitter,
      color: 'from-blue-400 to-blue-600',
      bgColor: 'bg-gradient-to-br from-blue-400/10 to-blue-600/10',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400',
      hoverColor: 'hover:border-blue-400'
    },
    vk: {
      name: 'VKontakte',
      icon: Globe,
      color: 'from-blue-600 to-indigo-600',
      bgColor: 'bg-gradient-to-br from-blue-600/10 to-indigo-600/10',
      borderColor: 'border-blue-600/30',
      textColor: 'text-blue-400',
      hoverColor: 'hover:border-blue-400'
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getEngagementColor = (engagement: number) => {
    if (engagement >= 5) return 'text-green-400';
    if (engagement >= 2) return 'text-yellow-400';
    return 'text-red-400';
  };

  const AccountCard = ({ account }: { account: SocialAccount }) => {
    const config = platformConfig[account.platform] || platformConfig.instagram;
    const IconComponent = config?.icon || Instagram;

    return (
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={`${config.bgColor} rounded-2xl border ${config.borderColor} ${config.hoverColor} transition-all duration-300 hover:shadow-lg hover:shadow-${account.platform === 'instagram' ? 'pink' : account.platform === 'youtube' ? 'red' : 'blue'}-500/20 group cursor-pointer`}
        style={{ padding: '24px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`w-12 h-12 bg-gradient-to-r ${config.color} rounded-xl flex items-center justify-center shadow-lg`}>
              <IconComponent className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white">@{account.username}</h3>
              <p className={`text-sm ${config.textColor} font-medium`}>{config.name}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={() => setEditingAccount(account)}
              className="w-8 h-8 rounded-lg bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDeleteAccount(account.id)}
              className="w-8 h-8 rounded-lg bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-4 h-4 text-gray-400" />
              <span className="text-xs text-gray-400 font-medium">Подписчики</span>
            </div>
            <p className="text-xl font-bold text-white">{formatNumber(account.followers)}</p>
          </div>
          
          <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-gray-400" />
              <span className="text-xs text-gray-400 font-medium">Вовлеченность</span>
            </div>
            <p className={`text-xl font-bold ${getEngagementColor(account.engagement)}`}>
              {account.engagement.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Heart className="w-4 h-4 text-red-400" />
              <span className="text-sm text-gray-300">Ср. лайки</span>
            </div>
            <span className="text-sm font-semibold text-white">{formatNumber(account.stats.avgLikes)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-gray-300">Ср. комментарии</span>
            </div>
            <span className="text-sm font-semibold text-white">{formatNumber(account.stats.avgComments)}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4 text-purple-400" />
              <span className="text-sm text-gray-300">Постов</span>
            </div>
            <span className="text-sm font-semibold text-white">{account.stats.postsCount}</span>
          </div>
        </div>

        {/* Niche Tags */}
        {account.niche && account.niche.length > 0 && (
          <div className="mb-6">
            <p className="text-xs text-gray-400 mb-2 font-medium">Ниши:</p>
            <div className="flex flex-wrap gap-2">
              {account.niche.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-800/50 text-xs text-gray-300 rounded-lg border border-gray-700/30"
                >
                  {tag}
                </span>
              ))}
              {account.niche.length > 3 && (
                <span className="px-2 py-1 bg-gray-800/50 text-xs text-gray-400 rounded-lg border border-gray-700/30">
                  +{account.niche.length - 3}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {account.isActive ? (
              <>
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400 font-medium">Активен</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-yellow-400 font-medium">Неактивен</span>
              </>
            )}
          </div>
          
          <button className={`flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r ${config.color} rounded-lg text-white text-sm font-medium hover:scale-105 transition-transform`}>
            <ExternalLink className="w-3 h-3" />
            Открыть
          </button>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Социальные аккаунты</h2>
          <p className="text-gray-400">Управление аккаунтами для продвижения ({accounts.length})</p>
        </div>
        
        <button
          onClick={() => setShowAddModal(true)}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium transition-all hover:scale-105 shadow-lg shadow-purple-500/25"
        >
          <Plus className="w-5 h-5" />
          Добавить аккаунт
        </button>
      </div>

      {/* Accounts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        <AnimatePresence>
          {accounts.map((account) => (
            <AccountCard key={account.id} account={account} />
          ))}
        </AnimatePresence>
        
        {/* Add New Card */}
        <motion.button
          onClick={() => setShowAddModal(true)}
          className="min-h-[400px] border-2 border-dashed border-gray-600 hover:border-purple-500 rounded-2xl flex flex-col items-center justify-center gap-4 text-gray-400 hover:text-purple-400 transition-all duration-300 hover:bg-purple-500/5"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="w-16 h-16 bg-gray-800 rounded-2xl flex items-center justify-center">
            <Plus className="w-8 h-8" />
          </div>
          <div className="text-center">
            <p className="font-semibold text-lg">Добавить аккаунт</p>
            <p className="text-sm">Подключите новую социальную сеть</p>
          </div>
        </motion.button>
      </div>

      {/* Add Account Modal */}
      <AnimatePresence>
        {showAddModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowAddModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-2xl border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Добавить социальный аккаунт</h3>

                {/* Platform Selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-3">Выберите платформу</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(platformConfig).map(([platform, config]) => {
                      const IconComponent = config.icon;
                      return (
                        <button
                          key={platform}
                          onClick={() => setSelectedPlatform(platform)}
                          className={`p-4 rounded-xl border-2 transition-all ${
                            selectedPlatform === platform
                              ? `${config.borderColor} ${config.bgColor}`
                              : 'border-gray-700 bg-gray-800/30 hover:border-gray-600'
                          }`}
                        >
                          <div className="flex flex-col items-center gap-2">
                            <div className={`w-10 h-10 bg-gradient-to-r ${config.color} rounded-lg flex items-center justify-center`}>
                              <IconComponent className="w-5 h-5 text-white" />
                            </div>
                            <span className="text-sm font-medium text-white">{config.name}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {selectedPlatform && (
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      const formData = new FormData(e.target as HTMLFormElement);
                      onAddAccount({
                        platform: selectedPlatform as any,
                        username: formData.get('username') as string,
                        followers: Number(formData.get('followers')) || 0,
                        engagement: Number(formData.get('engagement')) || 0,
                        niche: (formData.get('niche') as string).split(',').map(s => s.trim()).filter(Boolean),
                        isActive: true,
                        credentials: {
                          accessToken: formData.get('accessToken') as string,
                          apiKey: formData.get('apiKey') as string
                        },
                        stats: {
                          postsCount: Number(formData.get('postsCount')) || 0,
                          avgLikes: Number(formData.get('avgLikes')) || 0,
                          avgComments: Number(formData.get('avgComments')) || 0,
                          lastActivity: new Date().toISOString()
                        }
                      });
                      setShowAddModal(false);
                      setSelectedPlatform('');
                    }}
                    className="space-y-6"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">Имя пользователя</label>
                        <input
                          type="text"
                          name="username"
                          required
                          placeholder="username"
                          className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">Подписчики</label>
                        <input
                          type="number"
                          name="followers"
                          placeholder="10000"
                          className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">Вовлеченность (%)</label>
                        <input
                          type="number"
                          name="engagement"
                          step="0.1"
                          placeholder="5.5"
                          className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">Количество постов</label>
                        <input
                          type="number"
                          name="postsCount"
                          placeholder="150"
                          className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Ниши (через запятую)</label>
                      <input
                        type="text"
                        name="niche"
                        placeholder="музыка, хип-хоп, рэп"
                        className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Access Token (опционально)</label>
                      <input
                        type="password"
                        name="accessToken"
                        placeholder="Токен доступа к API"
                        className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                      />
                    </div>

                    <div className="flex gap-4 pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setShowAddModal(false);
                          setSelectedPlatform('');
                        }}
                        className="flex-1 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl font-medium transition-colors"
                      >
                        Отмена
                      </button>
                      <button
                        type="submit"
                        className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium transition-all"
                      >
                        Добавить аккаунт
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SocialMediaCards;
