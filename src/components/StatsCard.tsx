'use client';

import { useState } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  MoreHorizontal, 
  Eye,
  ExternalLink,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ReactNode;
  color?: 'purple' | 'blue' | 'green' | 'pink' | 'orange' | 'red';
  description?: string;
  trend?: number[];
  onClick?: () => void;
  actions?: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
  }>;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  color = 'purple',
  description,
  trend,
  onClick,
  actions
}) => {
  const [showActions, setShowActions] = useState(false);

  const colorClasses = {
    purple: {
      icon: 'text-purple-500',
      bg: 'bg-purple-500/10',
      border: 'border-purple-500/20',
      trend: 'stroke-purple-500'
    },
    pink: {
      icon: 'text-pink-500',
      bg: 'bg-pink-500/10',
      border: 'border-pink-500/20',
      trend: 'stroke-pink-500'
    },
    blue: {
      icon: 'text-blue-500',
      bg: 'bg-blue-500/10',
      border: 'border-blue-500/20',
      trend: 'stroke-blue-500'
    },
    green: {
      icon: 'text-green-500',
      bg: 'bg-green-500/10',
      border: 'border-green-500/20',
      trend: 'stroke-green-500'
    },
    orange: {
      icon: 'text-orange-500',
      bg: 'bg-orange-500/10',
      border: 'border-orange-500/20',
      trend: 'stroke-orange-500'
    },
    red: {
      icon: 'text-pink-500',
      bg: 'bg-pink-500/10',
      border: 'border-pink-500/20',
      trend: 'stroke-pink-500'
    }
  };

  // Защита от неопределенного цвета
  const safeColor = color && colorClasses[color] ? color : 'purple';
  const colors = colorClasses[safeColor];

  // Дополнительная проверка
  if (!colors) {
    console.error('StatsCard: Неопределенный цвет:', color, 'Доступные цвета:', Object.keys(colorClasses));
    return null;
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      }
      if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  const renderTrendLine = () => {
    if (!trend || trend.length < 2) return null;

    const max = Math.max(...trend);
    const min = Math.min(...trend);
    const range = max - min || 1;

    const points = trend.map((value, index) => {
      const x = (index / (trend.length - 1)) * 100;
      const y = 100 - ((value - min) / range) * 100;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="absolute bottom-0 right-0 w-20 h-10 opacity-30">
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            points={points}
            className={colors.trend}
          />
        </svg>
      </div>
    );
  };

  return (
    <div 
      className={`relative bg-gray-800/30 rounded-xl border ${colors.border} transition-all duration-300 hover:bg-gray-800/50 ${
        onClick ? 'cursor-pointer' : ''
      }`}
      style={{ padding: '20px' }}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
        <div className={`w-10 h-10 rounded-lg ${colors.bg} flex items-center justify-center ${colors.icon}`}>
          {icon}
        </div>
        
        {actions && actions.length > 0 && (
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              className="w-8 h-8 rounded-lg bg-gray-700/50 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>
            
            {showActions && (
              <div className="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 min-w-[150px]">
                {actions.map((action, index) => (
                  <button
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      action.onClick();
                      setShowActions(false);
                    }}
                    className="w-full flex items-center text-left text-gray-300 hover:text-white hover:bg-gray-700/50 transition-colors"
                    style={{ padding: '10px 15px', gap: '10px', lineHeight: '1.4' }}
                  >
                    {action.icon}
                    <span className="text-sm">{action.label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Value and Change */}
      <div style={{ marginBottom: '10px' }}>
        <div className="text-2xl font-bold text-white" style={{ lineHeight: '1.2', marginBottom: '5px' }}>
          {formatValue(value)}
        </div>
        
        {change && (
          <div className="flex items-center" style={{ gap: '5px' }}>
            {change.type === 'increase' ? (
              <TrendingUp className="w-4 h-4 text-green-400" />
            ) : (
              <TrendingDown className="w-4 h-4 text-red-400" />
            )}
            <span className={`text-sm font-medium ${
              change.type === 'increase' ? 'text-green-400' : 'text-red-400'
            }`} style={{ lineHeight: '1.4' }}>
              {change.type === 'increase' ? '+' : ''}{change.value}%
            </span>
            <span className="text-gray-400 text-sm" style={{ lineHeight: '1.4' }}>
              {change.period}
            </span>
          </div>
        )}
      </div>

      {/* Title and Description */}
      <div>
        <h3 className="text-gray-400 text-sm font-medium" style={{ lineHeight: '1.4', marginBottom: '2px' }}>
          {title}
        </h3>
        {description && (
          <p className="text-gray-500 text-xs" style={{ lineHeight: '1.6' }}>
            {description}
          </p>
        )}
      </div>

      {/* Trend Line */}
      {renderTrendLine()}

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />
    </div>
  );
};

// Компонент для группы статистик
interface StatsGridProps {
  stats: Array<Omit<StatsCardProps, 'onClick'> & { 
    id: string; 
    onClick?: () => void;
  }>;
  title?: string;
  actions?: Array<{
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
  }>;
}

export const StatsGrid: React.FC<StatsGridProps> = ({ stats, title, actions }) => {
  return (
    <div>
      {title && (
        <div className="flex items-center justify-between" style={{ marginBottom: '20px' }}>
          <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
            {title}
          </h2>
          
          {actions && actions.length > 0 && (
            <div className="flex items-center" style={{ gap: '10px' }}>
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className="flex items-center bg-gray-800/50 hover:bg-gray-800 text-gray-400 hover:text-white rounded-lg transition-colors"
                  style={{ padding: '8px 12px', gap: '6px', lineHeight: '1.4' }}
                >
                  {action.icon}
                  <span className="text-sm">{action.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4" style={{ gap: '20px' }}>
        {stats.map((stat) => (
          <StatsCard key={stat.id} {...stat} />
        ))}
      </div>
    </div>
  );
};

export default StatsCard;
