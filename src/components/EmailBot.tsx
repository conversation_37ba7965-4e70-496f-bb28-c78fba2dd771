'use client';

import { useState, useEffect } from 'react';
import { Mail, Send, BarChart3, Target, CheckCircle, Settings, Database, Zap, Clock, RefreshCw, AlertCircle } from 'lucide-react';

interface EmailStats {
  totalEmails: number;
  sentToday: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
}

interface EmailDatabase {
  id: string;
  name: string;
  contacts: number;
  lastUpdated: string;
  isActive: boolean;
  source: string;
}

interface EmailCampaign {
  id: string;
  name: string;
  status: 'draft' | 'sending' | 'sent' | 'paused';
  sent: number;
  total: number;
  openRate: number;
  createdAt: string;
}

const EmailBot = () => {
  const [isActive, setIsActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<EmailStats>({
    totalEmails: 0,
    sentToday: 0,
    openRate: 0,
    clickRate: 0,
    bounceRate: 0
  });
  const [databases, setDatabases] = useState<EmailDatabase[]>([]);
  const [smtpSettings, setSmtpSettings] = useState({
    host: '',
    port: '587',
    username: '',
    password: '',
    fromName: 'HIVE Agency',
    fromEmail: ''
  });
  const [selectedDatabases, setSelectedDatabases] = useState<string[]>([]);

  useEffect(() => {
    loadEmailData();
  }, []);

  const loadEmailData = async () => {
    try {
      setLoading(true);
      // Здесь будет загрузка данных из API
      // Пока используем моковые данные
      setStats({
        totalEmails: 15420,
        sentToday: 234,
        openRate: 24.5,
        clickRate: 3.2,
        bounceRate: 1.8
      });
      
      setDatabases([
        {
          id: '1',
          name: 'Музыкальные лейблы',
          contacts: 1247,
          lastUpdated: '2 часа назад',
          isActive: true,
          source: 'Импорт CSV'
        },
        {
          id: '2',
          name: 'Независимые артисты',
          contacts: 3521,
          lastUpdated: '1 день назад',
          isActive: true,
          source: 'Веб-форма'
        },
        {
          id: '3',
          name: 'Музыкальные блогеры',
          contacts: 892,
          lastUpdated: '3 дня назад',
          isActive: false,
          source: 'Парсинг'
        }
      ]);

      setCampaigns([
        {
          id: '1',
          name: 'Промо новых артистов',
          status: 'sending',
          sent: 1247,
          total: 2500,
          openRate: 28.3,
          createdAt: '2024-01-15'
        },
        {
          id: '2',
          name: 'Релиз трека недели',
          status: 'sent',
          sent: 3521,
          total: 3521,
          openRate: 31.7,
          createdAt: '2024-01-14'
        }
      ]);
    } catch (error) {
      console.error('Error loading email data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleBot = async () => {
    try {
      setLoading(true);
      setIsActive(!isActive);
      // Здесь будет API вызов для включения/выключения бота
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error toggling bot:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      // Здесь будет API вызов для сохранения настроек
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Email Bot</h2>
            <p className="text-gray-400">Автоматическая email рассылка</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
            isActive ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
          }`}>
            {isActive ? <CheckCircle className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
            {isActive ? 'Активен' : 'Неактивен'}
          </div>
          <button
            onClick={toggleBot}
            disabled={loading}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
              isActive 
                ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30' 
                : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Zap className="w-4 h-4" />
            )}
            {isActive ? 'Остановить' : 'Запустить'}
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Send className="w-6 h-6 text-blue-400" />
            <span className="text-sm text-green-400">+{stats.sentToday}</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.totalEmails.toLocaleString()}</div>
          <div className="text-sm text-gray-400">Всего отправлено</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Mail className="w-6 h-6 text-green-400" />
            <span className="text-sm text-blue-400">Сегодня</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.sentToday}</div>
          <div className="text-sm text-gray-400">Отправлено сегодня</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <BarChart3 className="w-6 h-6 text-purple-400" />
            <span className="text-sm text-green-400">+2.1%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.openRate}%</div>
          <div className="text-sm text-gray-400">Открываемость</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <Target className="w-6 h-6 text-yellow-400" />
            <span className="text-sm text-purple-400">{stats.clickRate}%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.clickRate}%</div>
          <div className="text-sm text-gray-400">Кликабельность</div>
        </div>

        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center justify-between mb-4">
            <AlertCircle className="w-6 h-6 text-red-400" />
            <span className="text-sm text-green-400">-0.3%</span>
          </div>
          <div className="text-2xl font-bold text-white mb-1">{stats.bounceRate}%</div>
          <div className="text-sm text-gray-400">Отказы</div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* SMTP Settings */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center gap-3 mb-6">
            <Settings className="w-5 h-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">SMTP настройки</h3>
          </div>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  SMTP хост
                </label>
                <input
                  type="text"
                  value={smtpSettings.host}
                  onChange={(e) => setSmtpSettings({...smtpSettings, host: e.target.value})}
                  placeholder="smtp.gmail.com"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Порт
                </label>
                <input
                  type="text"
                  value={smtpSettings.port}
                  onChange={(e) => setSmtpSettings({...smtpSettings, port: e.target.value})}
                  placeholder="587"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email отправителя
              </label>
              <input
                type="email"
                value={smtpSettings.fromEmail}
                onChange={(e) => setSmtpSettings({...smtpSettings, fromEmail: e.target.value})}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Имя отправителя
              </label>
              <input
                type="text"
                value={smtpSettings.fromName}
                onChange={(e) => setSmtpSettings({...smtpSettings, fromName: e.target.value})}
                placeholder="HIVE Agency"
                className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Логин
                </label>
                <input
                  type="text"
                  value={smtpSettings.username}
                  onChange={(e) => setSmtpSettings({...smtpSettings, username: e.target.value})}
                  placeholder="username"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Пароль
                </label>
                <input
                  type="password"
                  value={smtpSettings.password}
                  onChange={(e) => setSmtpSettings({...smtpSettings, password: e.target.value})}
                  placeholder="password"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
              </div>
            </div>

            <button
              onClick={saveSettings}
              disabled={loading}
              className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-600 hover:to-blue-600 transition-all disabled:opacity-50"
            >
              {loading ? 'Сохранение...' : 'Сохранить настройки'}
            </button>
          </div>
        </div>

        {/* Email Databases */}
        <div className="bg-gray-800/30 rounded-xl border border-gray-700/30 p-6">
          <div className="flex items-center gap-3 mb-6">
            <Database className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">Базы данных</h3>
          </div>
          
          <div className="space-y-4">
            {databases.map((db) => (
              <div key={db.id} className="bg-gray-700/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={selectedDatabases.includes(db.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedDatabases([...selectedDatabases, db.id]);
                        } else {
                          setSelectedDatabases(selectedDatabases.filter(id => id !== db.id));
                        }
                      }}
                      className="w-4 h-4 text-purple-500 bg-gray-600 border-gray-500 rounded focus:ring-purple-500"
                    />
                    <div className={`w-3 h-3 rounded-full ${
                      db.isActive ? 'bg-green-400' : 'bg-gray-400'
                    }`} />
                    <div>
                      <div className="font-medium text-white">{db.name}</div>
                      <div className="text-sm text-gray-400">{db.source}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-white">{db.contacts.toLocaleString()}</div>
                    <div className="text-xs text-gray-400">контактов</div>
                  </div>
                </div>
                <div className="text-xs text-gray-400">
                  Обновлено: {db.lastUpdated}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailBot;
