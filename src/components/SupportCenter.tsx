'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Send,
  Bot,
  User,
  Headphones,
  MessageCircle,
  Search,
  BookOpen,
  HelpCircle,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Zap,
  Shield,
  Settings
} from 'lucide-react';

interface SupportCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

const SupportCenter = ({ isOpen, onClose }: SupportCenterProps) => {
  const [activeTab, setActiveTab] = useState<'chat' | 'faq' | 'contact'>('chat');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Привет! Я AI-помощник HIVE Agency. Как дела? Чем могу помочь? 🎵',
      timestamp: new Date(),
      suggestions: [
        'Как создать заказ?',
        'Проблемы с оплатой',
        'Статус моего заказа',
        'Настройки аккаунта'
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(content);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        suggestions: aiResponse.suggestions
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string) => {
    const input = userInput.toLowerCase();
    
    if (input.includes('заказ') || input.includes('создать')) {
      return {
        content: 'Для создания заказа:\n\n1. Перейдите в раздел "Продвижение"\n2. Выберите тип: Артист или Трек\n3. Заполните данные проекта\n4. Выберите нужные услуги\n5. Оформите заказ\n\nНужна помощь с конкретным шагом?',
        suggestions: ['Выбор услуг', 'Оплата заказа', 'Сроки выполнения']
      };
    }
    
    if (input.includes('оплат') || input.includes('платеж')) {
      return {
        content: 'По вопросам оплаты:\n\n💳 Принимаем: карты, криптовалюты, банковские переводы\n⚡ Обработка: мгновенно\n🔒 Безопасность: SSL шифрование\n\nЕсли платеж не прошел, проверьте:\n- Достаточность средств\n- Корректность данных карты\n- Лимиты банка',
        suggestions: ['Возврат средств', 'Смена способа оплаты', 'Чек об оплате']
      };
    }
    
    if (input.includes('статус') || input.includes('прогресс')) {
      return {
        content: 'Отслеживание заказа:\n\n📊 В дашборде → раздел "Заказы"\n📱 Push-уведомления о статусе\n📧 Email-отчеты\n\nСтатусы:\n🟡 В обработке\n🔵 Выполняется\n🟢 Завершен',
        suggestions: ['Изменить заказ', 'Ускорить выполнение', 'Отчет по заказу']
      };
    }
    
    if (input.includes('настройк') || input.includes('профиль')) {
      return {
        content: 'Настройки аккаунта:\n\n⚙️ Профиль → основная информация\n🔔 Уведомления → настройка оповещений\n🔐 Безопасность → пароль, 2FA\n💳 Платежи → способы оплаты\n\nЧто именно нужно настроить?',
        suggestions: ['Сменить пароль', 'Настроить уведомления', 'Добавить карту']
      };
    }
    
    return {
      content: 'Понял! Вот что я могу помочь:\n\n🎵 Создание и управление заказами\n💳 Вопросы по оплате и биллингу\n📊 Отслеживание прогресса\n⚙️ Настройки аккаунта\n🎯 Выбор стратегии продвижения\n\nВыберите интересующую тему или задайте конкретный вопрос!',
      suggestions: ['Создать заказ', 'Проблемы с оплатой', 'Настройки', 'Связаться с менеджером']
    };
  };

  const faqItems = [
    {
      question: 'Как создать заказ на продвижение?',
      answer: 'Перейдите в раздел "Продвижение", выберите тип (Артист/Трек), заполните данные и выберите услуги.',
      category: 'Заказы'
    },
    {
      question: 'Какие способы оплаты доступны?',
      answer: 'Принимаем банковские карты, криптовалюты, банковские переводы и электронные кошельки.',
      category: 'Оплата'
    },
    {
      question: 'Сколько времени занимает продвижение?',
      answer: 'Зависит от выбранного пакета: от 7 до 30 дней. Первые результаты видны через 24-48 часов.',
      category: 'Сроки'
    },
    {
      question: 'Можно ли отменить заказ?',
      answer: 'Да, заказ можно отменить до начала выполнения. После старта возможен частичный возврат.',
      category: 'Отмена'
    },
    {
      question: 'Как отследить прогресс заказа?',
      answer: 'В личном кабинете в разделе "Заказы" доступна детальная статистика и прогресс выполнения.',
      category: 'Отслеживание'
    }
  ];

  const contactOptions = [
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: 'Онлайн чат',
      description: 'Мгновенные ответы 24/7',
      action: 'Начать чат',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: 'Email поддержка',
      description: '<EMAIL>',
      action: 'Написать письмо',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: 'Телефон',
      description: '+7 (999) 123-45-67',
      action: 'Позвонить',
      color: 'from-green-500 to-emerald-500'
    }
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-gray-900 rounded-2xl border border-gray-800 w-full max-w-4xl h-[80vh] flex flex-col overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-b border-gray-800 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Headphones className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Support Center</h2>
                  <p className="text-gray-400">Мы здесь, чтобы помочь вам 24/7</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="w-10 h-10 rounded-xl bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex gap-2 mt-6">
              {[
                { id: 'chat', label: 'AI Помощник', icon: Bot },
                { id: 'faq', label: 'FAQ', icon: HelpCircle },
                { id: 'contact', label: 'Контакты', icon: Phone }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                    activeTab === tab.id
                      ? 'bg-purple-500 text-white'
                      : 'bg-gray-800 text-gray-400 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            {activeTab === 'chat' && (
              <div className="h-full flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.type === 'ai' && (
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                      )}
                      <div
                        className={`max-w-[70%] rounded-2xl p-4 ${
                          message.type === 'user'
                            ? 'bg-purple-500 text-white'
                            : 'bg-gray-800 text-gray-100'
                        }`}
                      >
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        {message.suggestions && (
                          <div className="mt-3 space-y-2">
                            {message.suggestions.map((suggestion, index) => (
                              <button
                                key={index}
                                onClick={() => handleSendMessage(suggestion)}
                                className="block w-full text-left px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm transition-colors"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                      {message.type === 'user' && (
                        <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="w-4 h-4 text-gray-300" />
                        </div>
                      )}
                    </div>
                  ))}
                  {isTyping && (
                    <div className="flex gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Bot className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-gray-800 rounded-2xl p-4">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input */}
                <div className="border-t border-gray-800 p-6">
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
                      placeholder="Задайте ваш вопрос..."
                      className="flex-1 bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    />
                    <button
                      onClick={() => handleSendMessage(inputValue)}
                      className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white hover:scale-105 transition-transform"
                    >
                      <Send className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'faq' && (
              <div className="h-full overflow-y-auto p-6">
                <div className="space-y-4">
                  {faqItems.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gray-800 rounded-xl p-6 border border-gray-700"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <span className="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
                              {item.category}
                            </span>
                          </div>
                          <h3 className="text-lg font-semibold text-white mb-3">{item.question}</h3>
                          <p className="text-gray-300 leading-relaxed">{item.answer}</p>
                        </div>
                        <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-1" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div className="h-full overflow-y-auto p-6">
                <div className="grid gap-6">
                  {contactOptions.map((option, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-colors group cursor-pointer"
                    >
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${option.color} rounded-xl flex items-center justify-center`}>
                          {option.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white mb-1">{option.title}</h3>
                          <p className="text-gray-400">{option.description}</p>
                        </div>
                        <button className="flex items-center gap-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors">
                          {option.action}
                          <ArrowRight className="w-4 h-4" />
                        </button>
                      </div>
                    </motion.div>
                  ))}

                  {/* Quick Actions */}
                  <div className="mt-8">
                    <h3 className="text-xl font-bold text-white mb-4">Быстрые действия</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <button className="flex items-center gap-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-xl border border-gray-700 transition-colors">
                        <BookOpen className="w-5 h-5 text-blue-400" />
                        <span className="text-white">База знаний</span>
                      </button>
                      <button className="flex items-center gap-3 p-4 bg-gray-800 hover:bg-gray-700 rounded-xl border border-gray-700 transition-colors">
                        <Settings className="w-5 h-5 text-purple-400" />
                        <span className="text-white">Настройки</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default SupportCenter;
