'use client';

import { useState } from 'react';

const PricingSectionBasic = () => {
  const [activeTab, setActiveTab] = useState('artist');

  const artistPlans = [
    {
      id: 'artist-starter',
      name: 'Старт',
      price: '25 000 ₽',
      duration: '30 дней',
      popular: false,
      features: [
        'Управление 3 социальными аккаунтами',
        '2 базовых бота для автоматизации',
        '5 email кампаний',
        'Привлечение 1000 подписчиков в Telegram',
        'Базовая аналитика',
        '<PERSON><PERSON> поддержка'
      ]
    },
    {
      id: 'artist-pro',
      name: 'Профи',
      price: '50 000 ₽',
      duration: '30 дней',
      popular: true,
      features: [
        'Управление 8 социальными аккаунтами',
        '6 продвинутых ИИ ботов',
        'Неограниченные email кампании',
        'Привлечение 5000 подписчиков в Telegram',
        'Создание контента с ИИ',
        'Работа с инфлюенсерами',
        'Продвинутая аналитика',
        'Приоритетная поддержка 24/7'
      ]
    },
    {
      id: 'artist-enterprise',
      name: 'Энтерпрайз',
      price: '100 000 ₽',
      duration: '30 дней',
      popular: false,
      features: [
        'Неограниченные социальные аккаунты',
        'ИИ боты премиум с GPT-4',
        'Вирусные стратегии продвижения',
        'Массовый охват и скрапинг',
        'Создание сети Telegram каналов',
        'Персональный менеджер',
        'Кастомные боты под задачи',
        'White Label решения'
      ]
    }
  ];

  const trackPlans = [
    {
      id: 'track-basic',
      name: 'Базовый',
      price: '15 000 ₽',
      duration: '14 дней',
      popular: false,
      features: [
        'Размещение в 10+ плейлистах',
        'Продвижение в Instagram, TikTok, VK',
        '2 базовых бота для автолайков',
        'Email рассылка о релизе',
        'Посты в 5 Telegram каналах'
      ]
    },
    {
      id: 'track-viral',
      name: 'Вирусный',
      price: '35 000 ₽',
      duration: '21 день',
      popular: true,
      features: [
        'Вирусная кампания с трендовым контентом',
        'Сеть из 20+ инфлюенсеров',
        'ИИ генерация мемов и видео',
        'Массовое вовлечение через ботов',
        'Размещение в 50+ Telegram каналах',
        'Продвижение на радиостанциях',
        'Пресс-релизы в музыкальных СМИ'
      ]
    },
    {
      id: 'track-platinum',
      name: 'Платиновый',
      price: '75 000 ₽',
      duration: '30 дней',
      popular: false,
      features: [
        'Платиновая кампания с максимальным охватом',
        'Сеть знаменитостей для продвижения',
        'Армия из сотен умных ботов',
        'Глобальное международное продвижение',
        'Сеть из 100+ Telegram каналов',
        'Освещение во всех СМИ',
        'Продвижение в топы стриминговых сервисов',
        'Выделенная команда из 5+ специалистов'
      ]
    }
  ];

  const handleSelectPlan = (plan) => {
    alert(`Выбран тариф: ${plan.name} за ${plan.price}\n\nДля оформления заказа свяжитесь с нами!`);
  };

  const currentPlans = activeTab === 'artist' ? artistPlans : trackPlans;

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Выберите свой <span className="text-red-500">тариф</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            Профессиональное продвижение музыки с гарантированным результатом. 
            Выберите подходящий тариф и начните свой путь к успеху уже сегодня.
          </p>

          {/* Tab Switcher */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800/50 rounded-2xl p-2 backdrop-blur-xl border border-gray-700/50">
              <button
                onClick={() => setActiveTab('artist')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'artist'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                👥 Продвижение артистов
              </button>
              <button
                onClick={() => setActiveTab('track')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'track'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                🎵 Продвижение треков
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {currentPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-gray-900/50 backdrop-blur-xl rounded-3xl border transition-all duration-300 hover:scale-105 hover:shadow-2xl p-8 ${
                plan.popular
                  ? 'border-red-500/50 shadow-lg shadow-red-500/25'
                  : 'border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-red-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                    ⭐ Популярный
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="text-4xl font-bold text-white mb-2">
                  {plan.price}
                </div>
                <p className="text-gray-400">за {plan.duration}</p>
              </div>

              {/* Features */}
              <div className="mb-8">
                <h4 className="text-white font-semibold mb-4">Что включено:</h4>
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="text-red-400 mt-0.5">
                        ✅
                      </div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* CTA Button */}
              <button
                onClick={() => handleSelectPlan(plan)}
                className={`w-full py-4 rounded-xl font-semibold transition-all duration-300 ${
                  plan.popular
                    ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg shadow-red-500/25'
                    : 'bg-gray-700 hover:bg-gray-600 text-white'
                }`}
              >
                Выбрать тариф
              </button>

              {/* Payment Methods */}
              <div className="mt-4 flex justify-center items-center gap-2 text-gray-400 text-xs">
                💳 ₿ 📱 Карта • Крипто • СБП
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-16">
          <p className="text-gray-400 mb-4">
            Все тарифы включают бесплатную консультацию и гарантию результата
          </p>
          <div className="flex justify-center items-center gap-8 text-sm text-gray-500">
            <div>✅ Безопасные платежи</div>
            <div>✅ Гарантия результата</div>
            <div>👥 150+ успешных кампаний</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSectionBasic;
