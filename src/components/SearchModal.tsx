'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Search, 
  X, 
  Clock, 
  TrendingUp, 
  Music, 
  User, 
  Hash,
  ArrowRight,
  Mic,
  Radio,
  Users,
  Building
} from 'lucide-react';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SearchResult {
  id: string;
  type: 'artist' | 'track' | 'service' | 'user';
  title: string;
  subtitle?: string;
  image?: string;
  verified?: boolean;
}

const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'MORGENSHTERN',
    'Продвижение трека',
    'Instagram реклама',
    'Радиостанции'
  ]);
  const [trendingSearches] = useState<string[]>([
    'TikTok продвижение',
    'Spotify плейлисты',
    'YouTube каналы',
    'Музыкальные блогеры'
  ]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Mock search results
  const mockResults: SearchResult[] = [
    {
      id: '1',
      type: 'artist',
      title: 'MORGENSHTERN',
      subtitle: '12.5M подписчиков',
      verified: true
    },
    {
      id: '2',
      type: 'track',
      title: 'Новый хит',
      subtitle: 'MORGENSHTERN • 2024'
    },
    {
      id: '3',
      type: 'service',
      title: 'Продвижение в Instagram',
      subtitle: 'SMM услуги • от 15,000 ₽'
    },
    {
      id: '4',
      type: 'service',
      title: 'Размещение на радио',
      subtitle: 'Радиостанции • от 25,000 ₽'
    },
    {
      id: '5',
      type: 'user',
      title: 'Анна Петрова',
      subtitle: 'Менеджер по продвижению'
    }
  ];

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.length > 0) {
      // Фильтруем результаты по запросу
      const filtered = mockResults.filter(result =>
        result.title.toLowerCase().includes(query.toLowerCase()) ||
        result.subtitle?.toLowerCase().includes(query.toLowerCase())
      );
      setResults(filtered);
    } else {
      setResults([]);
    }
  }, [query]);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    // Добавляем в недавние поиски
    if (searchQuery && !recentSearches.includes(searchQuery)) {
      setRecentSearches(prev => [searchQuery, ...prev.slice(0, 4)]);
    }
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'artist':
        return <Mic className="w-4 h-4" />;
      case 'track':
        return <Music className="w-4 h-4" />;
      case 'service':
        return <Radio className="w-4 h-4" />;
      case 'user':
        return <User className="w-4 h-4" />;
      default:
        return <Hash className="w-4 h-4" />;
    }
  };

  const getResultTypeLabel = (type: string) => {
    switch (type) {
      case 'artist':
        return 'Артист';
      case 'track':
        return 'Трек';
      case 'service':
        return 'Услуга';
      case 'user':
        return 'Пользователь';
      default:
        return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center" style={{ paddingTop: '10vh' }}>
      <div className="bg-gray-900 rounded-2xl border border-gray-800 w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
        {/* Search Input */}
        <div className="flex items-center border-b border-gray-800" style={{ padding: '20px' }}>
          <Search className="w-5 h-5 text-gray-400" style={{ marginRight: '15px' }} />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Поиск артистов, треков, услуг..."
            className="flex-1 bg-transparent text-white placeholder-gray-400 focus:outline-none"
            style={{ fontSize: '16px', lineHeight: '1.4' }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && query) {
                handleSearch(query);
              }
            }}
          />
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            style={{ marginLeft: '15px' }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[60vh]" style={{ padding: '20px' }}>
          {query.length === 0 ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div>
                  <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                    <h3 className="text-white font-medium flex items-center" style={{ gap: '8px', lineHeight: '1.4' }}>
                      <Clock className="w-4 h-4" />
                      Недавние поиски
                    </h3>
                    <button
                      onClick={clearRecentSearches}
                      className="text-gray-400 hover:text-white text-sm transition-colors"
                      style={{ lineHeight: '1.4' }}
                    >
                      Очистить
                    </button>
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(search)}
                        className="flex items-center text-left text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
                        style={{ padding: '10px 12px', gap: '12px', lineHeight: '1.4' }}
                      >
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span>{search}</span>
                        <ArrowRight className="w-4 h-4 text-gray-500 ml-auto" />
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Trending Searches */}
              <div>
                <h3 className="text-white font-medium flex items-center" style={{ gap: '8px', marginBottom: '15px', lineHeight: '1.4' }}>
                  <TrendingUp className="w-4 h-4" />
                  Популярные запросы
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {trendingSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(search)}
                      className="flex items-center text-left text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
                      style={{ padding: '10px 12px', gap: '12px', lineHeight: '1.4' }}
                    >
                      <TrendingUp className="w-4 h-4 text-red-500" />
                      <span>{search}</span>
                      <ArrowRight className="w-4 h-4 text-gray-500 ml-auto" />
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                  Быстрые действия
                </h3>
                <div className="grid grid-cols-2" style={{ gap: '10px' }}>
                  {[
                    { icon: <Mic className="w-5 h-5" />, label: 'Найти артистов', action: () => handleSearch('артисты') },
                    { icon: <Music className="w-5 h-5" />, label: 'Найти треки', action: () => handleSearch('треки') },
                    { icon: <Radio className="w-5 h-5" />, label: 'Услуги продвижения', action: () => handleSearch('продвижение') },
                    { icon: <Users className="w-5 h-5" />, label: 'Команда', action: () => handleSearch('команда') }
                  ].map((item, index) => (
                    <button
                      key={index}
                      onClick={item.action}
                      className="flex items-center bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors"
                      style={{ padding: '12px', gap: '10px', lineHeight: '1.4' }}
                    >
                      <div className="text-red-500">{item.icon}</div>
                      <span className="text-white text-sm">{item.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div>
              {results.length > 0 ? (
                <div>
                  <h3 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                    Результаты поиска ({results.length})
                  </h3>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {results.map((result) => (
                      <button
                        key={result.id}
                        className="flex items-center text-left hover:bg-gray-800/50 rounded-lg transition-colors"
                        style={{ padding: '12px', gap: '12px', lineHeight: '1.4' }}
                        onClick={() => {
                          // Здесь будет логика перехода к результату
                          console.log('Navigate to:', result);
                          onClose();
                        }}
                      >
                        <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400">
                          {result.image ? (
                            <img src={result.image} alt={result.title} className="w-full h-full object-cover rounded-lg" />
                          ) : (
                            getResultIcon(result.type)
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center" style={{ gap: '8px' }}>
                            <span className="text-white font-medium">{result.title}</span>
                            {result.verified && (
                              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">✓</span>
                              </div>
                            )}
                          </div>
                          {result.subtitle && (
                            <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>{result.subtitle}</p>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 bg-gray-800 rounded px-2 py-1">
                          {getResultTypeLabel(result.type)}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center" style={{ padding: '40px 20px' }}>
                  <Search className="w-12 h-12 text-gray-600 mx-auto" style={{ marginBottom: '15px' }} />
                  <h3 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Ничего не найдено
                  </h3>
                  <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                    Попробуйте изменить запрос или воспользуйтесь популярными поисками
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-800 text-center" style={{ padding: '15px 20px' }}>
          <p className="text-gray-500 text-xs" style={{ lineHeight: '1.6' }}>
            Используйте <kbd className="bg-gray-800 px-2 py-1 rounded text-xs">Ctrl</kbd> + <kbd className="bg-gray-800 px-2 py-1 rounded text-xs">K</kbd> для быстрого поиска
          </p>
        </div>
      </div>
    </div>
  );
};

export default SearchModal;
