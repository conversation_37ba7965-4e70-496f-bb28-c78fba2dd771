'use client';

import { motion } from 'framer-motion';
import { Headphones, Zap, Target, TrendingUp, Music, Users } from 'lucide-react';
import Card from '@/components/ui/Card';

const ValueProposition = () => {
  const values = [
    {
      icon: Headphones,
      title: 'AI-Анализ треков',
      description: 'Искусственный интеллект анализирует ваш трек за секунды: жанр, BPM, потенциал, целевую аудиторию',
      color: 'from-purple-500 to-pink-500',
      stats: '99% точность'
    },
    {
      icon: Zap,
      title: 'Мгновенные результаты',
      description: 'Получите персонализированную стратегию продвижения и начните видеть результаты уже через 24 часа',
      color: 'from-blue-500 to-cyan-500',
      stats: '24 часа'
    },
    {
      icon: Target,
      title: 'Точное таргетирование',
      description: 'Находим именно вашу аудиторию среди миллионов слушателей на всех платформах',
      color: 'from-green-500 to-emerald-500',
      stats: '10M+ слушателей'
    }
  ];

  const achievements = [
    { number: '50M+', label: 'Прослушиваний', icon: Music },
    { number: '10K+', label: 'Треков продвинуто', icon: TrendingUp },
    { number: '500+', label: 'Довольных артистов', icon: Users },
  ];

  return (
    <div className="container-hive">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <h2 className="text-display-2 gradient-text mb-6">
          Почему H!VE Agency?
        </h2>
        <p className="text-body-large text-gray-300 max-w-3xl mx-auto">
          Мы объединили силу искусственного интеллекта с экспертизой музыкальной индустрии, 
          чтобы дать вашей музыке максимальный охват.
        </p>
      </motion.div>

      {/* Value Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
        {values.map((value, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="p-8 h-full text-center group">
              <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r ${value.color} rounded-3xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <value.icon className="w-10 h-10 text-white" />
              </div>

              <h3 className="text-heading-3 text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                {value.title}
              </h3>

              <p className="text-body text-gray-300 mb-6 leading-relaxed">
                {value.description}
              </p>

              <div className={`inline-block px-4 py-2 bg-gradient-to-r ${value.color} rounded-full text-white font-semibold text-sm`}>
                {value.stats}
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Achievements */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <Card variant="premium" className="p-12">
          <div className="text-center mb-8">
            <h3 className="text-heading-2 text-white mb-4">Наши достижения</h3>
            <p className="text-body text-gray-300">
              Цифры, которые говорят сами за себя
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-4">
                  <achievement.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-white mb-2">
                  {achievement.number}
                </div>
                <div className="text-gray-400 font-medium">
                  {achievement.label}
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default ValueProposition;
