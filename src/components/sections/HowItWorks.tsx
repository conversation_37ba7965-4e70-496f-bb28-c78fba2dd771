'use client';

import { motion } from 'framer-motion';
import { Upload, Headphones, Rocket, ArrowRight, Music, BarChart3, Target } from 'lucide-react';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';

const HowItWorks = () => {
  const steps = [
    {
      number: '01',
      icon: Upload,
      title: 'Загрузите трек',
      description: 'Просто перетащите ваш аудиофайл или опишите ваш проект в AI-агенте',
      details: ['MP3, WAV, FLAC форматы', 'Максимум 50MB', 'Мгновенная загрузка'],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      number: '02',
      icon: Headphones,
      title: 'AI анализирует',
      description: 'Искусственный интеллект определяет жанр, потенциал и создает стратегию',
      details: ['Анализ за 30 секунд', 'Определение целевой аудитории', 'Персональные рекомендации'],
      color: 'from-purple-500 to-pink-500'
    },
    {
      number: '03',
      icon: Rocket,
      title: 'Получите результат',
      description: 'Запускаем кампанию продвижения и отслеживаем результаты в реальном времени',
      details: ['Продвижение на всех платформах', 'Реальтайм аналитика', 'Гарантия результата'],
      color: 'from-green-500 to-emerald-500'
    }
  ];

  const features = [
    {
      icon: Music,
      title: 'Анализ трека',
      description: 'BPM, тональность, жанр, энергия'
    },
    {
      icon: BarChart3,
      title: 'Потенциал оценка',
      description: 'Коммерческий, вирусный, радио потенциал'
    },
    {
      icon: Target,
      title: 'Целевая аудитория',
      description: 'Возраст, интересы, платформы'
    }
  ];

  return (
    <div className="container-hive">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <Badge variant="info" size="lg" icon={Headphones} className="mb-6">
          Как это работает
        </Badge>
        <h2 className="text-display-2 gradient-text mb-6">
          3 простых шага к успеху
        </h2>
        <p className="text-body-large text-gray-300 max-w-3xl mx-auto">
          Наш AI-агент делает продвижение музыки простым и эффективным. 
          Никаких сложных настроек — только результат.
        </p>
      </motion.div>

      {/* Steps */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
        {steps.map((step, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <Card className="p-8 h-full text-center group">
              {/* Step Number */}
              <div className="absolute -top-4 left-8">
                <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                  {step.number}
                </div>
              </div>

              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r ${step.color} rounded-3xl mb-6 mt-4 group-hover:scale-110 transition-transform duration-300`}>
                <step.icon className="w-10 h-10 text-white" />
              </div>
              
              {/* Content */}
              <h3 className="text-heading-3 text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                {step.title}
              </h3>
              
              <p className="text-body text-gray-300 mb-6 leading-relaxed">
                {step.description}
              </p>

              {/* Details */}
              <div className="space-y-2">
                {step.details.map((detail, detailIndex) => (
                  <div key={detailIndex} className="flex items-center justify-center gap-2 text-sm text-gray-400">
                    <div className="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                    {detail}
                  </div>
                ))}
              </div>
            </Card>

            {/* Arrow */}
            {index < steps.length - 1 && (
              <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <ArrowRight className="w-4 h-4 text-white" />
                </div>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* AI Features */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <Card variant="premium" className="p-12">
          <div className="text-center mb-12">
            <h3 className="text-heading-2 text-white mb-4">Что анализирует наш AI</h3>
            <p className="text-body text-gray-300 max-w-2xl mx-auto">
              Наш искусственный интеллект использует передовые алгоритмы для глубокого анализа вашей музыки
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors duration-300">
                  {feature.title}
                </h4>
                <p className="text-gray-400 text-sm">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default HowItWorks;
