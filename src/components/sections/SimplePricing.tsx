'use client';

import { motion } from 'framer-motion';
import { Check, Sparkles, Crown, Zap, CreditCard } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';

const SimplePricing = () => {
  const plans = [
    {
      name: 'Starter',
      price: '15,000',
      originalPrice: '25,000',
      description: 'Идеально для первого трека',
      icon: Zap,
      color: 'from-blue-500 to-cyan-500',
      popular: false,
      features: [
        'AI анализ трека',
        'Продвижение на 3 платформах',
        '10K потенциальных прослушиваний',
        'Базовая аналитика',
        'Email поддержка'
      ],
      cta: 'Начать продвижение'
    },
    {
      name: 'Professional',
      price: '35,000',
      originalPrice: '50,000',
      description: 'Для серьезного продвижения',
      icon: Sparkles,
      color: 'from-purple-500 to-pink-500',
      popular: true,
      features: [
        'Расширенный AI анализ',
        'Продвижение на всех платформах',
        '50K потенциальных прослушиваний',
        'Продвинутая аналитика',
        'Персональный менеджер',
        'A/B тестирование',
        'Приоритетная поддержка'
      ],
      cta: 'Выбрать план'
    },
    {
      name: 'Enterprise',
      price: 'По запросу',
      originalPrice: null,
      description: 'Для лейблов и агентств',
      icon: Crown,
      color: 'from-yellow-500 to-orange-500',
      popular: false,
      features: [
        'Безлимитные треки',
        'Белый лейбл решение',
        'API интеграция',
        'Команда экспертов',
        '24/7 VIP поддержка',
        'Кастомные стратегии'
      ],
      cta: 'Связаться с нами'
    }
  ];

  const guarantee = [
    'Возврат средств в течение 30 дней',
    'Гарантия первых результатов за 24 часа',
    'Бесплатная консультация перед стартом'
  ];

  return (
    <div className="container-hive">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <Badge variant="warning" size="lg" icon={CreditCard} className="mb-6">
          Специальные цены
        </Badge>
        <h2 className="text-display-2 gradient-text mb-6">
          Выберите свой план
        </h2>
        <p className="text-body-large text-gray-300 max-w-3xl mx-auto">
          Прозрачные цены без скрытых платежей. Все планы включают AI-анализ и гарантию результата.
        </p>
      </motion.div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.name}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Popular Badge */}
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                <Badge variant="primary" size="md" icon={Sparkles}>
                  Популярный выбор
                </Badge>
              </div>
            )}

            <Card 
              variant={plan.popular ? 'premium' : 'default'} 
              className={`p-8 h-full text-center ${plan.popular ? 'scale-105' : ''}`}
            >
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${plan.color} rounded-2xl mb-6`}>
                <plan.icon className="w-8 h-8 text-white" />
              </div>
              
              {/* Plan Info */}
              <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
              <p className="text-gray-400 mb-6">{plan.description}</p>
              
              {/* Price */}
              <div className="mb-8">
                {plan.originalPrice && (
                  <div className="text-gray-500 line-through text-lg mb-1">
                    {plan.originalPrice} ₽
                  </div>
                )}
                <div className="flex items-baseline justify-center gap-2">
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  {plan.price !== 'По запросу' && <span className="text-gray-400">₽</span>}
                </div>
                {plan.originalPrice && (
                  <Badge variant="success" size="sm" className="mt-2">
                    Скидка {Math.round((1 - parseInt(plan.price.replace(/\D/g, '')) / parseInt(plan.originalPrice.replace(/\D/g, ''))) * 100)}%
                  </Badge>
                )}
              </div>

              {/* Features */}
              <div className="space-y-4 mb-8 text-left">
                {plan.features.map((feature, featureIndex) => (
                  <motion.div
                    key={featureIndex}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 + featureIndex * 0.05 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-3"
                  >
                    <div className={`flex-shrink-0 w-6 h-6 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center`}>
                      <Check className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-300">{feature}</span>
                  </motion.div>
                ))}
              </div>

              {/* CTA Button */}
              <Button
                variant={plan.popular ? 'primary' : 'secondary'}
                size="lg"
                className="w-full"
              >
                {plan.cta}
              </Button>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Guarantee */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        viewport={{ once: true }}
      >
        <Card variant="glass" className="p-8 max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mb-4">
              <Check className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Наши гарантии</h3>
            <p className="text-gray-300">
              Мы уверены в качестве наших услуг и предоставляем следующие гарантии
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {guarantee.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mb-3"></div>
                <p className="text-gray-300 text-sm">{item}</p>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default SimplePricing;
