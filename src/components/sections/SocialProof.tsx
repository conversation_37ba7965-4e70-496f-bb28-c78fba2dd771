'use client';

import { motion } from 'framer-motion';
import { Star, Quote, Music, TrendingUp, Users, Play } from 'lucide-react';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';

const SocialProof = () => {
  const testimonials = [
    {
      name: 'Алек<PERSON>ей Волков',
      role: 'Электронный продюсер',
      avatar: '🎧',
      rating: 5,
      text: 'H!VE Agency помогли мне получить 2M прослушиваний на Spotify за месяц! AI-анализ показал точно, какую аудиторию таргетировать.',
      results: '+2M прослушиваний',
      platform: 'Spotify'
    },
    {
      name: 'Мария Петрова',
      role: 'Инди-артист',
      avatar: '🎤',
      rating: 5,
      text: 'Невероятно! Мой трек попал в топ-50 российского чарта благодаря их стратегии. Рекомендую всем начинающим артистам.',
      results: 'Топ-50 чарт',
      platform: 'Apple Music'
    },
    {
      name: 'DJ Максим',
      role: 'Диджей и продюсер',
      avatar: '🎵',
      rating: 5,
      text: 'Лучший сервис для продвижения! AI сразу понял, что мой трек подойдет для клубов. Теперь играют по всей России.',
      results: '+500 клубов',
      platform: 'SoundCloud'
    }
  ];

  const platforms = [
    { name: 'Spotify', logo: '🎵', users: '500M+' },
    { name: 'Apple Music', logo: '🍎', users: '100M+' },
    { name: 'YouTube Music', logo: '📺', users: '2B+' },
    { name: 'SoundCloud', logo: '☁️', users: '300M+' },
    { name: 'TikTok', logo: '🎬', users: '1B+' },
    { name: 'VK Music', logo: '🎶', users: '100M+' }
  ];

  const stats = [
    { number: '95%', label: 'Успешных кампаний', icon: TrendingUp },
    { number: '4.9/5', label: 'Средняя оценка', icon: Star },
    { number: '24ч', label: 'Первые результаты', icon: Play },
  ];

  return (
    <div className="container-hive">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <Badge variant="success" size="lg" icon={Users} className="mb-6">
          Отзывы клиентов
        </Badge>
        <h2 className="text-display-2 gradient-text mb-6">
          Истории успеха наших артистов
        </h2>
        <p className="text-body-large text-gray-300 max-w-3xl mx-auto">
          Более 500 артистов уже достигли своих целей с H!VE Agency. 
          Присоединяйтесь к сообществу успешных музыкантов.
        </p>
      </motion.div>

      {/* Stats */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
      >
        {stats.map((stat, index) => (
          <Card key={index} className="p-6 text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-4">
              <stat.icon className="w-6 h-6 text-white" />
            </div>
            <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
            <div className="text-gray-400">{stat.label}</div>
          </Card>
        ))}
      </motion.div>

      {/* Testimonials */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="p-8 h-full">
              {/* Quote Icon */}
              <div className="flex justify-between items-start mb-6">
                <Quote className="w-8 h-8 text-purple-400" />
                <div className="flex gap-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </div>

              {/* Text */}
              <p className="text-gray-300 mb-6 leading-relaxed">
                "                &ldquo;{testimonial.text}&rdquo;"
              </p>

              {/* Results */}
              <div className="mb-6">
                <Badge variant="success" size="sm" className="mb-2">
                  {testimonial.results}
                </Badge>
                <div className="text-sm text-gray-400">
                  Платформа: {testimonial.platform}
                </div>
              </div>

              {/* Author */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-xl">
                  {testimonial.avatar}
                </div>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-gray-400">{testimonial.role}</div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Platforms */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <Card variant="glass" className="p-12">
          <div className="text-center mb-12">
            <h3 className="text-heading-2 text-white mb-4">Продвигаем на всех платформах</h3>
            <p className="text-body text-gray-300">
              Ваша музыка будет доступна миллиардам слушателей по всему миру
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {platforms.map((platform, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group cursor-pointer"
              >
                <div className="w-16 h-16 bg-black/30 rounded-2xl flex items-center justify-center text-2xl mb-3 group-hover:bg-black/50 transition-colors duration-300 mx-auto">
                  {platform.logo}
                </div>
                <div className="text-white font-medium text-sm mb-1 group-hover:text-purple-300 transition-colors duration-300">
                  {platform.name}
                </div>
                <div className="text-gray-400 text-xs">
                  {platform.users}
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default SocialProof;
