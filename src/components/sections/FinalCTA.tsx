'use client';

import { motion } from 'framer-motion';
import { Rocket, Headphones, Star, ArrowRight, Sparkles, Music } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const FinalCTA = () => {
  const benefits = [
    'AI анализ за 30 секунд',
    'Первые результаты за 24 часа',
    'Продвижение на всех платформах',
    'Гарантия возврата средств'
  ];

  const urgency = [
    { text: 'Ограниченное предложение', icon: Star },
    { text: 'Скидка 40% до конца месяца', icon: Sparkles },
    { text: 'Бесплатная консультация', icon: Headphones }
  ];

  return (
    <div className="container-hive">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <Card variant="premium" className="p-12 text-center relative overflow-hidden">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
          
          {/* Floating Elements */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-60"
              animate={{
                x: [0, 30, 0],
                y: [0, -30, 0],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 4 + i * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                left: `${20 + (i * 10) % 60}%`,
                top: `${30 + (i * 8) % 40}%`,
              }}
            />
          ))}

          <div className="relative z-10">
            {/* Icon */}
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl mb-8"
            >
              <Rocket className="w-12 h-12 text-white" />
            </motion.div>

            {/* Urgency Badges */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {urgency.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold"
                >
                  <item.icon className="w-4 h-4" />
                  {item.text}
                </motion.div>
              ))}
            </div>

            {/* Main Headline */}
            <h2 className="text-display-2 gradient-text mb-6">
              Готовы покорить музыкальные чарты?
            </h2>
            
            <p className="text-body-large text-gray-300 max-w-3xl mx-auto mb-8">
              Присоединяйтесь к 500+ успешным артистам, которые уже используют силу AI 
              для продвижения своей музыки. Начните прямо сейчас!
            </p>

            {/* Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-3 bg-black/20 rounded-xl p-4"
                >
                  <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <Star className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-gray-300 text-sm font-medium">{benefit}</span>
                </motion.div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button
                variant="primary"
                size="xl"
                icon={Headphones}
                className="text-xl px-12 py-6"
              >
                Начать с AI-анализа
              </Button>
              
              <Button
                variant="ghost"
                size="lg"
                icon={Music}
                iconPosition="right"
              >
                Посмотреть примеры
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-400 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                Без обязательств
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                Возврат средств 30 дней
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                Поддержка 24/7
              </div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Secondary CTA */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        viewport={{ once: true }}
        className="text-center mt-12"
      >
        <p className="text-gray-400 mb-4">
          Все еще сомневаетесь? Получите бесплатную консультацию
        </p>
        <Button variant="outline" size="md" icon={ArrowRight} iconPosition="right">
          Связаться с экспертом
        </Button>
      </motion.div>
    </div>
  );
};

export default FinalCTA;
