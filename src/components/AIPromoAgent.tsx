'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, Send, Upload, Music, User, TrendingUp, Target, Zap } from 'lucide-react';
import ServiceRecommendations from './ServiceRecommendations';
import { useAuth } from '@/contexts/AuthContext';
// import { aiService } from '@/services/aiService';
// import { orderService } from '@/services/orderService';
import { useRouter } from 'next/navigation';

interface ServiceCard {
  id: string;
  title: string;
  platform: string;
  description: string;
  estimatedReach: string;
  price: number;
  icon: React.ReactNode;
  color: string;
}

interface AIConversation {
  id: string;
  type: 'user' | 'ai';
  message: string;
  timestamp: Date;
}

const AIPromoAgent = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'artist' | 'track'>('track');
  const [userInput, setUserInput] = useState('');
  const [conversations, setConversations] = useState<AIConversation[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showServices, setShowServices] = useState(false);
  const [recommendedServices, setRecommendedServices] = useState<ServiceCard[]>([]);
  const [currentStep, setCurrentStep] = useState<'input' | 'questions' | 'services' | 'purchase'>('input');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [userData, setUserData] = useState<any>({});
  const messageIdRef = useRef(0);

  // Примеры сервисов для демонстрации
  const serviceExamples: ServiceCard[] = [
    {
      id: '1',
      title: 'TikTok Продвижение',
      platform: 'TikTok',
      description: 'Вирусное продвижение в TikTok с таргетингом по интересам',
      estimatedReach: '500K - 1.2M',
      price: 25000,
      icon: <TrendingUp className="w-6 h-6" />,
      color: 'from-pink-500 to-purple-600',
      features: ['Таргетинг по интересам', 'Вирусный контент', 'Аналитика'],
      estimatedResults: {
        plays: '+800K',
        followers: '+15K',
        engagement: '12%'
      }
    },
    {
      id: '2',
      title: 'Instagram Stories & Reels',
      platform: 'Instagram',
      description: 'Продвижение через Stories и Reels с микро-инфлюенсерами',
      estimatedReach: '300K - 800K',
      price: 35000,
      icon: <Target className="w-6 h-6" />,
      color: 'from-purple-500 to-pink-600',
      features: ['Микро-инфлюенсеры', 'Stories & Reels', 'Геотаргетинг'],
      estimatedResults: {
        plays: '+600K',
        followers: '+25K',
        engagement: '8%'
      }
    },
    {
      id: '3',
      title: 'YouTube Shorts',
      platform: 'YouTube',
      description: 'Создание и продвижение YouTube Shorts',
      estimatedReach: '200K - 600K',
      price: 30000,
      icon: <Zap className="w-6 h-6" />,
      color: 'from-red-500 to-orange-600',
      features: ['YouTube Shorts', 'SEO оптимизация', 'Монетизация'],
      estimatedResults: {
        plays: '+400K',
        followers: '+8K',
        engagement: '15%'
      }
    }
  ];

  const handlePurchase = async (selectedServices: ServiceCard[]) => {
    if (!user) {
      // Перенаправляем на страницу входа
      router.push('/login');
      return;
    }

    try {
      setIsProcessing(true);

      // Временная заглушка - показываем алерт с информацией о заказе
      const totalAmount = selectedServices.reduce((sum, service) => sum + service.price, 0);
      const serviceNames = selectedServices.map(s => s.title).join(', ');

      alert(`Заказ создан!\n\nСервисы: ${serviceNames}\nСумма: ${totalAmount.toLocaleString()} ₽\n\nВ реальной версии вы будете перенаправлены в дашборд для оплаты.`);

      // Перенаправляем в дашборд
      router.push('/dashboard');

      setCurrentStep('purchase');
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Ошибка при создании заказа. Попробуйте еще раз.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim()) return;

    setIsProcessing(true);

    // Добавляем сообщение пользователя
    const userMessage: AIConversation = {
      id: `user-${++messageIdRef.current}`,
      type: 'user',
      message: userInput,
      timestamp: new Date()
    };

    const newConversations = [...conversations, userMessage];
    setConversations(newConversations);

    try {
      // Сохраняем разговор в Appwrite (если пользователь авторизован)
      if (user && !conversationId) {
        // const savedId = await aiService.saveAIConversation(user.$id, activeTab, {
        //   initialPrompt: userInput,
        //   conversations: newConversations,
        //   userData: userData
        // });
        // setConversationId(savedId);
        console.log('Conversation would be saved for user:', user.$id);
      }
    } catch (error) {
      console.error('Error saving conversation:', error);
    }

    setUserInput('');

    // Симуляция AI ответа
    setTimeout(() => {
      const aiResponse: AIConversation = {
        id: `ai-${++messageIdRef.current}`,
        type: 'ai',
        message: `Отлично! Я понял, что вы хотите продвинуть ${activeTab === 'track' ? 'трек' : 'артиста'}. Расскажите мне больше:

1. Какой жанр музыки?
2. Какая ваша целевая аудитория?
3. Есть ли у вас артворк/обложка?
4. Какой бюджет вы планируете?`,
        timestamp: new Date()
      };

      setConversations(prev => [...prev, aiResponse]);
      setCurrentStep('questions');
      setIsProcessing(false);
    }, 2000);
  };

  const generateRecommendations = async () => {
    setIsProcessing(true);

    try {
      // Временно используем примеры сервисов
      setRecommendedServices(serviceExamples);
      setShowServices(true);
      setCurrentStep('services');

      const aiResponse: AIConversation = {
        id: `ai-${++messageIdRef.current}`,
        type: 'ai',
        message: 'На основе ваших данных я подобрал оптимальный набор сервисов для достижения цели в 2 миллиона слушателей:',
        timestamp: new Date()
      };

      setConversations(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Error generating recommendations:', error);
      // Fallback к примерам
      setRecommendedServices(serviceExamples);
      setShowServices(true);
      setCurrentStep('services');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-b from-black via-gray-900/50 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-red-900/20 via-transparent to-transparent"></div>

      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-red-500/30 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-red-400/40 rounded-full animate-ping"></div>
        <div className="absolute top-1/2 left-1/3 w-1.5 h-1.5 bg-red-300/20 rounded-full animate-bounce"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10 py-20">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <motion.h1
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-8 leading-tight"
            >
              H!VE <span className="text-red-500 glow-red animate-glow-text">AI</span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-2xl md:text-3xl text-gray-300 max-w-4xl mx-auto mb-4 font-light"
            >
              Умный агент для продвижения музыки
            </motion.p>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="text-lg text-gray-400 max-w-3xl mx-auto"
            >
              Опишите ваш проект, и AI подберет идеальный набор сервисов для достижения ваших целей
            </motion.p>
          </motion.div>

          {/* Artist/Track Toggle */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            className="flex justify-center mb-12"
          >
            <div className="bg-gray-900/70 backdrop-blur-xl rounded-full p-3 border border-gray-600/50 shadow-2xl">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('artist')}
                  className={`px-12 py-5 rounded-full font-bold text-lg transition-all duration-500 flex items-center gap-3 ${
                    activeTab === 'artist'
                      ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-500/30 scale-105'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <User className="w-6 h-6" />
                  Артист
                </button>
                <button
                  onClick={() => setActiveTab('track')}
                  className={`px-12 py-5 rounded-full font-bold text-lg transition-all duration-500 flex items-center gap-3 ${
                    activeTab === 'track'
                      ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-500/30 scale-105'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                >
                  <Music className="w-6 h-6" />
                  Трек
                </button>
              </div>
            </div>
          </motion.div>

          {/* AI Chat Interface */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="bg-gray-900/50 backdrop-blur-xl rounded-3xl border border-gray-600/50 p-10 mb-12 shadow-2xl"
          >
            {/* Conversation History */}
            <div className="mb-6 max-h-60 overflow-y-auto space-y-4">
              <AnimatePresence>
                {conversations.map((conv) => (
                  <motion.div
                    key={conv.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className={`flex ${conv.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                      conv.type === 'user' 
                        ? 'bg-red-500 text-white' 
                        : 'bg-gray-800 text-gray-200 border border-gray-700'
                    }`}>
                      <p className="text-sm whitespace-pre-line">{conv.message}</p>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {isProcessing && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className="bg-gray-800 border border-gray-700 px-4 py-3 rounded-2xl">
                    <div className="flex items-center gap-2">
                      <Sparkles className="w-4 h-4 text-red-400 animate-pulse" />
                      <span className="text-gray-300 text-sm">AI думает...</span>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Input Form */}
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-6">
              <div className="flex-1 relative">
                <div className="relative">
                  <input
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder={`Опишите ваш ${activeTab === 'track' ? 'трек' : 'проект'}... (например: "Мой трек Sacral DJ - New Track в стиле техно, хочу увеличить аудиторию до 2 миллионов")`}
                    className="w-full px-8 py-6 bg-gray-800/70 border-2 border-gray-600/50 rounded-3xl text-white text-lg placeholder-gray-400 focus:outline-none focus:border-red-500 focus:ring-4 focus:ring-red-500/20 transition-all duration-300 shadow-inner"
                    disabled={isProcessing}
                  />
                  <button
                    type="button"
                    className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors p-2 rounded-full hover:bg-gray-700/50"
                    title="Загрузить артворк"
                  >
                    <Upload className="w-6 h-6" />
                  </button>
                </div>

                {/* Подсказки */}
                {!userInput && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 flex flex-wrap gap-2"
                  >
                    <span className="text-gray-500 text-sm">Примеры:</span>
                    <button
                      type="button"
                      onClick={() => setUserInput(`Мой ${activeTab} в стиле техно, хочу 2 миллиона слушателей`)}
                      className="px-3 py-1 bg-gray-800/50 text-gray-400 text-sm rounded-full hover:bg-gray-700/50 hover:text-white transition-colors"
                    >
                      Техно, 2M слушателей
                    </button>
                    <button
                      type="button"
                      onClick={() => setUserInput(`${activeTab === 'track' ? 'Трек' : 'Артист'} в стиле поп, бюджет 100к`)}
                      className="px-3 py-1 bg-gray-800/50 text-gray-400 text-sm rounded-full hover:bg-gray-700/50 hover:text-white transition-colors"
                    >
                      Поп, бюджет 100к
                    </button>
                  </motion.div>
                )}
              </div>

              <button
                type="submit"
                disabled={isProcessing || !userInput.trim()}
                className="px-10 py-6 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 disabled:from-gray-700 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-3xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-lg shadow-red-500/30 hover:shadow-red-500/40 hover:scale-105"
              >
                {isProcessing ? (
                  <>
                    <Sparkles className="w-6 h-6 animate-spin" />
                    Думаю...
                  </>
                ) : (
                  <>
                    <Send className="w-6 h-6" />
                    Начать
                  </>
                )}
              </button>
            </form>
          </motion.div>

          {/* Quick Actions */}
          {currentStep === 'questions' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-center mb-8"
            >
              <button
                onClick={generateRecommendations}
                className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white rounded-2xl font-semibold transition-all duration-300 shadow-lg shadow-red-500/25 flex items-center gap-2"
              >
                <Sparkles className="w-5 h-5" />
                Получить рекомендации
              </button>
            </motion.div>
          )}

          {/* Service Recommendations */}
          {showServices && (
            <ServiceRecommendations
              services={recommendedServices}
              onPurchase={handlePurchase}
            />
          )}
        </div>
      </div>
    </section>
  );
};

export default AIPromoAgent;
