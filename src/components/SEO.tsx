import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  noindex?: boolean;
  nofollow?: boolean;
  canonical?: string;
  alternateLanguages?: Array<{ hreflang: string; href: string }>;
  structuredData?: object;
}

const defaultSEO = {
  title: 'HIVE Agency - Профессиональное продвижение музыки',
  description: 'Комплексное продвижение артистов и треков. Instagram, YouTube, радио, плейлисты. Более 150 успешных кампаний. Гарантированный результат.',
  keywords: 'продвижение музыки, музыкальное агентство, продвижение артистов, продвижение треков, Instagram продвижение, YouTube продвижение, радио ротация, плейлисты Spotify',
  image: '/images/og-image.jpg',
  url: 'https://hiveagency.com',
  type: 'website' as const,
  siteName: 'HIVE Agency',
  locale: 'ru_RU',
  twitterCard: 'summary_large_image' as const,
};

export default function SEO({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  locale = 'ru_RU',
  siteName,
  twitterCard = 'summary_large_image',
  noindex = false,
  nofollow = false,
  canonical,
  alternateLanguages = [],
  structuredData,
}: SEOProps) {
  const seo = {
    title: title ? `${title} | ${defaultSEO.siteName}` : defaultSEO.title,
    description: description || defaultSEO.description,
    keywords: keywords || defaultSEO.keywords,
    image: image || defaultSEO.image,
    url: url || defaultSEO.url,
    type,
    author,
    publishedTime,
    modifiedTime,
    locale,
    siteName: siteName || defaultSEO.siteName,
    twitterCard,
    noindex,
    nofollow,
    canonical: canonical || url || defaultSEO.url,
    alternateLanguages,
    structuredData,
  };

  const robotsContent = [
    seo.noindex ? 'noindex' : 'index',
    seo.nofollow ? 'nofollow' : 'follow',
  ].join(', ');

  return (
    <Head>
      {/* Основные мета-теги */}
      <title>{seo.title}</title>
      <meta name="description" content={seo.description} />
      <meta name="keywords" content={seo.keywords} />
      <meta name="robots" content={robotsContent} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta httpEquiv="Content-Language" content={seo.locale} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={seo.canonical} />
      
      {/* Alternate languages */}
      {seo.alternateLanguages.map((lang) => (
        <link
          key={lang.hreflang}
          rel="alternate"
          hrefLang={lang.hreflang}
          href={lang.href}
        />
      ))}
      
      {/* Open Graph */}
      <meta property="og:type" content={seo.type} />
      <meta property="og:title" content={seo.title} />
      <meta property="og:description" content={seo.description} />
      <meta property="og:image" content={seo.image} />
      <meta property="og:url" content={seo.url} />
      <meta property="og:site_name" content={seo.siteName} />
      <meta property="og:locale" content={seo.locale} />
      
      {seo.author && <meta property="article:author" content={seo.author} />}
      {seo.publishedTime && (
        <meta property="article:published_time" content={seo.publishedTime} />
      )}
      {seo.modifiedTime && (
        <meta property="article:modified_time" content={seo.modifiedTime} />
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={seo.twitterCard} />
      <meta name="twitter:title" content={seo.title} />
      <meta name="twitter:description" content={seo.description} />
      <meta name="twitter:image" content={seo.image} />
      <meta name="twitter:site" content="@hiveagency" />
      <meta name="twitter:creator" content="@hiveagency" />
      
      {/* Дополнительные мета-теги для музыкального агентства */}
      <meta name="theme-color" content="#ef4444" />
      <meta name="msapplication-TileColor" content="#ef4444" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      
      {/* Favicon */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Preconnect для оптимизации */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://images.unsplash.com" />
      
      {/* DNS prefetch */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Structured Data */}
      {seo.structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seo.structuredData),
          }}
        />
      )}
      
      {/* Yandex.Metrica */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
            m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
            (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
            
            ym(95234567, "init", {
              clickmap:true,
              trackLinks:true,
              accurateTrackBounce:true,
              webvisor:true
            });
          `,
        }}
      />
      
      {/* Google Analytics */}
      <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID" />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
          `,
        }}
      />
    </Head>
  );
}
