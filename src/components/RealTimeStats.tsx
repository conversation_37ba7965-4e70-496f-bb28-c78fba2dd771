'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Users,
  Play,
  Eye,
  Heart,
  Share2,
  Music,
  Radio,
  Instagram,
  Youtube,
  BarChart3,
  Target,
  Zap,
  Clock
} from 'lucide-react';

interface RealTimeStatsProps {
  orderId?: string;
  userId?: string;
}

interface StatItem {
  id: string;
  label: string;
  value: number;
  change: number;
  icon: any;
  color: string;
  platform?: string;
}

interface PlatformStats {
  platform: string;
  icon: any;
  color: string;
  stats: {
    plays: number;
    likes: number;
    shares: number;
    followers: number;
  };
  growth: {
    plays: number;
    likes: number;
    shares: number;
    followers: number;
  };
}

export default function RealTimeStats({ orderId, userId }: RealTimeStatsProps) {
  const [stats, setStats] = useState<StatItem[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Симуляция реальных данных
  useEffect(() => {
    const generateStats = () => {
      const baseStats: StatItem[] = [
        {
          id: 'total-plays',
          label: 'Общие прослушивания',
          value: Math.floor(Math.random() * 50000) + 10000,
          change: Math.floor(Math.random() * 20) + 5,
          icon: Play,
          color: 'text-green-400'
        },
        {
          id: 'total-followers',
          label: 'Новые подписчики',
          value: Math.floor(Math.random() * 5000) + 500,
          change: Math.floor(Math.random() * 15) + 3,
          icon: Users,
          color: 'text-blue-400'
        },
        {
          id: 'engagement',
          label: 'Вовлеченность',
          value: Math.floor(Math.random() * 100) + 50,
          change: Math.floor(Math.random() * 10) + 2,
          icon: Heart,
          color: 'text-pink-400'
        },
        {
          id: 'reach',
          label: 'Охват аудитории',
          value: Math.floor(Math.random() * 100000) + 25000,
          change: Math.floor(Math.random() * 25) + 8,
          icon: Target,
          color: 'text-purple-400'
        }
      ];

      const platforms: PlatformStats[] = [
        {
          platform: 'Spotify',
          icon: Music,
          color: 'text-green-400',
          stats: {
            plays: Math.floor(Math.random() * 20000) + 5000,
            likes: Math.floor(Math.random() * 2000) + 500,
            shares: Math.floor(Math.random() * 500) + 100,
            followers: Math.floor(Math.random() * 1000) + 200
          },
          growth: {
            plays: Math.floor(Math.random() * 20) + 5,
            likes: Math.floor(Math.random() * 15) + 3,
            shares: Math.floor(Math.random() * 10) + 2,
            followers: Math.floor(Math.random() * 12) + 4
          }
        },
        {
          platform: 'YouTube',
          icon: Youtube,
          color: 'text-red-400',
          stats: {
            plays: Math.floor(Math.random() * 15000) + 3000,
            likes: Math.floor(Math.random() * 1500) + 300,
            shares: Math.floor(Math.random() * 300) + 50,
            followers: Math.floor(Math.random() * 800) + 150
          },
          growth: {
            plays: Math.floor(Math.random() * 18) + 4,
            likes: Math.floor(Math.random() * 12) + 2,
            shares: Math.floor(Math.random() * 8) + 1,
            followers: Math.floor(Math.random() * 10) + 3
          }
        },
        {
          platform: 'Instagram',
          icon: Instagram,
          color: 'text-pink-400',
          stats: {
            plays: Math.floor(Math.random() * 10000) + 2000,
            likes: Math.floor(Math.random() * 3000) + 800,
            shares: Math.floor(Math.random() * 800) + 200,
            followers: Math.floor(Math.random() * 1200) + 300
          },
          growth: {
            plays: Math.floor(Math.random() * 15) + 3,
            likes: Math.floor(Math.random() * 20) + 5,
            shares: Math.floor(Math.random() * 12) + 3,
            followers: Math.floor(Math.random() * 15) + 4
          }
        },
        {
          platform: 'Radio',
          icon: Radio,
          color: 'text-blue-400',
          stats: {
            plays: Math.floor(Math.random() * 50000) + 10000,
            likes: Math.floor(Math.random() * 1000) + 200,
            shares: Math.floor(Math.random() * 200) + 50,
            followers: Math.floor(Math.random() * 500) + 100
          },
          growth: {
            plays: Math.floor(Math.random() * 25) + 8,
            likes: Math.floor(Math.random() * 8) + 2,
            shares: Math.floor(Math.random() * 5) + 1,
            followers: Math.floor(Math.random() * 6) + 2
          }
        }
      ];

      setStats(baseStats);
      setPlatformStats(platforms);
      setIsLoading(false);
    };

    generateStats();

    // Обновление каждые 30 секунд
    const interval = setInterval(() => {
      generateStats();
    }, 30000);

    return () => clearInterval(interval);
  }, [orderId, userId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Overall Stats */}
      <div>
        <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
          <BarChart3 className="w-6 h-6 text-purple-400" />
          Общая статистика
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-xl border border-gray-700/30 p-6 hover:scale-105 transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
                <div className="flex items-center gap-1 text-green-400 text-sm">
                  <TrendingUp className="w-4 h-4" />
                  +{stat.change}%
                </div>
              </div>
              
              <div className="text-2xl font-bold text-white mb-1">
                {stat.value.toLocaleString()}
              </div>
              
              <div className="text-gray-400 text-sm">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Platform Stats */}
      <div>
        <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
          <Zap className="w-6 h-6 text-purple-400" />
          Статистика по платформам
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {platformStats.map((platform, index) => (
            <motion.div
              key={platform.platform}
              initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl rounded-xl border border-gray-700/30 p-6"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className={`w-10 h-10 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center`}>
                  <platform.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-bold text-white">{platform.platform}</h4>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Прослушивания</span>
                    <div className="text-right">
                      <div className="text-white font-semibold">
                        {platform.stats.plays.toLocaleString()}
                      </div>
                      <div className="text-green-400 text-xs">
                        +{platform.growth.plays}%
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Лайки</span>
                    <div className="text-right">
                      <div className="text-white font-semibold">
                        {platform.stats.likes.toLocaleString()}
                      </div>
                      <div className="text-green-400 text-xs">
                        +{platform.growth.likes}%
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Репосты</span>
                    <div className="text-right">
                      <div className="text-white font-semibold">
                        {platform.stats.shares.toLocaleString()}
                      </div>
                      <div className="text-green-400 text-xs">
                        +{platform.growth.shares}%
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Подписчики</span>
                    <div className="text-right">
                      <div className="text-white font-semibold">
                        {platform.stats.followers.toLocaleString()}
                      </div>
                      <div className="text-green-400 text-xs">
                        +{platform.growth.followers}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mt-4 pt-4 border-t border-gray-700/50">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400 text-sm">Прогресс кампании</span>
                  <span className="text-white text-sm font-semibold">
                    {Math.floor(Math.random() * 40) + 60}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.floor(Math.random() * 40) + 60}%` }}
                    transition={{ duration: 1, delay: index * 0.2 }}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Live Updates Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="flex items-center justify-center gap-2 text-gray-400 text-sm"
      >
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        Обновление в реальном времени
        <Clock className="w-4 h-4" />
      </motion.div>
    </div>
  );
}
