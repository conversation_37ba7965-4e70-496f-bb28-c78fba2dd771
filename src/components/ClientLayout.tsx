'use client';

import { ReactNode } from 'react';
import { AuthProvider } from "@/contexts/AuthContext";
import { LanguageProvider } from "@/contexts/LanguageContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

interface ClientLayoutProps {
  children: ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <LanguageProvider>
      <AuthProvider>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </AuthProvider>
    </LanguageProvider>
  );
}
