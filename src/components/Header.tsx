'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, Search, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import SearchModal from '@/components/SearchModal';
import ProfileSettings from '@/components/ProfileSettings';


const Header = () => {
  const { user, logout } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isProfileSettingsOpen, setIsProfileSettingsOpen] = useState(false);
  const [language] = useState('ru');

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  // Горячие клавиши
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrl<PERSON>ey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(true);
      }
      if (e.key === 'Escape') {
        setIsSearchOpen(false);
        setIsProfileSettingsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);


  const menuItems = [
    { href: '#about', label: language === 'ru' ? 'О нас' : 'About' },
    { href: '#services', label: language === 'ru' ? 'Услуги' : 'Services' },
    { href: '/pricing', label: language === 'ru' ? 'Тарифы' : 'Pricing' },
    { href: '#partners', label: language === 'ru' ? 'Партнеры' : 'Partners' },
    { href: '#team', label: language === 'ru' ? 'Команда' : 'Team' },
    { href: '#contacts', label: language === 'ru' ? 'Контакты' : 'Contacts' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl">
      <div className="container-wide py-8">
        <div className="flex items-center justify-between">
          {/* Logo - Modern Style */}
          <Link href="/" className="flex items-center gap-2 text-2xl font-bold transition-all duration-300 hover:scale-105 group">
            <div className="relative">
              {/* H!VE Logo with modern styling */}
              <div className="text-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 bg-clip-text font-black text-3xl tracking-wider" style={{ fontFamily: 'var(--font-space-grotesk)' }}>
                <span className="inline-block border-2 border-purple-500 px-6 py-4 rounded-lg bg-purple-500/10 hover:bg-purple-500/20 transition-all duration-300 shadow-lg shadow-purple-500/20">
                  H!VE
                </span>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation - Red Bull Style with Rounded Container */}
          <nav className="hidden md:flex">
            <div className="flex items-center bg-white/10 backdrop-blur-md border border-white/20 rounded-full" style={{ gap: '40px', padding: '20px 48px' }}>
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-white hover:text-purple-400 transition-all duration-300 font-medium text-sm uppercase tracking-wide relative group py-2 px-4"
                >
                  {item.label}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 group-hover:w-full"></span>
                </Link>
              ))}
            </div>
          </nav>

          {/* Right Side Actions - Red Bull Style */}
          <div className="flex items-center" style={{ gap: '30px' }}>
            {/* Search Icon */}
            <button
              onClick={() => setIsSearchOpen(true)}
              className="text-white hover:text-purple-400 transition-colors duration-300 p-3"
              title="Поиск (Ctrl+K)"
            >
              <Search className="w-6 h-6" />
            </button>

            {/* User Profile Icon */}
            {user ? (
              <div className="relative">
                <Link
                  href="/dashboard"
                  className="text-white hover:text-purple-400 transition-colors duration-300 p-3 flex items-center"
                  style={{ gap: '8px' }}
                  title="Перейти в дашборд"
                >
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-6 h-6" />
                  )}
                  <span className="hidden md:block text-sm">{user.name}</span>
                </Link>
              </div>
            ) : (
              <button
                onClick={() => setIsProfileSettingsOpen(true)}
                className="text-white hover:text-purple-400 transition-colors duration-300 p-3"
                title="Профиль"
              >
                <User className="w-6 h-6" />
              </button>
            )}



            {/* Auth Buttons */}
            {user ? (
              <div className="hidden md:flex items-center" style={{ gap: '15px' }}>
                <Link
                  href="/admin"
                  className="px-4 py-2 border border-purple-500/50 text-purple-400 rounded-lg hover:bg-purple-500/10 hover:border-purple-500 transition-all duration-300 font-medium"
                >
                  Админ
                </Link>
                <button
                  onClick={logout}
                  className="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-lg shadow-purple-500/30"
                  title="Выйти"
                >
                  <span className="text-xs font-bold">OUT</span>
                </button>
              </div>
            ) : (
              <div className="hidden md:flex items-center" style={{ gap: '15px' }}>
                <Link
                  href="/register"
                  className="px-4 py-2 border border-purple-500/50 text-purple-400 rounded-lg hover:bg-purple-500/10 hover:border-purple-500 transition-all duration-300 font-medium"
                >
                  {language === 'ru' ? 'Регистрация' : 'Sign Up'}
                </Link>
                <Link
                  href="/login"
                  className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-lg shadow-blue-500/30"
                >
                  <span className="text-xs font-bold">GO</span>
                </Link>
              </div>
            )}

            {/* Mobile Menu Toggle */}
            <button
              onClick={toggleMenu}
              className="md:hidden text-white hover:text-purple-500 transition-all duration-300 p-2 rounded-lg hover:bg-purple-500/10"
            >
              {isMenuOpen ? <X size={28} /> : <Menu size={28} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-purple-500/20">
            <div className="flex flex-col pt-4" style={{ gap: '20px' }}>
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-white hover:text-purple-500 transition-colors duration-300 font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="flex flex-col pt-2" style={{ gap: '15px' }}>
                <Link
                  href="/register"
                  className="border border-purple-500/50 text-purple-400 px-6 py-2 rounded-full transition-colors duration-300 font-medium text-center hover:bg-purple-500/10"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {language === 'ru' ? 'Регистрация' : 'Sign Up'}
                </Link>
                <Link
                  href="/login"
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-6 py-2 rounded-full transition-colors duration-300 font-medium text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {language === 'ru' ? 'Войти' : 'Login'}
                </Link>
              </div>
            </div>
          </nav>
        )}

        {/* Search Modal */}
        <SearchModal
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
        />

        {/* Profile Settings Modal */}
        <ProfileSettings
          isOpen={isProfileSettingsOpen}
          onClose={() => setIsProfileSettingsOpen(false)}
        />
      </div>
    </header>
  );
};

export default Header;
