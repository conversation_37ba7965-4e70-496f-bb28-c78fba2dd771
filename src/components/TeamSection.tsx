'use client';

import { useState } from 'react';
import { FaInstagram, FaLinkedin, FaTwitter } from 'react-icons/fa';

const TeamSection = () => {
  const [language] = useState('ru'); // This would come from context in real app

  const teamMembers = [
    {
      id: 1,
      name: 'Алексей Петров',
      position: language === 'ru' ? 'Генеральный директор' : 'CEO',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? '15 лет опыта в музыкальной индустрии. Работал с крупнейшими артистами России.'
        : '15 years of experience in the music industry. Worked with the biggest artists in Russia.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
    {
      id: 2,
      name: '<PERSON>а<PERSON>ия Иванова',
      position: language === 'ru' ? 'Директор по маркетингу' : 'Marketing Director',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? 'Эксперт в области цифрового маркетинга и продвижения в социальных сетях.'
        : 'Expert in digital marketing and social media promotion.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
    {
      id: 3,
      name: 'Дмитрий Сидоров',
      position: language === 'ru' ? 'Директор по развитию' : 'Business Development Director',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? 'Отвечает за партнерские отношения с лейблами и радиостанциями.'
        : 'Responsible for partnerships with labels and radio stations.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
    {
      id: 4,
      name: 'Анна Козлова',
      position: language === 'ru' ? 'PR-менеджер' : 'PR Manager',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? 'Специалист по связям с общественностью и медиа-отношениям.'
        : 'Public relations and media relations specialist.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
    {
      id: 5,
      name: 'Сергей Волков',
      position: language === 'ru' ? 'Музыкальный продюсер' : 'Music Producer',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? 'Работает с артистами над созданием и продвижением музыкального контента.'
        : 'Works with artists on creating and promoting musical content.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
    {
      id: 6,
      name: 'Елена Морозова',
      position: language === 'ru' ? 'Аналитик' : 'Data Analyst',
      photo: '/api/placeholder/300/300',
      bio: language === 'ru' 
        ? 'Анализирует эффективность кампаний и предоставляет детальную отчетность.'
        : 'Analyzes campaign effectiveness and provides detailed reporting.',
      social_links: {
        instagram: 'https://instagram.com',
        linkedin: 'https://linkedin.com',
        twitter: 'https://twitter.com',
      },
    },
  ];

  return (
    <section id="team" className="py-16 md:py-32 bg-gray-900">
      <div className="container-wide">
        {/* Header */}
        <div className="text-center mb-12 md:mb-24 max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-6 md:mb-10 text-center leading-tight">
            {language === 'ru' ? 'Наша команда' : 'Our Team'}
          </h2>
          <div className="w-32 md:w-40 h-1 md:h-1.5 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16 rounded-full"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-5xl mx-auto leading-relaxed font-light">
            {language === 'ru'
              ? 'Профессионалы с многолетним опытом в музыкальной индустрии, готовые воплотить ваши амбиции в реальность'
              : 'Professionals with years of experience in the music industry, ready to turn your ambitions into reality'
            }
          </p>
        </div>

        {/* Team Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-30 max-w-7xl mx-auto">
          {teamMembers.map((member) => (
            <div
              key={member.id}
              className="card-monochrome rounded-3xl overflow-hidden group"
            >
              <div className="relative z-10">
                {/* Photo */}
                <div className="relative overflow-hidden">
                  <div className="w-full h-64 md:h-80 bg-gray-800/50 border border-gray-700/50 flex items-center justify-center group-hover:border-red-500/30 transition-all duration-300">
                    <span className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">Photo</span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content */}
                <div className="card-padding">
                  <h3 className="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-red-50 transition-colors duration-300">{member.name}</h3>
                  <p className="text-gray-400 font-semibold mb-4 text-sm md:text-base group-hover:text-red-400 transition-colors duration-300">{member.position}</p>
                  <p className="text-gray-400 text-xs md:text-sm mb-6 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">{member.bio}</p>

                  {/* Social Links */}
                  <div className="flex space-x-3">
                    {member.social_links.instagram && (
                      <a
                        href={member.social_links.instagram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-red-400 transition-colors duration-300 p-2 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-red-500/30"
                      >
                        <FaInstagram size={16} />
                      </a>
                    )}
                    {member.social_links.linkedin && (
                      <a
                        href={member.social_links.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-red-400 transition-colors duration-300 p-2 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-red-500/30"
                      >
                        <FaLinkedin size={16} />
                      </a>
                    )}
                    {member.social_links.twitter && (
                      <a
                        href={member.social_links.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-red-400 transition-colors duration-300 p-2 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-red-500/30"
                      >
                        <FaTwitter size={16} />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Join Team CTA */}
        <div className="text-center mt-16 card-monochrome rounded-3xl p-8 md:p-12 group">
          <div className="relative z-10">
            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-6 group-hover:text-red-50 transition-colors duration-300">
              {language === 'ru'
                ? 'Присоединяйтесь к нашей команде!'
                : 'Join our team!'
              }
            </h3>
            <p className="text-lg md:text-xl text-gray-400 mb-8 max-w-2xl mx-auto group-hover:text-gray-300 transition-colors duration-300">
              {language === 'ru'
                ? 'Мы всегда ищем талантливых профессионалов для расширения нашей команды'
                : 'We are always looking for talented professionals to expand our team'
              }
            </p>
            <button className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-red-500 hover:to-red-600 text-white px-6 md:px-8 py-3 md:py-4 rounded-xl font-semibold text-base md:text-lg transition-all duration-300 border border-gray-600/50 hover:border-red-500/50 hover:shadow-lg hover:shadow-red-500/25">
              {language === 'ru' ? 'Отправить резюме' : 'Send Resume'}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TeamSection;
