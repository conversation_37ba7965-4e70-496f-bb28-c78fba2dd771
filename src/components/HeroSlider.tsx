'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play } from 'lucide-react';

interface CaseStudy {
  id: number;
  title: string;
  artist: string;
  description: string;
  results: string;
  image: string;
  video?: string;
}

const HeroSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [language] = useState('ru'); // This would come from context in real app

  const caseStudies: CaseStudy[] = [
    {
      id: 1,
      title: language === 'ru' ? 'Продвижение на радио' : 'Radio Promotion',
      artist: 'ARTIST NAME',
      description: language === 'ru' 
        ? 'Успешное продвижение трека на топ радиостанциях России' 
        : 'Successful track promotion on top Russian radio stations',
      results: language === 'ru' ? '+2.5M прослушиваний' : '+2.5M plays',
      image: '/api/placeholder/1920/1080',
    },
    {
      id: 2,
      title: language === 'ru' ? 'Социальные сети' : 'Social Media',
      artist: 'ANOTHER ARTIST',
      description: language === 'ru' 
        ? 'Вирусная кампания в TikTok и Instagram' 
        : 'Viral campaign on TikTok and Instagram',
      results: language === 'ru' ? '+10M просмотров' : '+10M views',
      image: '/api/placeholder/1920/1080',
    },
    {
      id: 3,
      title: language === 'ru' ? 'ТВ реклама' : 'TV Advertising',
      artist: 'THIRD ARTIST',
      description: language === 'ru' 
        ? 'Размещение на федеральных телеканалах' 
        : 'Placement on federal TV channels',
      results: language === 'ru' ? '+50% узнаваемости' : '+50% recognition',
      image: '/api/placeholder/1920/1080',
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % caseStudies.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [caseStudies.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % caseStudies.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + caseStudies.length) % caseStudies.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section className="relative h-screen overflow-hidden bg-black cyber-grid">
      {/* Floating particles */}
      <div className="absolute inset-0 z-5">
        {[...Array(20)].map((_, i) => {
          // Фиксированные позиции для избежания hydration ошибок
          const positions = [
            { left: 10, top: 20, delay: 0, duration: 3 },
            { left: 80, top: 15, delay: 0.5, duration: 4 },
            { left: 25, top: 70, delay: 1, duration: 3.5 },
            { left: 90, top: 60, delay: 1.5, duration: 4.5 },
            { left: 15, top: 45, delay: 2, duration: 3.2 },
            { left: 70, top: 25, delay: 0.3, duration: 4.2 },
            { left: 45, top: 80, delay: 1.2, duration: 3.8 },
            { left: 85, top: 35, delay: 0.8, duration: 4.1 },
            { left: 30, top: 10, delay: 1.8, duration: 3.6 },
            { left: 60, top: 90, delay: 0.2, duration: 4.3 },
            { left: 5, top: 65, delay: 1.4, duration: 3.4 },
            { left: 95, top: 40, delay: 0.7, duration: 4.7 },
            { left: 40, top: 5, delay: 1.6, duration: 3.9 },
            { left: 75, top: 75, delay: 0.4, duration: 4.4 },
            { left: 20, top: 55, delay: 1.1, duration: 3.7 },
            { left: 65, top: 30, delay: 0.9, duration: 4.6 },
            { left: 35, top: 85, delay: 1.7, duration: 3.3 },
            { left: 55, top: 50, delay: 0.6, duration: 4.8 },
            { left: 12, top: 35, delay: 1.3, duration: 3.1 },
            { left: 88, top: 70, delay: 0.1, duration: 4.9 }
          ];

          const pos = positions[i] || positions[0];

          return (
            <div
              key={i}
              className="absolute w-1 h-1 bg-red-500 rounded-full animate-float opacity-30"
              style={{
                left: `${pos.left}%`,
                top: `${pos.top}%`,
                animationDelay: `${pos.delay}s`,
                animationDuration: `${pos.duration}s`,
              }}
            />
          );
        })}
      </div>

      {/* Background Slides */}
      {caseStudies.map((study, index) => (
        <div
          key={study.id}
          className={`absolute inset-0 transition-all duration-1000 ${
            index === currentSlide ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url(${study.image})`,
            }}
          />
          <div className="absolute inset-0 bg-black/50" />
          <div className="absolute inset-0 holographic opacity-10" />
        </div>
      ))}

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container-wide">
          <div className="max-w-4xl">
            {caseStudies.map((study, index) => (
              <div
                key={study.id}
                className={`transition-all duration-1000 ${
                  index === currentSlide
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                {index === currentSlide && (
                  <>
                    <div className="mb-4">
                      <span className="inline-block bg-red-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                        {language === 'ru' ? 'Кейс' : 'Case Study'}
                      </span>
                    </div>
                    
                    <h1 className="heading-xl text-white mb-6 leading-tight animate-fadeInUp">
                      {study.title}
                    </h1>
                    
                    <div className="text-2xl md:text-3xl text-red-500 font-semibold mb-6 animate-slideInLeft">
                      {study.artist}
                    </div>

                    <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl text-body animate-slideInRight">
                      {study.description}
                    </p>

                    <div className="text-3xl md:text-4xl font-bold text-white mb-8 gradient-text animate-pulse-custom">
                      {study.results}
                    </div>
                    
                    <div className="flex flex-col sm:flex-row animate-fadeInUp" style={{ gap: '25px' }}>
                      <button className="btn-primary text-lg flex items-center justify-center gap-3 animate-glow">
                        <Play />
                        {language === 'ru' ? 'Смотреть кейс' : 'View Case'}
                      </button>

                      <button className="btn-secondary text-lg">
                        {language === 'ru' ? 'Начать проект' : 'Start Project'}
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors duration-300"
      >
        <ChevronLeft size={20} />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors duration-300"
      >
        <ChevronRight size={20} />
      </button>

      {/* Dots Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex" style={{ gap: '15px' }}>
        {caseStudies.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-colors duration-300 ${
              index === currentSlide ? 'bg-red-500' : 'bg-white/50'
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSlider;
