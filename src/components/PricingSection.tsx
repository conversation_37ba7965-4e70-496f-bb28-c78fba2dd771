'use client';

import { useState } from 'react';
import { Crown, Rocket, Check, Music, CreditCard, Bitcoin, Smartphone, Zap, Users, BarChart3, Shield } from 'lucide-react';
import { FaStar, FaUser } from 'react-icons/fa';
import Link from 'next/link';
import { PRICING_TIERS } from '@/config/pricing';
import { PricingTier } from '@/types/pricing';
import PaymentModal from '@/components/PaymentModal';
import { useAuth } from '@/contexts/AuthContext';

const PricingSection = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'artist' | 'track'>('artist');
  const [selectedTier, setSelectedTier] = useState<PricingTier | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [language] = useState('ru');

  const artistPlans = [
    {
      id: 'artist-starter',
      name: 'Стартер',
      price: 15000,
      period: '/месяц',
      icon: <FaStar className="text-3xl text-yellow-500" />,
      popular: false,
      description: 'Комплексное продвижение для начинающих артистов',
      features: [
        '📹 Создание и продвижение видео на YouTube',
        '📸 Ведение Instagram аккаунта',
        '🎵 Создание и ведение страниц ВКонтакте',
        '📧 Email рассылка по базе артистов (5000+ контактов)',
        '🏢 Рассылка по клубам и промоутерам (200+ контактов)',
        '💬 Создание и ведение группы в Telegram',
        '🎼 Страницы в музыкальных соц. сетях (Last.fm, Bandcamp)',
        '💬 Продвижение на музыкальных форумах',
        '📊 Еженедельная аналитика и отчеты',
      ],
      benefits: [
        '🚀 Рост аудитории на 300-500% за месяц',
        '💰 Увеличение доходов от стриминга на 200%',
        '🎯 Таргетированная аудитория по интересам',
        '📈 Профессиональная статистика роста',
      ],
    },
    {
      id: 'artist-pro',
      name: 'Профи',
      price: 35000,
      period: '/месяц',
      icon: <Crown className="text-3xl text-purple-500" />,
      popular: true,
      description: 'Максимальное продвижение для серьезных артистов',
      features: [
        '🎬 Профессиональные видеоклипы для YouTube',
        '✨ Premium контент для Instagram + Stories',
        '🎵 Полное ведение ВКонтакте + реклама',
        '📧 Персонализированные email кампании (15000+ контактов)',
        '🏢 VIP рассылка по премиум клубам (500+ контактов)',
        '💬 Активное комьюнити в Telegram с розыгрышами',
        '🎼 Продвижение на всех музыкальных платформах',
        '🌐 SEO продвижение на музыкальных форумах',
        '📊 Ежедневная детальная аналитика',
        '🎯 Персональный менеджер',
        '📻 Продвижение на радио (50+ станций)',
      ],
      benefits: [
        '🚀 Рост аудитории на 500-1000% за месяц',
        '💰 Увеличение доходов от стриминга на 400%',
        '🎯 Премиум таргетинг и ретаргетинг',
        '📈 Профессиональная аналитика + прогнозы',
        '🏆 Гарантированное попадание в топ-чарты',
      ],
    },
    {
      id: 'artist-premium',
      name: 'Звезда',
      price: 75000,
      period: '/месяц',
      icon: <Rocket className="text-3xl text-red-500" />,
      popular: false,
      description: 'VIP продвижение для звезд и топ-артистов',
      features: [
        '🎬 Профессиональные клипы + режиссерская работа',
        '✨ Эксклюзивный контент для всех соц. сетей',
        '🎵 Полное управление всеми музыкальными платформами',
        '📧 Масштабные email кампании (50000+ контактов)',
        '🏢 Эксклюзивные контракты с топ-клубами',
        '💬 Фан-клуб в Telegram с эксклюзивным контентом',
        '🎼 Размещение на всех мировых музыкальных платформах',
        '🌐 Глобальное SEO продвижение',
        '📊 Аналитика в реальном времени + AI прогнозы',
        '👨‍💼 Команда экспертов (менеджер, SMM, PR)',
        '📻 Продвижение на радио (200+ станций)',
        '📺 Размещение на ТВ и в медиа',
      ],
      benefits: [
        '🚀 Рост аудитории на 1000%+ за месяц',
        '💰 Увеличение доходов от стриминга на 800%',
        '🎯 Глобальный таргетинг по всему миру',
        '📈 AI-аналитика + персональные рекомендации',
        '🏆 Гарантированное попадание в топ-10 чартов',
        '🌟 Статус верифицированного артиста',
      ],
    },
  ];

  const trackPlans = [
    {
      id: 'track-basic',
      name: 'SMM Старт',
      price: 8000,
      period: '/трек',
      icon: <Music className="text-3xl text-blue-500" />,
      popular: false,
      description: 'Комплексное SMM продвижение трека',
      features: [
        '📱 Создание страниц ВК/Instagram/YouTube/TikTok',
        '🎵 Релиз на Sacral Track с увеличенными роялти',
        '🤖 Threads бот: ежедневные посты с треком',
        '📊 Автоматический сбор статистики прослушиваний',
        '💰 Отслеживание роялти в реальном времени',
        '📈 Еженедельные отчеты по росту',
        '🎯 Базовый таргетинг аудитории',
        '💬 Создание фан-сообщества',
      ],
      benefits: [
        '🚀 Рост прослушиваний на 500% за месяц',
        '💰 Увеличение роялти на 300%',
        '📱 Автоматизация SMM на 80%',
        '📊 Детальная аналитика роста',
      ],
    },
    {
      id: 'track-advanced',
      name: 'SMM Про',
      price: 18000,
      period: '/трек',
      icon: <Crown className="text-3xl text-purple-500" />,
      popular: true,
      description: 'Профессиональное SMM + вирусное продвижение',
      features: [
        '📱 Премиум ведение всех соц. сетей + контент',
        '🎵 Эксклюзивный релиз на Sacral Track + бонусы',
        '🤖 AI Threads бот: умные посты + взаимодействие',
        '📊 Продвинутая аналитика + прогнозы',
        '💰 Максимизация роялти через все платформы',
        '📈 Ежедневные детальные отчеты',
        '🎯 Премиум таргетинг + ретаргетинг',
        '💬 Активное комьюнити + конкурсы',
        '🎬 Создание вирусного контента',
        '📻 Продвижение на радио (30+ станций)',
      ],
      benefits: [
        '🚀 Рост прослушиваний на 1000% за месяц',
        '💰 Увеличение роялти на 600%',
        '📱 Полная автоматизация SMM',
        '📊 AI-аналитика + персональные рекомендации',
        '🏆 Гарантированное попадание в тренды',
      ],
    },
    {
      id: 'track-viral',
      name: 'SMM Звезда',
      price: 35000,
      period: '/трек',
      icon: <Rocket className="text-3xl text-red-500" />,
      popular: false,
      description: 'Максимальное вирусное продвижение',
      features: [
        '📱 Эксклюзивное ведение всех платформ + креативы',
        '🎵 VIP релиз на Sacral Track + максимальные роялти',
        '🤖 Продвинутый AI бот: вирусные кампании',
        '📊 Аналитика в реальном времени + AI прогнозы',
        '💰 Оптимизация роялти через все мировые платформы',
        '📈 Персональный дашборд с live-статистикой',
        '🎯 Глобальный таргетинг по всему миру',
        '💬 Международное фан-сообщество',
        '🎬 Профессиональные вирусные кампании',
        '📻 Продвижение на радио (100+ станций)',
        '📺 Размещение на ТВ и в медиа',
        '🌟 Коллаборации с топ-блогерами',
      ],
      benefits: [
        '🚀 Рост прослушиваний на 2000%+ за месяц',
        '💰 Увеличение роялти на 1000%',
        '📱 Полная автоматизация + AI управление',
        '📊 Предиктивная аналитика + стратегии',
        '🏆 Гарантированное попадание в топ-чарты',
        '🌟 Международное признание',
      ],
    },
  ];

  const currentPlans = activeTab === 'artist' ? artistPlans : trackPlans;

  return (
    <section id="pricing" className="py-16 md:py-32 bg-black">
      <div className="container-wide">
        <div className="text-center mb-12 md:mb-24 max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-6 md:mb-10 text-center leading-tight">
            Тарифы
          </h2>
          <div className="w-32 md:w-40 h-1 md:h-1.5 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16 rounded-full"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-5xl mx-auto mb-8 md:mb-16 leading-relaxed font-light">
            Выберите подходящий план для продвижения вашей музыки
          </p>

          {/* Tab Switcher */}
          <div className="flex justify-center" style={{ marginBottom: '30px' }}>
            <div className="bg-gray-800/80 rounded-full flex shadow-2xl border border-gray-700/50" style={{ padding: '20px' }}>
              <button
                onClick={() => setActiveTab('artist')}
                className={`rounded-full font-bold text-lg transition-all duration-300 ${
                  activeTab === 'artist'
                    ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg transform scale-105'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                }`}
                style={{ padding: '20px 30px', marginRight: '10px' }}
              >
                <FaUser className="inline text-xl" style={{ marginRight: '15px' }} />
                Продвижение артиста (PR, промоушен)
              </button>
              <button
                onClick={() => setActiveTab('track')}
                className={`rounded-full font-bold text-lg transition-all duration-300 ${
                  activeTab === 'track'
                    ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg transform scale-105'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                }`}
                style={{ padding: '20px 30px', marginLeft: '10px' }}
              >
                <Music className="inline text-xl" style={{ marginRight: '15px' }} />
                Продвижение трека (SMM)
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 max-w-7xl mx-auto" style={{ gap: '30px' }}>
          {currentPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-gray-800 rounded-3xl group hover:bg-gray-700 transition-all duration-500 hover:scale-105 shadow-2xl h-full flex flex-col ${
                plan.popular ? 'ring-2 ring-red-500 scale-105 shadow-red-500/20' : ''
              }`}
              style={{ padding: '30px' }}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-full text-sm font-bold shadow-lg">
                    🔥 ПОПУЛЯРНЫЙ
                  </span>
                </div>
              )}

              <div className="text-center" style={{ marginBottom: '30px' }}>
                <div className="inline-flex items-center justify-center w-24 h-24 bg-gray-700/50 rounded-full group-hover:bg-red-500/20 transition-all duration-300" style={{ marginBottom: '20px' }}>
                  <div className="transform group-hover:scale-110 transition-transform duration-300 text-4xl">{plan.icon}</div>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>{plan.name}</h3>
                <p className="text-gray-300 leading-relaxed text-lg" style={{ marginBottom: '20px', lineHeight: '1.6' }}>{plan.description}</p>
                <div className="text-4xl md:text-5xl font-bold text-white bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent" style={{ marginBottom: '10px', lineHeight: '1.2' }}>
                  {plan.price.toLocaleString('ru-RU')} ₽
                </div>
                <div className="text-gray-300 text-lg font-medium" style={{ lineHeight: '1.4' }}>{plan.period}</div>
              </div>

              {/* Features */}
              <div className="mb-10">
                <h4 className="text-xl font-bold text-white mb-6 text-center">Что входит:</h4>
                <ul style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start text-gray-200 text-base" style={{ gap: '15px' }}>
                      <Check className="w-6 h-6 text-green-400 flex-shrink-0 mt-0.5" />
                      <span className="leading-relaxed" style={{ lineHeight: '1.6' }}>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Benefits */}
              <div className="mb-10">
                <h4 className="text-xl font-bold text-white mb-6 text-center">Преимущества:</h4>
                <ul style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  {plan.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start text-gray-200 text-base" style={{ gap: '15px' }}>
                      <div className="w-6 h-6 bg-red-500 rounded-full flex-shrink-0 mt-0.5 flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                      <span className="font-medium leading-relaxed" style={{ lineHeight: '1.6' }}>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div style={{ marginTop: '30px' }}>
                <Link
                  href="/register"
                  className={`block w-full text-center rounded-full font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg ${
                    plan.popular
                      ? 'bg-red-500 hover:bg-red-600 text-white shadow-red-500/30'
                      : 'bg-gray-700 hover:bg-gray-600 text-white'
                  }`}
                  style={{ padding: '20px 30px', lineHeight: '1.4' }}
                >
                  Выбрать план
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center" style={{ marginTop: '30px' }}>
          <p className="text-gray-400" style={{ marginBottom: '20px', lineHeight: '1.6' }}>
            Нужен индивидуальный план?
          </p>
          <Link
            href="/register"
            className="inline-flex items-center text-red-500 hover:text-red-400 font-semibold"
            style={{ lineHeight: '1.4' }}
          >
            Связаться с нами
          </Link>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
