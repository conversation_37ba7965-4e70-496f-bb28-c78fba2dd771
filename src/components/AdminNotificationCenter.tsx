'use client';

import { useState, useEffect } from 'react';
import { 
  Bell, 
  X, 
  Check, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  DollarSign,
  Users,
  Package,
  MessageSquare,
  Settings,
  Shield,
  TrendingUp,
  Clock,
  Star,
  Mail,
  Phone,
  CreditCard,
  RefreshCw,
  Eye,
  Trash2,
  MoreHorizontal
} from 'lucide-react';

export interface AdminNotification {
  id: string;
  type: 'order' | 'user' | 'payment' | 'system' | 'security' | 'review' | 'message' | 'analytics';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

const mockNotifications: AdminNotification[] = [
  {
    id: '1',
    type: 'order',
    priority: 'high',
    title: 'Новый заказ на продвижение',
    message: 'Заказ #12345 от пользователя "ArtistName" на продвижение трека в Instagram',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    actionUrl: '/admin/orders/12345',
    metadata: { orderId: '12345', amount: 5000, service: 'Instagram Promotion' }
  },
  {
    id: '2',
    type: 'payment',
    priority: 'critical',
    title: 'Неудачная оплата',
    message: 'Платеж на сумму 15,000₽ отклонен банком. Требуется связаться с клиентом',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
    actionUrl: '/admin/payments/failed',
    metadata: { amount: 15000, reason: 'insufficient_funds' }
  },
  {
    id: '3',
    type: 'user',
    priority: 'medium',
    title: 'Новая регистрация',
    message: 'Зарегистрировался новый пользователь: <EMAIL>',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: false,
    actionUrl: '/admin/users',
    metadata: { userType: 'producer', email: '<EMAIL>' }
  },
  {
    id: '4',
    type: 'review',
    priority: 'medium',
    title: 'Новый отзыв',
    message: 'Получен 5-звездочный отзыв о продвижении на YouTube',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    read: true,
    metadata: { rating: 5, service: 'YouTube Promotion' }
  },
  {
    id: '5',
    type: 'system',
    priority: 'low',
    title: 'Обновление системы',
    message: 'Запланировано техническое обслуживание на 02:00 - 04:00',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: true,
    metadata: { maintenanceStart: '02:00', maintenanceEnd: '04:00' }
  },
  {
    id: '6',
    type: 'security',
    priority: 'high',
    title: 'Подозрительная активность',
    message: 'Обнаружены множественные попытки входа с разных IP-адресов',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    read: false,
    actionUrl: '/admin/security/logs',
    metadata: { attempts: 15, ips: ['***********', '********'] }
  },
  {
    id: '7',
    type: 'analytics',
    priority: 'low',
    title: 'Еженедельный отчет',
    message: 'Готов отчет по продажам за неделю. Рост на 23%',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
    read: true,
    actionUrl: '/admin/analytics/weekly',
    metadata: { growth: 23, revenue: 125000 }
  },
  {
    id: '8',
    type: 'message',
    priority: 'medium',
    title: 'Сообщение в поддержку',
    message: 'Новое обращение в службу поддержки от VIP клиента',
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
    read: false,
    actionUrl: '/admin/support/tickets',
    metadata: { ticketId: 'SUP-001', priority: 'vip' }
  }
];

export default function AdminNotificationCenter() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<AdminNotification[]>(mockNotifications);
  const [filter, setFilter] = useState<'all' | 'unread' | 'high'>('all');

  const unreadCount = notifications.filter(n => !n.read).length;
  const highPriorityCount = notifications.filter(n => n.priority === 'high' || n.priority === 'critical').length;

  const getNotificationIcon = (type: AdminNotification['type']) => {
    switch (type) {
      case 'order': return <Package className="w-4 h-4" />;
      case 'payment': return <CreditCard className="w-4 h-4" />;
      case 'user': return <Users className="w-4 h-4" />;
      case 'review': return <Star className="w-4 h-4" />;
      case 'system': return <Settings className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      case 'analytics': return <TrendingUp className="w-4 h-4" />;
      case 'message': return <MessageSquare className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: AdminNotification['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'low': return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Только что';
    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} ч назад`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} дн назад`;
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const filteredNotifications = notifications.filter(n => {
    switch (filter) {
      case 'unread': return !n.read;
      case 'high': return n.priority === 'high' || n.priority === 'critical';
      default: return true;
    }
  });

  return (
    <div className="relative">
      {/* Bell Icon */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-800/50"
      >
        <Bell className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 w-96 bg-gray-900/95 backdrop-blur-xl border border-gray-800/50 rounded-2xl shadow-2xl z-50">
            {/* Header */}
            <div className="p-4 border-b border-gray-800/50">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white">Уведомления</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              {/* Stats */}
              <div className="flex items-center gap-4 text-sm">
                <span className="text-gray-400">
                  Непрочитанных: <span className="text-red-400 font-semibold">{unreadCount}</span>
                </span>
                <span className="text-gray-400">
                  Важных: <span className="text-orange-400 font-semibold">{highPriorityCount}</span>
                </span>
              </div>
              
              {/* Filters */}
              <div className="flex gap-2 mt-3">
                {[
                  { key: 'all', label: 'Все' },
                  { key: 'unread', label: 'Непрочитанные' },
                  { key: 'high', label: 'Важные' }
                ].map(({ key, label }) => (
                  <button
                    key={key}
                    onClick={() => setFilter(key as any)}
                    className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                      filter === key 
                        ? 'bg-red-500 text-white' 
                        : 'bg-gray-800/50 text-gray-400 hover:text-white'
                    }`}
                  >
                    {label}
                  </button>
                ))}
              </div>
              
              {/* Actions */}
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="mt-3 text-xs text-blue-400 hover:text-blue-300 transition-colors"
                >
                  Отметить все как прочитанные
                </button>
              )}
            </div>

            {/* Notifications List */}
            <div className="max-h-96 overflow-y-auto">
              {filteredNotifications.length === 0 ? (
                <div className="p-6 text-center text-gray-400">
                  <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Нет уведомлений</p>
                </div>
              ) : (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 border-b border-gray-800/30 hover:bg-gray-800/30 transition-colors ${
                      !notification.read ? 'bg-gray-800/20' : ''
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      {/* Icon */}
                      <div className={`p-2 rounded-lg ${getPriorityColor(notification.priority)}`}>
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className={`font-medium ${!notification.read ? 'text-white' : 'text-gray-300'}`}>
                            {notification.title}
                          </h4>
                          <div className="flex items-center gap-1">
                            {!notification.read && (
                              <div className="w-2 h-2 bg-red-500 rounded-full" />
                            )}
                            <button
                              onClick={() => deleteNotification(notification.id)}
                              className="text-gray-500 hover:text-red-400 transition-colors"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-500">
                            {formatTimeAgo(notification.timestamp)}
                          </span>
                          
                          <div className="flex items-center gap-2">
                            {!notification.read && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                              >
                                Прочитано
                              </button>
                            )}
                            
                            {notification.actionUrl && (
                              <button
                                onClick={() => {
                                  markAsRead(notification.id);
                                  // Navigate to action URL
                                  window.location.href = notification.actionUrl!;
                                }}
                                className="text-xs text-green-400 hover:text-green-300 transition-colors"
                              >
                                Открыть
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            <div className="p-3 border-t border-gray-800/50 text-center">
              <button className="text-sm text-gray-400 hover:text-white transition-colors">
                Показать все уведомления
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
