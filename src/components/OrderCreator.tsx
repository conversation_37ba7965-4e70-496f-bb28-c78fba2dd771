'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AppwriteOrderService } from '@/services/appwriteService';
import { notificationService } from '@/services/notificationService';
import { ShoppingCart, CreditCard, CheckCircle } from 'lucide-react';

interface OrderCreatorProps {
  serviceType: 'artist' | 'track';
  packageType: string;
  title: string;
  description: string;
  price: number;
  onOrderCreated?: (orderId: string) => void;
}

const OrderCreator: React.FC<OrderCreatorProps> = ({
  serviceType,
  packageType,
  title,
  description,
  price,
  onOrderCreated
}) => {
  const { user } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [requirements, setRequirements] = useState<Record<string, any>>({});

  const handleCreateOrder = async () => {
    if (!user) {
      alert('Необходимо войти в систему для создания заказа');
      return;
    }

    setIsCreating(true);
    try {
      // Создаем заказ в Appwrite
      const order = await AppwriteOrderService.createOrder({
        userId: user.$id,
        serviceType,
        packageType,
        title,
        description,
        price,
        currency: 'RUB',
        requirements,
        metadata: {
          createdFrom: 'dashboard',
          userAgent: navigator.userAgent,
        }
      });

      // Создаем системное событие для уведомлений
      notificationService.handleSystemEvent({
        type: 'order',
        action: 'created',
        data: {
          orderTitle: title,
          orderId: order.orderId,
          customerName: user.name || user.email,
          serviceType,
          packageType,
          price,
        },
        userId: user.$id
      });

      // Уведомляем родительский компонент
      if (onOrderCreated) {
        onOrderCreated(order.orderId);
      }

      alert('Заказ успешно создан! Вы получите уведомление о дальнейших шагах.');
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Ошибка при создании заказа. Попробуйте еще раз.');
    } finally {
      setIsCreating(false);
    }
  };

  const updateRequirement = (key: string, value: any) => {
    setRequirements(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
      <div className="flex items-center" style={{ marginBottom: '20px', gap: '15px' }}>
        <ShoppingCart className="w-6 h-6 text-red-500" />
        <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
          Создать заказ
        </h3>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {/* Информация о пакете */}
        <div className="bg-gray-700/30 rounded-lg" style={{ padding: '15px' }}>
          <h4 className="text-white font-medium" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
            {title}
          </h4>
          <p className="text-gray-400 text-sm" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
            {description}
          </p>
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Стоимость:</span>
            <span className="text-xl font-bold text-white">{price.toLocaleString()} ₽</span>
          </div>
        </div>

        {/* Требования для артиста */}
        {serviceType === 'artist' && (
          <div>
            <h4 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
              Дополнительная информация
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Имя артиста
                </label>
                <input
                  type="text"
                  placeholder="Введите имя артиста"
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  onChange={(e) => updateRequirement('artistName', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Жанр музыки
                </label>
                <input
                  type="text"
                  placeholder="Например: Hip-Hop, Pop, Rock"
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  onChange={(e) => updateRequirement('genre', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Ссылки на социальные сети
                </label>
                <textarea
                  placeholder="Instagram, YouTube, VK и другие ссылки"
                  rows={3}
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.6' }}
                  onChange={(e) => updateRequirement('socialLinks', e.target.value)}
                />
              </div>
            </div>
          </div>
        )}

        {/* Требования для трека */}
        {serviceType === 'track' && (
          <div>
            <h4 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
              Информация о треке
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Название трека
                </label>
                <input
                  type="text"
                  placeholder="Введите название трека"
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  onChange={(e) => updateRequirement('trackTitle', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Исполнитель
                </label>
                <input
                  type="text"
                  placeholder="Имя исполнителя"
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  onChange={(e) => updateRequirement('artist', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                  Ссылка на трек
                </label>
                <input
                  type="url"
                  placeholder="https://..."
                  className="w-full bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                  style={{ padding: '12px 16px', lineHeight: '1.4' }}
                  onChange={(e) => updateRequirement('trackUrl', e.target.value)}
                />
              </div>
            </div>
          </div>
        )}

        {/* Кнопки действий */}
        <div className="flex justify-between" style={{ gap: '15px', paddingTop: '10px' }}>
          <button
            onClick={handleCreateOrder}
            disabled={isCreating}
            className="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center justify-center"
            style={{ padding: '15px 20px', lineHeight: '1.4', gap: '10px' }}
          >
            {isCreating ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Создание заказа...
              </>
            ) : (
              <>
                <CheckCircle className="w-5 h-5" />
                Создать заказ
              </>
            )}
          </button>
          
          <button
            className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center justify-center"
            style={{ padding: '15px 20px', lineHeight: '1.4', gap: '10px' }}
          >
            <CreditCard className="w-5 h-5" />
            Оплатить сейчас
          </button>
        </div>

        {/* Информация об оплате */}
        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg" style={{ padding: '15px' }}>
          <p className="text-blue-400 text-sm" style={{ lineHeight: '1.6' }}>
            💡 После создания заказа вы получите уведомление с инструкциями по оплате. 
            Работа начнется после подтверждения платежа.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderCreator;
