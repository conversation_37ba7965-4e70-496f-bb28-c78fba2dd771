'use client';

import { useState } from 'react';
import {
  Upload,
  Image,
  Music,
  Globe,
  Instagram,
  Youtube,
  Radio,
  Mail,
  Building,
  Users,
  BarChart3,
  CheckCircle,
  Plus,
  Link,
  Video,
  MessageCircle,
  Headphones,
  Send
} from 'lucide-react';
import { FaVk, FaTiktok, FaSpotify, FaTelegram } from 'react-icons/fa';

const TrackPromotion = () => {
  const [activeStep, setActiveStep] = useState(1);
  const [trackData, setTrackData] = useState({
    title: '',
    artist: '',
    genre: '',
    description: '',
    trackFile: null,
    artwork: null,
    releaseOnSacral: false
  });

  const promotionServices = [
    {
      id: 'social-pages',
      title: 'Создание страниц',
      description: 'Создаются страницы VK/Instagram/YouTube/TikTok',
      icon: <Globe className="w-6 h-6" />,
      platforms: ['VK', 'Instagram', 'YouTube', 'TikTok'],
      status: 'available'
    },
    {
      id: 'sacral-release',
      title: 'Релиз на Sacral Track',
      description: 'Релиз трека на платформе Sacral Track для увеличения роялти',
      icon: <Music className="w-6 h-6" />,
      features: ['Увеличение роялти', 'Дополнительная аудитория'],
      status: 'optional'
    },
    {
      id: 'threads-bot',
      title: 'Threads бот',
      description: 'Бот создает витки с треками ежедневно + статистика прослушиваний и роялти',
      icon: <MessageCircle className="w-6 h-6" />,
      features: ['Ежедневные посты', 'Статистика', 'Роялти трекинг'],
      status: 'premium'
    },
    {
      id: 'youtube-promo',
      title: 'Видео на YouTube',
      description: 'Создание и продвижение видео на YouTube',
      icon: <Youtube className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'instagram-promo',
      title: 'Продвижение в Instagram',
      description: 'Реклама и продвижение в Instagram',
      icon: <Instagram className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'vk-promo',
      title: 'Продвижение в VK',
      description: 'Реклама и продвижение в VKontakte',
      icon: <FaVk className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'email-campaign',
      title: 'Email рассылка',
      description: 'Рассылка по базе артистов и лейблов',
      icon: <Mail className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'club-outreach',
      title: 'Рассылка по клубам',
      description: 'Отправка трека в клубы и на радиостанции',
      icon: <Building className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'artist-network',
      title: 'Рассылка по артистам',
      description: 'Отправка трека другим артистам для коллабораций',
      icon: <Users className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'telegram-group',
      title: 'Группа в Telegram',
      description: 'Создание и продвижение в Telegram группах',
      icon: <FaTelegram className="w-6 h-6" />,
      status: 'available'
    },
    {
      id: 'music-forums',
      title: 'Музыкальные форумы',
      description: 'Продвижение на музыкальных форумах и сообществах',
      icon: <Headphones className="w-6 h-6" />,
      status: 'available'
    }
  ];

  const steps = [
    { id: 1, title: 'Загрузка материалов', description: 'Трек, артворк, описание' },
    { id: 2, title: 'Выбор услуг', description: 'Выберите нужные услуги продвижения' },
    { id: 3, title: 'Настройка кампании', description: 'Настройка параметров продвижения' },
    { id: 4, title: 'Запуск', description: 'Запуск кампании продвижения' }
  ];

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
      {/* Progress Steps */}
      <div className="flex items-center justify-between mb-8">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
              activeStep >= step.id 
                ? 'bg-red-500 border-red-500 text-white' 
                : 'border-gray-600 text-gray-400'
            }`}>
              {activeStep > step.id ? <CheckCircle className="w-5 h-5" /> : step.id}
            </div>
            <div className="ml-3">
              <div className="text-sm font-semibold text-white">{step.title}</div>
              <div className="text-xs text-gray-400">{step.description}</div>
            </div>
            {index < steps.length - 1 && (
              <div className={`w-16 h-0.5 mx-4 ${
                activeStep > step.id ? 'bg-red-500' : 'bg-gray-600'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      {activeStep === 1 && (
        <div className="grid grid-cols-1 md:grid-cols-3" style={{ gap: '30px' }}>
          {/* Upload Track */}
          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <Upload className="w-8 h-8 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px' }}>Добавить трек</h3>
            <p className="text-gray-400 text-sm" style={{ marginBottom: '20px' }}>Загрузите ваш трек в высоком качестве (WAV, FLAC, MP3 320kbps)</p>
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center">
              <input type="file" accept="audio/*" className="hidden" id="track-upload" />
              <label htmlFor="track-upload" className="cursor-pointer">
                <div className="text-gray-400 mb-2">Перетащите файл сюда или</div>
                <div className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-colors inline-block">
                  Выбрать файл
                </div>
              </label>
            </div>
          </div>

          {/* Upload Artwork */}
          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <Image className="w-8 h-8 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px' }}>Добавить артворк</h3>
            <p className="text-gray-400 text-sm" style={{ marginBottom: '20px' }}>Обложка трека 3000x3000px в формате JPG или PNG</p>
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center">
              <input type="file" accept="image/*" className="hidden" id="artwork-upload" />
              <label htmlFor="artwork-upload" className="cursor-pointer">
                <div className="text-gray-400 mb-2">Перетащите изображение сюда или</div>
                <div className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-colors inline-block">
                  Выбрать файл
                </div>
              </label>
            </div>
          </div>

          {/* Track Info */}
          <div className="bg-gray-800/30 rounded-xl border border-gray-700/30" style={{ padding: '20px' }}>
            <Music className="w-8 h-8 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px' }}>Информация о треке</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <input 
                type="text" 
                placeholder="Название трека" 
                className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              />
              <input 
                type="text" 
                placeholder="Исполнитель" 
                className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              />
              <select className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white">
                <option>Выберите жанр</option>
                <option>Hip-Hop</option>
                <option>Pop</option>
                <option>Rock</option>
                <option>Electronic</option>
                <option>R&B</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {activeStep === 2 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3" style={{ gap: '30px' }}>
          {promotionServices.map((service) => (
            <div key={service.id} className="bg-gray-800/30 rounded-xl border border-gray-700/30 hover:border-red-500/30 transition-colors" style={{ padding: '20px' }}>
              <div className="flex items-center justify-between mb-4">
                <div className="text-red-500">{service.icon}</div>
                <div className={`px-2 py-1 rounded-full text-xs ${
                  service.status === 'available' ? 'bg-green-500/20 text-green-400' :
                  service.status === 'optional' ? 'bg-blue-500/20 text-blue-400' :
                  'bg-purple-500/20 text-purple-400'
                }`}>
                  {service.status === 'available' ? 'Доступно' :
                   service.status === 'optional' ? 'Опционально' : 'Премиум'}
                </div>
              </div>
              <h3 className="text-lg font-semibold text-white" style={{ marginBottom: '20px' }}>{service.title}</h3>
              <p className="text-gray-400 text-sm" style={{ marginBottom: '20px' }}>{service.description}</p>
              
              {service.platforms && (
                <div className="flex flex-wrap" style={{ gap: '10px', marginBottom: '20px' }}>
                  {service.platforms.map((platform) => (
                    <span key={platform} className="bg-gray-700/50 text-gray-300 px-2 py-1 rounded text-xs">
                      {platform}
                    </span>
                  ))}
                </div>
              )}
              
              {service.features && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '5px', marginBottom: '20px' }}>
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-300" style={{ gap: '10px' }}>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span style={{ lineHeight: '1.4' }}>{feature}</span>
                    </div>
                  ))}
                </div>
              )}
              
              <button className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-colors">
                Добавить в пакет
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <button 
          onClick={() => setActiveStep(Math.max(1, activeStep - 1))}
          disabled={activeStep === 1}
          className="px-6 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Назад
        </button>
        <button 
          onClick={() => setActiveStep(Math.min(4, activeStep + 1))}
          disabled={activeStep === 4}
          className="px-6 py-2 bg-red-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Далее
        </button>
      </div>
    </div>
  );
};

export default TrackPromotion;
