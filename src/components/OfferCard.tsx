'use client';

import { useState } from 'react';
import { 
  Music, 
  TrendingUp, 
  Target, 
  Radio, 
  Youtube, 
  Instagram,
  Check,
  ArrowRight,
  Star,
  Zap,
  Crown,
  Sparkles,
  ExternalLink,
  CreditCard
} from 'lucide-react';
import { FaSpotify, FaTiktok } from 'react-icons/fa';

interface OfferCardProps {
  type: 'starter' | 'pro' | 'premium' | 'custom';
  title: string;
  price: string;
  originalPrice?: string;
  description: string;
  features: string[];
  platforms: string[];
  results: string;
  popular?: boolean;
  onSelect: () => void;
  language?: 'ru' | 'en';
}

const OfferCard: React.FC<OfferCardProps> = ({
  type,
  title,
  price,
  originalPrice,
  description,
  features,
  platforms,
  results,
  popular = false,
  onSelect,
  language = 'ru'
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getIcon = () => {
    switch (type) {
      case 'starter':
        return <Zap className="w-6 h-6 text-green-400" />;
      case 'pro':
        return <Star className="w-6 h-6 text-blue-400" />;
      case 'premium':
        return <Crown className="w-6 h-6 text-purple-400" />;
      default:
        return <Sparkles className="w-6 h-6 text-red-400" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'spotify':
        return <FaSpotify className="w-4 h-4 text-green-400" />;
      case 'tiktok':
        return <FaTiktok className="w-4 h-4 text-pink-400" />;
      case 'youtube':
        return <Youtube className="w-4 h-4 text-red-400" />;
      case 'instagram':
        return <Instagram className="w-4 h-4 text-purple-400" />;
      case 'radio':
        return <Radio className="w-4 h-4 text-blue-400" />;
      default:
        return <Music className="w-4 h-4 text-gray-400" />;
    }
  };

  const getGradient = () => {
    switch (type) {
      case 'starter':
        return 'from-green-500/20 to-emerald-500/20 border-green-500/30';
      case 'pro':
        return 'from-blue-500/20 to-cyan-500/20 border-blue-500/30';
      case 'premium':
        return 'from-purple-500/20 to-pink-500/20 border-purple-500/30';
      default:
        return 'from-red-500/20 to-orange-500/20 border-red-500/30';
    }
  };

  return (
    <div 
      className={`relative group transition-all duration-300 ${isHovered ? 'scale-105' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Glow effect */}
      <div className={`absolute inset-0 bg-gradient-to-r ${getGradient()} rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-all duration-500`}></div>
      
      {/* Popular badge */}
      {popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
            <Star className="w-3 h-3" />
            <span>{language === 'ru' ? 'ПОПУЛЯРНЫЙ' : 'POPULAR'}</span>
          </div>
        </div>
      )}

      <div className={`relative bg-gray-900/80 backdrop-blur-xl border ${getGradient()} rounded-2xl p-6 h-full transition-all duration-300`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-xl bg-gradient-to-br ${getGradient()}`}>
              {getIcon()}
            </div>
            <div>
              <h3 className="text-white font-bold text-lg mb-2">{title}</h3>
              <p className="text-gray-400 text-sm">{description}</p>
            </div>
          </div>
        </div>

        {/* Price */}
        <div className="mb-8">
          <div className="flex items-baseline space-x-2">
            <span className="text-3xl font-bold text-white">{price}</span>
            {originalPrice && (
              <span className="text-lg text-gray-500 line-through">{originalPrice}</span>
            )}
          </div>
          <p className="text-gray-400 text-sm mt-2">{language === 'ru' ? 'за кампанию' : 'per campaign'}</p>
        </div>

        {/* Results */}
        <div className="mb-8 p-4 rounded-xl bg-gray-800/50 border border-gray-700/50">
          <div className="flex items-center space-x-2 mb-3">
            <TrendingUp className="w-4 h-4 text-green-400" />
            <span className="text-green-400 font-semibold text-sm">
              {language === 'ru' ? 'Ожидаемый результат' : 'Expected Result'}
            </span>
          </div>
          <p className="text-white font-bold text-lg">{results}</p>
        </div>

        {/* Platforms */}
        <div className="mb-8">
          <h4 className="text-gray-300 font-semibold text-sm mb-5">
            {language === 'ru' ? 'Платформы:' : 'Platforms:'}
          </h4>
          <div className="flex flex-wrap gap-2">
            {platforms.map((platform, index) => (
              <div key={index} className="flex items-center space-x-2 bg-gray-800/50 px-3 py-2 rounded-lg">
                {getPlatformIcon(platform)}
                <span className="text-gray-300 text-sm">{platform}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="mb-8">
          <h4 className="text-gray-300 font-semibold text-sm mb-5">
            {language === 'ru' ? 'Включено:' : 'Included:'}
          </h4>
          <div className="space-y-3">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={onSelect}
          className={`w-full bg-gradient-to-r ${
            type === 'starter' ? 'from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700' :
            type === 'pro' ? 'from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700' :
            type === 'premium' ? 'from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700' :
            'from-red-500 to-orange-600 hover:from-red-600 hover:to-orange-700'
          } text-white py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center space-x-2 group/btn`}
        >
          <CreditCard className="w-5 h-5 group-hover/btn:scale-110 transition-transform" />
          <span>{language === 'ru' ? 'Выбрать пакет' : 'Choose Package'}</span>
          <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform" />
        </button>

        {/* Additional info */}
        <div className="mt-4 text-center">
          <p className="text-gray-500 text-xs">
            {language === 'ru' ? 'Гарантия результата или возврат денег' : 'Results guaranteed or money back'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default OfferCard;
