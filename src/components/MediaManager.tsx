'use client';

import { useState, useRef } from 'react';
import { 
  Upload, 
  Image, 
  Music, 
  Video, 
  File,
  Trash2, 
  Download, 
  Eye, 
  Edit,
  Share2,
  Copy,
  Folder,
  Grid,
  List,
  Search,
  Filter,
  Plus,
  X
} from 'lucide-react';
import FileUpload from '@/components/FileUpload';

interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'audio' | 'video' | 'document';
  size: number;
  url: string;
  thumbnail?: string;
  uploadedAt: string;
  tags: string[];
  folder?: string;
}

interface MediaManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectFile?: (file: MediaFile) => void;
  allowMultiple?: boolean;
  fileTypes?: string[];
}

const MediaManager: React.FC<MediaManagerProps> = ({
  isOpen,
  onClose,
  onSelectFile,
  allowMultiple = false,
  fileTypes = ['image', 'audio', 'video', 'document']
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [showUpload, setShowUpload] = useState(false);

  // Mock данные файлов
  const mockFiles: MediaFile[] = [
    {
      id: '1',
      name: 'track-cover.jpg',
      type: 'image',
      size: 2048000,
      url: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop',
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=150&h=150&fit=crop',
      uploadedAt: '2024-01-15T10:30:00Z',
      tags: ['cover', 'artwork', 'music'],
      folder: 'covers'
    },
    {
      id: '2',
      name: 'new-track.mp3',
      type: 'audio',
      size: 8192000,
      url: '/audio/new-track.mp3',
      uploadedAt: '2024-01-14T15:20:00Z',
      tags: ['track', 'demo', 'hip-hop'],
      folder: 'tracks'
    },
    {
      id: '3',
      name: 'promo-video.mp4',
      type: 'video',
      size: 52428800,
      url: '/video/promo-video.mp4',
      thumbnail: 'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?w=150&h=150&fit=crop',
      uploadedAt: '2024-01-13T09:45:00Z',
      tags: ['promo', 'video', 'marketing'],
      folder: 'videos'
    },
    {
      id: '4',
      name: 'contract.pdf',
      type: 'document',
      size: 1024000,
      url: '/documents/contract.pdf',
      uploadedAt: '2024-01-12T14:10:00Z',
      tags: ['contract', 'legal', 'document'],
      folder: 'documents'
    }
  ];

  const folders = [
    { id: 'all', name: 'Все файлы', count: mockFiles.length },
    { id: 'covers', name: 'Обложки', count: mockFiles.filter(f => f.folder === 'covers').length },
    { id: 'tracks', name: 'Треки', count: mockFiles.filter(f => f.folder === 'tracks').length },
    { id: 'videos', name: 'Видео', count: mockFiles.filter(f => f.folder === 'videos').length },
    { id: 'documents', name: 'Документы', count: mockFiles.filter(f => f.folder === 'documents').length }
  ];

  const filteredFiles = mockFiles.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         file.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesFolder = selectedFolder === 'all' || file.folder === selectedFolder;
    const matchesType = fileTypes.includes(file.type);
    
    return matchesSearch && matchesFolder && matchesType;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="w-5 h-5" />;
      case 'audio': return <Music className="w-5 h-5" />;
      case 'video': return <Video className="w-5 h-5" />;
      default: return <File className="w-5 h-5" />;
    }
  };

  const handleFileSelect = (file: MediaFile) => {
    if (allowMultiple) {
      setSelectedFiles(prev => 
        prev.includes(file.id) 
          ? prev.filter(id => id !== file.id)
          : [...prev, file.id]
      );
    } else {
      if (onSelectFile) {
        onSelectFile(file);
        onClose();
      }
    }
  };

  const handleSelectMultiple = () => {
    if (onSelectFile && selectedFiles.length > 0) {
      const files = mockFiles.filter(f => selectedFiles.includes(f.id));
      files.forEach(file => onSelectFile(file));
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
      <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-800" style={{ padding: '20px 30px' }}>
          <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
            Медиа менеджер
          </h2>
          <div className="flex items-center" style={{ gap: '15px' }}>
            <button
              onClick={() => setShowUpload(true)}
              className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
              style={{ padding: '10px 15px', gap: '8px', lineHeight: '1.4' }}
            >
              <Upload className="w-4 h-4" />
              Загрузить
            </button>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-800" style={{ padding: '20px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              {/* Folders */}
              <div>
                <h3 className="text-white font-medium" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
                  Папки
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                  {folders.map((folder) => (
                    <button
                      key={folder.id}
                      onClick={() => setSelectedFolder(folder.id)}
                      className={`flex items-center justify-between rounded-lg transition-colors text-left ${
                        selectedFolder === folder.id
                          ? 'bg-red-500/20 text-red-400'
                          : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                      }`}
                      style={{ padding: '8px 12px', lineHeight: '1.4' }}
                    >
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <Folder className="w-4 h-4" />
                        <span>{folder.name}</span>
                      </div>
                      <span className="text-xs">{folder.count}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="text-white font-medium" style={{ marginBottom: '10px', lineHeight: '1.4' }}>
                  Действия
                </h3>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                  <button className="flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors" style={{ padding: '8px 12px', gap: '8px', lineHeight: '1.4' }}>
                    <Plus className="w-4 h-4" />
                    Новая папка
                  </button>
                  <button className="flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors" style={{ padding: '8px 12px', gap: '8px', lineHeight: '1.4' }}>
                    <Download className="w-4 h-4" />
                    Скачать все
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Toolbar */}
            <div className="border-b border-gray-800" style={{ padding: '15px 20px' }}>
              <div className="flex items-center justify-between">
                <div className="flex items-center" style={{ gap: '15px' }}>
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Поиск файлов..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ paddingLeft: '35px', paddingRight: '12px', paddingTop: '8px', paddingBottom: '8px', lineHeight: '1.4', width: '250px' }}
                    />
                  </div>

                  <button className="bg-gray-800/50 hover:bg-gray-800 border border-gray-700 rounded-lg text-gray-400 hover:text-white transition-colors flex items-center" style={{ padding: '8px 12px', gap: '6px', lineHeight: '1.4' }}>
                    <Filter className="w-4 h-4" />
                    Фильтры
                  </button>
                </div>

                <div className="flex items-center" style={{ gap: '10px' }}>
                  <div className="flex bg-gray-800/50 rounded-lg" style={{ padding: '2px' }}>
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 rounded transition-colors ${
                        viewMode === 'grid' ? 'bg-red-500 text-white' : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      <Grid className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 rounded transition-colors ${
                        viewMode === 'list' ? 'bg-red-500 text-white' : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Files Grid/List */}
            <div className="flex-1 overflow-y-auto" style={{ padding: '20px' }}>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6" style={{ gap: '15px' }}>
                  {filteredFiles.map((file) => (
                    <div
                      key={file.id}
                      onClick={() => handleFileSelect(file)}
                      className={`bg-gray-800/30 border rounded-lg cursor-pointer transition-all hover:bg-gray-800/50 ${
                        selectedFiles.includes(file.id) ? 'border-red-500 bg-red-500/10' : 'border-gray-700/50'
                      }`}
                      style={{ padding: '12px' }}
                    >
                      <div className="aspect-square bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden" style={{ marginBottom: '10px' }}>
                        {file.thumbnail ? (
                          <img src={file.thumbnail} alt={file.name} className="w-full h-full object-cover" />
                        ) : (
                          <div className="text-gray-400">
                            {getFileIcon(file.type)}
                          </div>
                        )}
                      </div>
                      <div className="text-white text-sm font-medium truncate" style={{ marginBottom: '4px', lineHeight: '1.4' }}>
                        {file.name}
                      </div>
                      <div className="text-gray-400 text-xs" style={{ lineHeight: '1.6' }}>
                        {formatFileSize(file.size)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {filteredFiles.map((file) => (
                    <div
                      key={file.id}
                      onClick={() => handleFileSelect(file)}
                      className={`flex items-center bg-gray-800/30 border rounded-lg cursor-pointer transition-all hover:bg-gray-800/50 ${
                        selectedFiles.includes(file.id) ? 'border-red-500 bg-red-500/10' : 'border-gray-700/50'
                      }`}
                      style={{ padding: '12px', gap: '12px' }}
                    >
                      <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden">
                        {file.thumbnail ? (
                          <img src={file.thumbnail} alt={file.name} className="w-full h-full object-cover" />
                        ) : (
                          <div className="text-gray-400">
                            {getFileIcon(file.type)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="text-white font-medium" style={{ lineHeight: '1.4' }}>{file.name}</div>
                        <div className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                          {formatFileSize(file.size)} • {new Date(file.uploadedAt).toLocaleDateString('ru-RU')}
                        </div>
                      </div>
                      <div className="flex items-center" style={{ gap: '8px' }}>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-gray-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Download className="w-4 h-4" />
                        </button>
                        <button className="w-8 h-8 rounded-lg bg-gray-700 hover:bg-red-600 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {filteredFiles.length === 0 && (
                <div className="text-center" style={{ padding: '60px 20px' }}>
                  <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto" style={{ marginBottom: '20px' }}>
                    <File className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-white font-medium" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                    Файлы не найдены
                  </h3>
                  <p className="text-gray-400" style={{ lineHeight: '1.6' }}>
                    Попробуйте изменить поисковый запрос или загрузить новые файлы
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        {allowMultiple && selectedFiles.length > 0 && (
          <div className="border-t border-gray-800 flex items-center justify-between" style={{ padding: '15px 30px' }}>
            <span className="text-gray-400" style={{ lineHeight: '1.4' }}>
              Выбрано файлов: {selectedFiles.length}
            </span>
            <div className="flex items-center" style={{ gap: '15px' }}>
              <button
                onClick={() => setSelectedFiles([])}
                className="text-gray-400 hover:text-white transition-colors"
                style={{ lineHeight: '1.4' }}
              >
                Отменить
              </button>
              <button
                onClick={handleSelectMultiple}
                className="bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                style={{ padding: '10px 20px', lineHeight: '1.4' }}
              >
                Выбрать ({selectedFiles.length})
              </button>
            </div>
          </div>
        )}

        {/* Upload Modal */}
        {showUpload && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center" style={{ padding: '20px' }}>
            <div className="bg-gray-800 rounded-xl border border-gray-700 max-w-2xl w-full">
              <div className="flex items-center justify-between border-b border-gray-700" style={{ padding: '20px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Загрузка файлов
                </h3>
                <button
                  onClick={() => setShowUpload(false)}
                  className="w-8 h-8 rounded-lg bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div style={{ padding: '20px' }}>
                <FileUpload
                  accept="image/*,audio/*,video/*,.pdf,.doc,.docx"
                  multiple={true}
                  maxSize={50}
                  maxFiles={10}
                  onFilesChange={(files) => {
                    console.log('Files uploaded:', files);
                    // Здесь будет логика загрузки файлов
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MediaManager;
