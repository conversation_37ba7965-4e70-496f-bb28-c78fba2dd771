'use client';

import { motion } from 'framer-motion';
import { 
  Music, 
  BarChart3, 
  Target, 
  TrendingUp, 
  Users, 
  Radio,
  Heart,
  Zap,
  Star,
  Play,
  Pause,
  Volume2
} from 'lucide-react';

interface TrackAnalysis {
  genre: string;
  subgenre: string;
  bpm: number;
  key: string;
  energy: number;
  danceability: number;
  valence: number;
  potential: {
    commercial: number;
    viral: number;
    radio: number;
    club: number;
  };
  recommendations: string[];
  targetAudience: string[];
  platforms: {
    name: string;
    score: number;
    reason: string;
  }[];
}

interface TrackAnalysisDisplayProps {
  analysis: TrackAnalysis;
  trackName: string;
  isPlaying?: boolean;
  onPlayToggle?: () => void;
}

const TrackAnalysisDisplay: React.FC<TrackAnalysisDisplayProps> = ({
  analysis,
  trackName,
  isPlaying = false,
  onPlayToggle
}) => {
  const getPotentialColor = (score: number) => {
    if (score >= 80) return 'from-green-500 to-emerald-500';
    if (score >= 60) return 'from-yellow-500 to-orange-500';
    return 'from-pink-500 to-rose-500';
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'spotify': return '🎵';
      case 'tiktok': return '🎬';
      case 'youtube': return '📺';
      case 'soundcloud': return '☁️';
      default: return '🎶';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      {/* Track Info Header */}
      <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-6 border border-purple-500/20">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
              <Music className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-white">{trackName}</h3>
              <p className="text-gray-400">{analysis.genre} • {analysis.subgenre}</p>
            </div>
          </div>
          
          {onPlayToggle && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onPlayToggle}
              className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white"
            >
              {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6 ml-1" />}
            </motion.button>
          )}
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{analysis.bpm}</div>
            <div className="text-sm text-gray-400">BPM</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-pink-400">{analysis.key}</div>
            <div className="text-sm text-gray-400">Тональность</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{Math.round(analysis.energy * 100)}%</div>
            <div className="text-sm text-gray-400">Энергия</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{Math.round(analysis.danceability * 100)}%</div>
            <div className="text-sm text-gray-400">Танцевальность</div>
          </div>
        </div>
      </div>

      {/* Potential Scores */}
      <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-6 border border-purple-500/20">
        <div className="flex items-center gap-3 mb-6">
          <Target className="w-6 h-6 text-purple-400" />
          <h4 className="text-xl font-bold text-white">Потенциал трека</h4>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(analysis.potential).map(([key, score]) => (
            <motion.div
              key={key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="space-y-2"
            >
              <div className="flex justify-between items-center">
                <span className="text-gray-300 capitalize">
                  {key === 'commercial' ? 'Коммерческий' : 
                   key === 'viral' ? 'Вирусный' :
                   key === 'radio' ? 'Радио' : 'Клубный'}
                </span>
                <span className="text-white font-bold">{score}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${score}%` }}
                  transition={{ duration: 1, delay: 0.2 }}
                  className={`h-3 rounded-full bg-gradient-to-r ${getPotentialColor(score)}`}
                />
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Platform Recommendations */}
      <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-6 border border-purple-500/20">
        <div className="flex items-center gap-3 mb-6">
          <TrendingUp className="w-6 h-6 text-purple-400" />
          <h4 className="text-xl font-bold text-white">Рекомендуемые платформы</h4>
        </div>
        
        <div className="space-y-4">
          {analysis.platforms.map((platform, index) => (
            <motion.div
              key={platform.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-4 bg-black/20 rounded-2xl border border-purple-500/10"
            >
              <div className="flex items-center gap-4">
                <div className="text-2xl">{getPlatformIcon(platform.name)}</div>
                <div>
                  <div className="text-white font-semibold">{platform.name}</div>
                  <div className="text-sm text-gray-400">{platform.reason}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-purple-400">{platform.score}%</div>
                <div className="text-sm text-gray-400">Совместимость</div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-6 border border-purple-500/20">
        <div className="flex items-center gap-3 mb-6">
          <Zap className="w-6 h-6 text-purple-400" />
          <h4 className="text-xl font-bold text-white">AI Рекомендации</h4>
        </div>
        
        <div className="space-y-3">
          {analysis.recommendations.map((rec, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start gap-3 p-4 bg-black/20 rounded-2xl border border-purple-500/10"
            >
              <Star className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <p className="text-gray-300">{rec}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Target Audience */}
      <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-6 border border-purple-500/20">
        <div className="flex items-center gap-3 mb-6">
          <Users className="w-6 h-6 text-purple-400" />
          <h4 className="text-xl font-bold text-white">Целевая аудитория</h4>
        </div>
        
        <div className="flex flex-wrap gap-3">
          {analysis.targetAudience.map((audience, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="px-4 py-2 bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-full text-gray-300"
            >
              {audience}
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default TrackAnalysisDisplay;
