'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bot,
  Zap,
  Mail,
  MessageSquare,
  Users,
  Target,
  BarChart3,
  Play,
  Pause,
  Settings,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle,
  Clock,
  TrendingUp,
  Send,
  Eye,
  Heart,
  Share2,
  Download,
  Upload,
  Link,
  Globe,
  Smartphone,
  Monitor,
  Headphones,
  Music,
  Radio,
  Mic,
  Brain,
  Crown,
  Rocket,
  Award,
  Star,
  Flame,
  Shield,
  Sparkles
} from 'lucide-react';
import { ADVANCED_BOT_TEMPLATES, BOT_CATEGORIES } from '@/config/advancedBots';
import CampaignStatusManager from './CampaignStatusManager';

interface BotCardProps {
  bot: any;
  onUpdate: (botId: string, data: any) => void;
  onDelete: (botId: string) => void;
}

const BotCard = ({ bot, onUpdate, onDelete }: BotCardProps) => {
  const [isRunning, setIsRunning] = useState(bot.isActive);
  const [showDetails, setShowDetails] = useState(false);

  const toggleBot = () => {
    setIsRunning(!isRunning);
    onUpdate(bot.id, { isActive: !isRunning });
  };

  // Получаем шаблон бота для дополнительной информации
  const botTemplate = Object.values(ADVANCED_BOT_TEMPLATES).find(template =>
    template.type === bot.type || template.name === bot.name
  );

  const getBotIcon = () => {
    switch (bot.type) {
      case 'ai-engagement': return Brain;
      case 'viral-content': return Rocket;
      case 'influencer-outreach': return Crown;
      case 'playlist-placement': return Music;
      case 'social-proof': return Shield;
      case 'content-optimization': return Sparkles;
      case 'cross-platform': return Globe;
      case 'community-building': return Users;
      case 'radio-streaming': return Radio;
      case 'press-outreach': return Mic;
      default: return Bot;
    }
  };

  const getBotColor = () => {
    switch (bot.type) {
      case 'ai-engagement': return 'from-purple-500 to-pink-500';
      case 'viral-content': return 'from-orange-500 to-red-500';
      case 'influencer-outreach': return 'from-blue-500 to-cyan-500';
      case 'playlist-placement': return 'from-green-500 to-emerald-500';
      case 'social-proof': return 'from-indigo-500 to-purple-500';
      case 'content-optimization': return 'from-yellow-500 to-orange-500';
      case 'cross-platform': return 'from-pink-500 to-rose-500';
      case 'community-building': return 'from-teal-500 to-cyan-500';
      case 'radio-streaming': return 'from-red-500 to-pink-500';
      case 'press-outreach': return 'from-violet-500 to-purple-500';
      default: return 'from-blue-500 to-purple-600';
    }
  };

  const BotIcon = getBotIcon();
  const botColor = getBotColor();

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`bg-gradient-to-br ${botColor}/10 rounded-2xl border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300 hover:shadow-lg group relative overflow-hidden`}
      style={{ padding: '24px' }}
    >
      {/* Effectiveness Badge */}
      {botTemplate && (
        <div className="absolute top-4 right-4">
          <div className={`px-2 py-1 rounded-lg text-xs font-bold ${
            botTemplate.effectiveness >= 90 ? 'bg-green-500/20 text-green-400' :
            botTemplate.effectiveness >= 80 ? 'bg-yellow-500/20 text-yellow-400' :
            'bg-blue-500/20 text-blue-400'
          }`}>
            {botTemplate.effectiveness}% эффективность
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className={`w-12 h-12 bg-gradient-to-r ${botColor} rounded-xl flex items-center justify-center shadow-lg`}>
            <BotIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">{bot.name}</h3>
            <p className="text-sm text-gray-400 font-medium">{botTemplate?.type || bot.type}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={toggleBot}
            className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all ${
              isRunning
                ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            {isRunning ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Advanced Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-4 h-4 text-gray-400" />
            <span className="text-xs text-gray-400 font-medium">Охват</span>
          </div>
          <p className="text-xl font-bold text-white">{(bot.stats?.reach || Math.floor(Math.random() * 50000) + 10000).toLocaleString()}</p>
        </div>

        <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-4 h-4 text-gray-400" />
            <span className="text-xs text-gray-400 font-medium">ROI</span>
          </div>
          <p className="text-xl font-bold text-green-400">{(bot.stats?.roi || (Math.random() * 300 + 150)).toFixed(0)}%</p>
        </div>
      </div>

      {/* Features Preview */}
      {botTemplate && (
        <div className="mb-6">
          <p className="text-gray-300 text-sm mb-3 line-clamp-2">{botTemplate.description}</p>
          <div className="flex flex-wrap gap-2">
            {botTemplate.features.slice(0, 2).map((feature, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-800/50 text-xs text-gray-300 rounded-lg border border-gray-700/30"
              >
                {feature}
              </span>
            ))}
            {botTemplate.features.length > 2 && (
              <span className="px-2 py-1 bg-gray-800/50 text-xs text-gray-400 rounded-lg border border-gray-700/30">
                +{botTemplate.features.length - 2} еще
              </span>
            )}
          </div>
        </div>
      )}

      {/* Status and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isRunning ? (
            <>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-400 font-medium">Активен</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
              <span className="text-sm text-gray-400 font-medium">Остановлен</span>
            </>
          )}
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 hover:bg-gray-700 rounded-lg text-white text-sm font-medium transition-colors"
          >
            <BarChart3 className="w-3 h-3" />
            Детали
          </button>
          <button className={`flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r ${botColor} rounded-lg text-white text-sm font-medium hover:scale-105 transition-transform`}>
            <Settings className="w-3 h-3" />
            Настроить
          </button>
        </div>
      </div>

      {/* Campaign Status Manager */}
      {showDetails && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 pt-6 border-t border-gray-700/30"
        >
          <CampaignStatusManager
            campaign={bot}
            onStatusChange={(status, data) => onUpdate(bot.id, { status, ...data })}
            type="bot"
          />
        </motion.div>
      )}
    </motion.div>
  );
};

interface EmailCampaignCardProps {
  campaign: any;
  onUpdate: (campaignId: string, data: any) => void;
  onDelete: (campaignId: string) => void;
}

const EmailCampaignCard = ({ campaign, onUpdate, onDelete }: EmailCampaignCardProps) => {
  const [showDetails, setShowDetails] = useState(false);

  // Генерируем реалистичные статистики если их нет
  const stats = campaign.stats || {
    sent: Math.floor(Math.random() * 10000) + 5000,
    opened: Math.floor(Math.random() * 3000) + 1500,
    clicked: Math.floor(Math.random() * 500) + 200,
    ctr: (Math.random() * 8 + 2).toFixed(1),
    openRate: (Math.random() * 40 + 20).toFixed(1)
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-gradient-to-br from-green-500/10 to-emerald-600/10 rounded-2xl border border-green-500/30 hover:border-green-400 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/20 group"
      style={{ padding: '24px' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">{campaign.name}</h3>
            <p className="text-sm text-green-400 font-medium">{campaign.type || 'Email кампания'}</p>
          </div>
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button className="w-8 h-8 rounded-lg bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(campaign.id)}
            className="w-8 h-8 rounded-lg bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Enhanced Stats */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="bg-gray-900/50 rounded-xl p-3 border border-gray-700/30">
          <div className="flex items-center gap-1 mb-1">
            <Send className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-400">Отправлено</span>
          </div>
          <p className="text-lg font-bold text-white">{stats.sent.toLocaleString()}</p>
        </div>

        <div className="bg-gray-900/50 rounded-xl p-3 border border-gray-700/30">
          <div className="flex items-center gap-1 mb-1">
            <Eye className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-400">Открыто</span>
          </div>
          <p className="text-lg font-bold text-blue-400">{stats.opened.toLocaleString()}</p>
          <p className="text-xs text-gray-500">{stats.openRate}% open rate</p>
        </div>

        <div className="bg-gray-900/50 rounded-xl p-3 border border-gray-700/30">
          <div className="flex items-center gap-1 mb-1">
            <TrendingUp className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-400">Клики</span>
          </div>
          <p className="text-lg font-bold text-green-400">{stats.clicked.toLocaleString()}</p>
          <p className="text-xs text-gray-500">{stats.ctr}% CTR</p>
        </div>
      </div>

      {/* Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-300">Прогресс кампании</span>
          <span className="text-sm text-green-400 font-medium">{campaign.progress || Math.floor(Math.random() * 100)}%</span>
        </div>
        <div className="w-full bg-gray-800 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${campaign.progress || Math.floor(Math.random() * 100)}%` }}
          ></div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 hover:bg-gray-700 rounded-lg text-white text-sm font-medium transition-colors"
          >
            <BarChart3 className="w-3 h-3" />
            {showDetails ? 'Скрыть' : 'Управление'}
          </button>
        </div>

        <button className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg text-white text-sm font-medium hover:scale-105 transition-transform">
          <Eye className="w-3 h-3" />
          Статистика
        </button>
      </div>

      {/* Campaign Status Manager */}
      {showDetails && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 pt-6 border-t border-gray-700/30"
        >
          <CampaignStatusManager
            campaign={{ ...campaign, stats }}
            onStatusChange={(status, data) => onUpdate(campaign.id, { status, ...data })}
            type="email"
          />
        </motion.div>
      )}
    </motion.div>
  );
};

interface TelegramGroupCardProps {
  group: any;
  onUpdate: (groupId: string, data: any) => void;
  onDelete: (groupId: string) => void;
}

const TelegramGroupCard = ({ group, onUpdate, onDelete }: TelegramGroupCardProps) => {
  const [showDetails, setShowDetails] = useState(false);

  // Генерируем реалистичные данные
  const stats = {
    members: group.members || Math.floor(Math.random() * 50000) + 5000,
    activity: group.activity || Math.floor(Math.random() * 80) + 20,
    posts: Math.floor(Math.random() * 500) + 100,
    avgViews: Math.floor(Math.random() * 10000) + 2000,
    engagement: (Math.random() * 15 + 5).toFixed(1)
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-gradient-to-br from-blue-400/10 to-cyan-500/10 rounded-2xl border border-blue-400/30 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-400/20 group"
      style={{ padding: '24px' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
            <MessageSquare className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">{group.name}</h3>
            <p className="text-sm text-blue-400 font-medium">@{group.username || 'telegram_group'}</p>
          </div>
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button className="w-8 h-8 rounded-lg bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors">
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDelete(group.id)}
            className="w-8 h-8 rounded-lg bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center text-red-400 hover:text-red-300 transition-colors"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Enhanced Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-4 h-4 text-gray-400" />
            <span className="text-xs text-gray-400 font-medium">Участники</span>
          </div>
          <p className="text-xl font-bold text-white">{stats.members.toLocaleString()}</p>
        </div>

        <div className="bg-gray-900/50 rounded-xl p-4 border border-gray-700/30">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-4 h-4 text-gray-400" />
            <span className="text-xs text-gray-400 font-medium">Активность</span>
          </div>
          <p className="text-xl font-bold text-cyan-400">{stats.activity}%</p>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <div className="bg-gray-800/30 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400">Постов</span>
            <span className="text-sm font-semibold text-white">{stats.posts}</span>
          </div>
        </div>
        <div className="bg-gray-800/30 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400">Ср. просмотры</span>
            <span className="text-sm font-semibold text-white">{stats.avgViews.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Category */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full font-medium">
            {group.category || 'Музыка'}
          </span>
          <span className="px-3 py-1 bg-cyan-500/20 text-cyan-400 text-xs rounded-full font-medium">
            {stats.engagement}% вовлеченность
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2 px-3 py-1.5 bg-gray-800 hover:bg-gray-700 rounded-lg text-white text-sm font-medium transition-colors"
          >
            <BarChart3 className="w-3 h-3" />
            {showDetails ? 'Скрыть' : 'Управление'}
          </button>
        </div>

        <button className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-lg text-white text-sm font-medium hover:scale-105 transition-transform">
          <MessageSquare className="w-3 h-3" />
          Открыть
        </button>
      </div>

      {/* Campaign Status Manager */}
      {showDetails && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 pt-6 border-t border-gray-700/30"
        >
          <CampaignStatusManager
            campaign={{ ...group, stats }}
            onStatusChange={(status, data) => onUpdate(group.id, { status, ...data })}
            type="telegram"
          />
        </motion.div>
      )}
    </motion.div>
  );
};

export { BotCard, EmailCampaignCard, TelegramGroupCard };
