'use client';

import { motion } from 'framer-motion';
import { Headphones, Rocket, Shield, Globe, Sparkles, Target, Users, TrendingUp, Award, Zap, Music, Star } from 'lucide-react';

const ModernAboutSection = () => {
  const stats = [
    { 
      number: '10K+', 
      label: 'Продвинутых треков', 
      icon: <Music className="w-6 h-6" />, 
      color: 'from-purple-500 to-pink-500',
      delay: 0.1
    },
    { 
      number: '500+', 
      label: 'Довольных артистов', 
      icon: <Users className="w-6 h-6" />, 
      color: 'from-blue-500 to-cyan-500',
      delay: 0.2
    },
    { 
      number: '50M+', 
      label: 'Общих прослушиваний', 
      icon: <TrendingUp className="w-6 h-6" />, 
      color: 'from-green-500 to-emerald-500',
      delay: 0.3
    },
    { 
      number: '95%', 
      label: 'Успешных кампаний', 
      icon: <Award className="w-6 h-6" />, 
      color: 'from-yellow-500 to-orange-500',
      delay: 0.4
    },
  ];

  const features = [
    {
      icon: <Headphones className="w-8 h-8" />,
      title: 'AI-Powered анализ',
      description: 'Искусственный интеллект анализирует ваш трек и находит оптимальную стратегию продвижения для максимального охвата',
      color: 'from-purple-500 to-pink-500',
      delay: 0.1
    },
    {
      icon: <Rocket className="w-8 h-8" />,
      title: 'Быстрые результаты',
      description: 'Первые результаты уже через 24-48 часов после запуска кампании благодаря нашим алгоритмам',
      color: 'from-blue-500 to-cyan-500',
      delay: 0.2
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Гарантия качества',
      description: 'Возврат средств, если не достигнем заявленных показателей в течение 30 дней работы',
      color: 'from-green-500 to-emerald-500',
      delay: 0.3
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: 'Глобальный охват',
      description: 'Продвижение на всех популярных платформах: Spotify, Apple Music, YouTube, TikTok, Instagram',
      color: 'from-yellow-500 to-orange-500',
      delay: 0.4
    }
  ];

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-900/20 to-black">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-500/10 via-transparent to-transparent"></div>
        
        {/* Floating Elements */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40"
            animate={{
              x: [0, 50, 0],
              y: [0, -50, 0],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 6 + i * 0.3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            style={{
              left: `${15 + (i * 5) % 70}%`,
              top: `${25 + (i * 4) % 50}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-3 mb-6">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
              className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl"
            >
              <Sparkles className="w-6 h-6 text-white" />
            </motion.div>
            <h2 className="text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
              О H!VE Agency
            </h2>
          </div>
          
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Мы используем передовые AI-технологии для анализа и продвижения музыки. 
            Наша миссия — помочь каждому артисту найти свою аудиторию и достичь успеха.
          </p>
        </motion.div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: stat.delay }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-black/20 backdrop-blur-xl rounded-3xl p-8 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 text-center group"
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <div className="text-white">
                  {stat.icon}
                </div>
              </div>
              
              <motion.div
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: stat.delay + 0.2 }}
                viewport={{ once: true }}
                className="text-4xl font-bold text-white mb-2"
              >
                {stat.number}
              </motion.div>
              
              <p className="text-gray-400 font-medium">{stat.label}</p>
            </motion.div>
          ))}
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: feature.delay }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="bg-black/20 backdrop-blur-xl rounded-3xl p-8 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 group"
            >
              <div className="flex items-start gap-6">
                <div className={`flex-shrink-0 w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <div className="text-white">
                    {feature.icon}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-12 border border-purple-500/20 max-w-4xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Star className="w-8 h-8 text-yellow-400" />
              <h3 className="text-3xl font-bold text-white">Готовы к успеху?</h3>
              <Star className="w-8 h-8 text-yellow-400" />
            </div>
            
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Присоединяйтесь к тысячам артистов, которые уже достигли успеха с H!VE Agency
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-12 rounded-2xl transition-all duration-300 shadow-lg shadow-purple-500/25 text-lg"
            >
              <Zap className="w-6 h-6 inline mr-3" />
              Начать продвижение
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernAboutSection;
