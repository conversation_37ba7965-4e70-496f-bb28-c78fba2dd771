'use client';

import { useState } from 'react';
import { FaPhone, FaEnvelope, FaMapMarkerAlt, FaTelegram, FaWhatsapp, FaInstagram } from 'react-icons/fa';

const ContactsSection = () => {
  const [language] = useState('ru'); // This would come from context in real app
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    type: 'general',
    message: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const contactInfo = [
    {
      icon: <FaPhone className="text-2xl text-red-500" />,
      title: language === 'ru' ? 'Телефон' : 'Phone',
      value: '+7 (495) 123-45-67',
      link: 'tel:+74951234567',
    },
    {
      icon: <FaEnvelope className="text-2xl text-red-500" />,
      title: language === 'ru' ? 'Email' : 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>',
    },
    {
      icon: <FaMapMarkerAlt className="text-2xl text-red-500" />,
      title: language === 'ru' ? 'Адрес' : 'Address',
      value: language === 'ru' ? 'Москва, ул. Тверская, 1' : 'Moscow, Tverskaya St., 1',
      link: null,
    },
  ];

  const socialLinks = [
    {
      icon: <FaTelegram className="text-2xl" />,
      name: 'Telegram',
      url: 'https://t.me/hiveagency',
      color: 'hover:text-blue-500',
    },
    {
      icon: <FaWhatsapp className="text-2xl" />,
      name: 'WhatsApp',
      url: 'https://wa.me/74951234567',
      color: 'hover:text-green-500',
    },
    {
      icon: <FaInstagram className="text-2xl" />,
      name: 'Instagram',
      url: 'https://instagram.com/hiveagency',
      color: 'hover:text-pink-500',
    },
  ];

  return (
    <section id="contacts" className="py-16 md:py-32 bg-black">
      <div className="container-wide">
        {/* Header */}
        <div className="text-center mb-12 md:mb-24 max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-6 md:mb-10 text-center leading-tight">
            {language === 'ru' ? 'Контакты' : 'Contacts'}
          </h2>
          <div className="w-32 md:w-40 h-1 md:h-1.5 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16 rounded-full"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-5xl mx-auto leading-relaxed font-light">
            {language === 'ru'
              ? 'Готовы обсудить ваш проект? Свяжитесь с нами любым удобным способом'
              : 'Ready to discuss your project? Contact us in any convenient way'
            }
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-30 max-w-7xl mx-auto">
          {/* Contact Form */}
          <div className="card-monochrome rounded-3xl card-padding-lg group">
            <div className="relative z-10">
              <h3 className="text-xl md:text-2xl font-bold text-white mb-6 group-hover:text-red-50 transition-colors duration-300">
                {language === 'ru' ? 'Отправить сообщение' : 'Send Message'}
              </h3>
            
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-400 mb-2 group-hover:text-gray-300 transition-colors duration-300">
                    {language === 'ru' ? 'Имя' : 'Name'}
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-3 text-white focus:border-red-500/50 focus:bg-gray-800/70 focus:outline-none transition-all duration-300 backdrop-blur-sm"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-400 mb-2 group-hover:text-gray-300 transition-colors duration-300">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-3 text-white focus:border-red-500/50 focus:bg-gray-800/70 focus:outline-none transition-all duration-300 backdrop-blur-sm"
                    required
                  />
                </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {language === 'ru' ? 'Тип обращения' : 'Inquiry Type'}
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300"
                >
                  <option value="general">
                    {language === 'ru' ? 'Общий вопрос' : 'General Inquiry'}
                  </option>
                  <option value="artist">
                    {language === 'ru' ? 'Продвижение артиста' : 'Artist Promotion'}
                  </option>
                  <option value="label">
                    {language === 'ru' ? 'Сотрудничество с лейблом' : 'Label Partnership'}
                  </option>
                  <option value="booking">
                    {language === 'ru' ? 'Букинг' : 'Booking'}
                  </option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">
                  {language === 'ru' ? 'Сообщение' : 'Message'}
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300 resize-none"
                  required
                />
              </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-red-500 hover:to-red-600 text-white py-4 rounded-lg font-semibold text-lg transition-all duration-300 border border-gray-600/50 hover:border-red-500/50 hover:shadow-lg hover:shadow-red-500/25"
                >
                  {language === 'ru' ? 'Отправить' : 'Send'}
                </button>
              </form>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-8">
            {/* Contact Details */}
            <div className="card-monochrome rounded-3xl card-padding-lg group">
              <div className="relative z-10">
                <h3 className="text-xl md:text-2xl font-bold text-white mb-6 group-hover:text-red-50 transition-colors duration-300">
                  {language === 'ru' ? 'Контактная информация' : 'Contact Information'}
                </h3>

                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="flex-shrink-0 p-2 rounded-lg bg-gray-800/50 border border-gray-700/50 group-hover:border-red-500/30 transition-all duration-300">
                        <div className="text-gray-400 group-hover:text-red-400 transition-colors duration-300">
                          {info.icon}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-300">{info.title}</div>
                        {info.link ? (
                          <a
                            href={info.link}
                            className="text-white hover:text-red-400 transition-colors duration-300"
                          >
                            {info.value}
                          </a>
                        ) : (
                          <div className="text-white group-hover:text-red-50 transition-colors duration-300">{info.value}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="bg-black rounded-3xl card-padding-lg border border-gray-700">
              <h3 className="text-2xl font-bold text-white mb-6">
                {language === 'ru' ? 'Социальные сети' : 'Social Media'}
              </h3>
              
              <div className="flex space-x-6">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`text-gray-400 ${social.color} transition-colors duration-300`}
                    title={social.name}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>

            {/* Working Hours */}
            <div className="bg-black rounded-3xl card-padding-lg border border-gray-700">
              <h3 className="text-2xl font-bold text-white mb-6">
                {language === 'ru' ? 'Время работы' : 'Working Hours'}
              </h3>
              
              <div className="space-y-3 text-gray-300">
                <div className="flex justify-between">
                  <span>{language === 'ru' ? 'Пн - Пт:' : 'Mon - Fri:'}</span>
                  <span>10:00 - 19:00</span>
                </div>
                <div className="flex justify-between">
                  <span>{language === 'ru' ? 'Сб:' : 'Sat:'}</span>
                  <span>11:00 - 16:00</span>
                </div>
                <div className="flex justify-between">
                  <span>{language === 'ru' ? 'Вс:' : 'Sun:'}</span>
                  <span>{language === 'ru' ? 'Выходной' : 'Closed'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactsSection;
