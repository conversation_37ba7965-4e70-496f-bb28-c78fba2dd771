'use client';

import { useState } from 'react';
import Link from 'next/link';
import { FaInstagram, FaTelegram, FaWhatsapp, FaYoutube, FaPhone, FaEnvelope } from 'react-icons/fa';

const Footer = () => {
  const [language] = useState('ru'); // This would come from context in real app

  const footerLinks = {
    services: {
      title: language === 'ru' ? 'Услуги' : 'Services',
      links: [
        { href: '#services', label: language === 'ru' ? 'Продвижение артистов' : 'Artist Promotion' },
        { href: '#services', label: language === 'ru' ? 'Продвижение треков' : 'Track Promotion' },
        { href: '#services', label: language === 'ru' ? 'Радио реклама' : 'Radio Advertising' },
        { href: '#services', label: language === 'ru' ? 'ТВ реклама' : 'TV Advertising' },
        { href: '#services', label: language === 'ru' ? 'Социальные сети' : 'Social Media' },
      ],
    },
    company: {
      title: language === 'ru' ? 'Компания' : 'Company',
      links: [
        { href: '#about', label: language === 'ru' ? 'О нас' : 'About Us' },
        { href: '#team', label: language === 'ru' ? 'Команда' : 'Team' },
        { href: '#partners', label: language === 'ru' ? 'Партнеры' : 'Partners' },
        { href: '/careers', label: language === 'ru' ? 'Карьера' : 'Careers' },
        { href: '/blog', label: language === 'ru' ? 'Блог' : 'Blog' },
      ],
    },
    support: {
      title: language === 'ru' ? 'Поддержка' : 'Support',
      links: [
        { href: '#contacts', label: language === 'ru' ? 'Контакты' : 'Contacts' },
        { href: '/faq', label: language === 'ru' ? 'FAQ' : 'FAQ' },
        { href: '/privacy', label: language === 'ru' ? 'Политика конфиденциальности' : 'Privacy Policy' },
        { href: '/terms', label: language === 'ru' ? 'Условия использования' : 'Terms of Service' },
        { href: '/help', label: language === 'ru' ? 'Помощь' : 'Help' },
      ],
    },
  };

  const socialLinks = [
    {
      icon: <FaInstagram className="text-xl" />,
      url: 'https://instagram.com/hiveagency',
      name: 'Instagram',
    },
    {
      icon: <FaTelegram className="text-xl" />,
      url: 'https://t.me/hiveagency',
      name: 'Telegram',
    },
    {
      icon: <FaWhatsapp className="text-xl" />,
      url: 'https://wa.me/74951234567',
      name: 'WhatsApp',
    },
    {
      icon: <FaYoutube className="text-xl" />,
      url: 'https://youtube.com/@hiveagency',
      name: 'YouTube',
    },
  ];

  return (
    <footer className="bg-black border-t border-gray-800">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link href="/" className="text-3xl font-bold text-white mb-6 block">
              <span className="text-red-500">H!</span>VE
            </Link>
            
            <p className="text-gray-300 mb-6 leading-relaxed">
              {language === 'ru' 
                ? 'Ведущее агенство по продвижению артистов и треков. Мы помогаем талантам достичь новых высот в музыкальной индустрии.'
                : 'Leading agency for artist and track promotion. We help talents reach new heights in the music industry.'
              }
            </p>

            {/* Contact Info */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-3">
                <FaPhone className="text-red-500" />
                <a href="tel:+74951234567" className="text-gray-300 hover:text-white transition-colors duration-300">
                  +7 (495) 123-45-67
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <FaEnvelope className="text-red-500" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors duration-300">
                  <EMAIL>
                </a>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gray-800 hover:bg-red-500 text-gray-300 hover:text-white p-3 rounded-full transition-all duration-300"
                  title={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key}>
              <h3 className="text-white font-semibold text-lg mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-red-500 transition-colors duration-300"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Newsletter Subscription */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-white font-semibold text-lg mb-2">
                {language === 'ru' ? 'Подпишитесь на новости' : 'Subscribe to Newsletter'}
              </h3>
              <p className="text-gray-300">
                {language === 'ru' 
                  ? 'Получайте последние новости музыкальной индустрии'
                  : 'Get the latest music industry news'
                }
              </p>
            </div>
            
            <div className="flex w-full md:w-auto">
              <input
                type="email"
                placeholder={language === 'ru' ? 'Ваш email' : 'Your email'}
                className="bg-gray-800 border border-gray-600 rounded-l-lg px-4 py-3 text-white focus:border-red-500 focus:outline-none transition-colors duration-300 flex-1 md:w-64"
              />
              <button className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-r-lg font-semibold transition-colors duration-300">
                {language === 'ru' ? 'Подписаться' : 'Subscribe'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © 2024 HIVE! Agency. {language === 'ru' ? 'Все права защищены.' : 'All rights reserved.'}
            </div>
            
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors duration-300">
                {language === 'ru' ? 'Политика конфиденциальности' : 'Privacy Policy'}
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors duration-300">
                {language === 'ru' ? 'Условия использования' : 'Terms of Service'}
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors duration-300">
                {language === 'ru' ? 'Файлы cookie' : 'Cookies'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
