'use client';

import { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  User, 
  Mail, 
  Phone, 
  Camera, 
  Save, 
  X, 
  Eye, 
  EyeOff,
  Upload,
  Check,
  AlertCircle,
  Settings,
  Bell,
  Shield,
  Palette,
  Globe,
  Music,
  Link as LinkIcon
} from 'lucide-react';

interface ProfileSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({ isOpen, onClose }) => {
  const { user, updateProfile } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [avatar, setAvatar] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
    password: '',
    newPassword: '',
    confirmPassword: '',
    links: user?.links || [''],
    notifications: {
      email: true,
      push: true,
      marketing: false,
      orders: true,
      analytics: true
    },
    privacy: {
      profileVisible: true,
      showEmail: false,
      showPhone: false
    },
    theme: 'dark',
    language: 'ru'
  });

  const handleInputChange = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedChange = (section: string, field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev] as Record<string, unknown>,
        [field]: value
      }
    }));
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatar(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLinkChange = (index: number, value: string) => {
    const newLinks = [...formData.links];
    newLinks[index] = value;
    setFormData(prev => ({ ...prev, links: newLinks }));
  };

  const addLink = () => {
    setFormData(prev => ({ ...prev, links: [...prev.links, ''] }));
  };

  const removeLink = (index: number) => {
    const newLinks = formData.links.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, links: newLinks }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Здесь будет логика сохранения профиля
      await updateProfile({
        name: formData.name,
        email: formData.email,
        bio: formData.bio,
        links: formData.links.filter(link => link.trim() !== ''),
        ...(formData.newPassword && { password: formData.newPassword })
      });
      
      // Показать уведомление об успехе
      alert('Профиль успешно обновлен!');
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Ошибка при обновлении профиля');
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Профиль', icon: <User className="w-4 h-4" /> },
    { id: 'account', label: 'Аккаунт', icon: <Settings className="w-4 h-4" /> },
    { id: 'notifications', label: 'Уведомления', icon: <Bell className="w-4 h-4" /> },
    { id: 'privacy', label: 'Приватность', icon: <Shield className="w-4 h-4" /> },
    { id: 'appearance', label: 'Внешний вид', icon: <Palette className="w-4 h-4" /> }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
      <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-800" style={{ padding: '20px 30px' }}>
          <h2 className="text-xl font-bold text-white" style={{ lineHeight: '1.4' }}>
            Настройки профиля
          </h2>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-800" style={{ padding: '20px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center rounded-lg transition-colors text-left ${
                    activeTab === tab.id
                      ? 'bg-red-500 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                  }`}
                  style={{ gap: '10px', padding: '12px 15px', lineHeight: '1.4' }}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto" style={{ padding: '30px' }}>
            {activeTab === 'profile' && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Информация профиля
                </h3>

                {/* Avatar Upload */}
                <div className="flex items-center" style={{ gap: '20px' }}>
                  <div className="relative">
                    <div className="w-20 h-20 rounded-full bg-gray-800 flex items-center justify-center overflow-hidden">
                      {avatarPreview ? (
                        <img src={avatarPreview} alt="Avatar" className="w-full h-full object-cover" />
                      ) : user?.avatar ? (
                        <img src={user.avatar} alt="Avatar" className="w-full h-full object-cover" />
                      ) : (
                        <User className="w-8 h-8 text-gray-400" />
                      )}
                    </div>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="absolute -bottom-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white hover:bg-red-600 transition-colors"
                    >
                      <Camera className="w-3 h-3" />
                    </button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                    />
                  </div>
                  <div>
                    <p className="text-white font-medium" style={{ lineHeight: '1.4' }}>Фото профиля</p>
                    <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>
                      JPG, PNG или GIF. Максимум 5MB.
                    </p>
                  </div>
                </div>

                {/* Form Fields */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Имя
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Биография
                    </label>
                    <textarea
                      value={formData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      rows={4}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.6' }}
                      placeholder="Расскажите о себе..."
                    />
                  </div>

                  {/* Social Links */}
                  <div>
                    <div className="flex items-center justify-between" style={{ marginBottom: '15px' }}>
                      <label className="text-sm text-gray-400" style={{ lineHeight: '1.4' }}>
                        Социальные сети
                      </label>
                      <button
                        onClick={addLink}
                        className="text-red-500 hover:text-red-400 text-sm flex items-center"
                        style={{ gap: '5px', lineHeight: '1.4' }}
                      >
                        <LinkIcon className="w-4 h-4" />
                        Добавить ссылку
                      </button>
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                      {formData.links.map((link, index) => (
                        <div key={index} className="flex items-center" style={{ gap: '10px' }}>
                          <input
                            type="url"
                            value={link}
                            onChange={(e) => handleLinkChange(index, e.target.value)}
                            className="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                            style={{ padding: '10px 14px', lineHeight: '1.4' }}
                            placeholder="https://..."
                          />
                          {formData.links.length > 1 && (
                            <button
                              onClick={() => removeLink(index)}
                              className="w-8 h-8 text-gray-400 hover:text-red-400 transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'account' && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Настройки аккаунта
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Email
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Телефон
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                      placeholder="+7 (999) 123-45-67"
                    />
                  </div>

                  {/* Password Change */}
                  <div className="border-t border-gray-800" style={{ paddingTop: '20px' }}>
                    <h4 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                      Изменить пароль
                    </h4>
                    
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                      <div>
                        <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                          Текущий пароль
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? 'text' : 'password'}
                            value={formData.password}
                            onChange={(e) => handleInputChange('password', e.target.value)}
                            className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                            style={{ padding: '12px 16px', paddingRight: '45px', lineHeight: '1.4' }}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                          >
                            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                          Новый пароль
                        </label>
                        <input
                          type="password"
                          value={formData.newPassword}
                          onChange={(e) => handleInputChange('newPassword', e.target.value)}
                          className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                          style={{ padding: '12px 16px', lineHeight: '1.4' }}
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                          Подтвердите новый пароль
                        </label>
                        <input
                          type="password"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                          className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500"
                          style={{ padding: '12px 16px', lineHeight: '1.4' }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Настройки уведомлений
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  {[
                    { key: 'email', label: 'Email уведомления', description: 'Получать уведомления на почту' },
                    { key: 'push', label: 'Push уведомления', description: 'Уведомления в браузере' },
                    { key: 'orders', label: 'Уведомления о заказах', description: 'Статус заказов и платежей' },
                    { key: 'analytics', label: 'Аналитика', description: 'Отчеты и статистика' },
                    { key: 'marketing', label: 'Маркетинг', description: 'Акции и специальные предложения' }
                  ].map((item) => (
                    <div key={item.key} className="flex items-center justify-between">
                      <div>
                        <p className="text-white font-medium" style={{ lineHeight: '1.4' }}>{item.label}</p>
                        <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>{item.description}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.notifications[item.key as keyof typeof formData.notifications]}
                          onChange={(e) => handleNestedChange('notifications', item.key, e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'privacy' && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Настройки приватности
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  {[
                    { key: 'profileVisible', label: 'Публичный профиль', description: 'Ваш профиль виден другим пользователям' },
                    { key: 'showEmail', label: 'Показывать email', description: 'Email будет виден в профиле' },
                    { key: 'showPhone', label: 'Показывать телефон', description: 'Номер телефона будет виден в профиле' }
                  ].map((item) => (
                    <div key={item.key} className="flex items-center justify-between">
                      <div>
                        <p className="text-white font-medium" style={{ lineHeight: '1.4' }}>{item.label}</p>
                        <p className="text-gray-400 text-sm" style={{ lineHeight: '1.6' }}>{item.description}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.privacy[item.key as keyof typeof formData.privacy]}
                          onChange={(e) => handleNestedChange('privacy', item.key, e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                      </label>
                    </div>
                  ))}
                </div>

                <div className="border-t border-gray-800" style={{ paddingTop: '20px' }}>
                  <h4 className="text-white font-medium" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                    Удаление аккаунта
                  </h4>
                  <p className="text-gray-400 text-sm" style={{ marginBottom: '15px', lineHeight: '1.6' }}>
                    Это действие нельзя отменить. Все ваши данные будут удалены навсегда.
                  </p>
                  <button className="bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors" style={{ padding: '10px 20px', lineHeight: '1.4' }}>
                    Удалить аккаунт
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'appearance' && (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
                <h3 className="text-lg font-semibold text-white" style={{ lineHeight: '1.4' }}>
                  Внешний вид
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '15px', lineHeight: '1.4' }}>
                      Тема
                    </label>
                    <div className="grid grid-cols-3" style={{ gap: '15px' }}>
                      {[
                        { value: 'dark', label: 'Темная', preview: 'bg-gray-900' },
                        { value: 'light', label: 'Светлая', preview: 'bg-white' },
                        { value: 'auto', label: 'Авто', preview: 'bg-gradient-to-r from-gray-900 to-white' }
                      ].map((theme) => (
                        <button
                          key={theme.value}
                          onClick={() => handleInputChange('theme', theme.value)}
                          className={`border-2 rounded-lg transition-colors ${
                            formData.theme === theme.value
                              ? 'border-red-500'
                              : 'border-gray-700 hover:border-gray-600'
                          }`}
                          style={{ padding: '15px' }}
                        >
                          <div className={`w-full h-12 rounded-lg ${theme.preview}`} style={{ marginBottom: '10px' }}></div>
                          <p className="text-white text-sm" style={{ lineHeight: '1.4' }}>{theme.label}</p>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm text-gray-400" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                      Язык
                    </label>
                    <select
                      value={formData.language}
                      onChange={(e) => handleInputChange('language', e.target.value)}
                      className="w-full bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-red-500"
                      style={{ padding: '12px 16px', lineHeight: '1.4' }}
                    >
                      <option value="ru">Русский</option>
                      <option value="en">English</option>
                      <option value="es">Español</option>
                      <option value="fr">Français</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-800 flex justify-end" style={{ padding: '20px 30px', gap: '15px' }}>
          <button
            onClick={onClose}
            className="bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            style={{ padding: '12px 20px', lineHeight: '1.4' }}
          >
            Отмена
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="bg-red-500 hover:bg-red-600 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center"
            style={{ padding: '12px 20px', lineHeight: '1.4', gap: '8px' }}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Сохранение...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Сохранить
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
