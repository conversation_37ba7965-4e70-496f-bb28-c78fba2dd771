'use client';

import { useState, useEffect } from 'react';
import { Bo<PERSON>, Send, Sparkles, Users, Music, ArrowRight, Clock, DollarSign } from 'lucide-react';

const AIHelperClient = () => {
  const [mounted, setMounted] = useState(false);
  const [activeCategory, setActiveCategory] = useState<'artist' | 'track'>('artist');
  const [userQuery, setUserQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showProposal, setShowProposal] = useState(false);

  useEffect(() => {
    // Дополнительная проверка на клиентскую среду
    if (typeof window !== 'undefined') {
      setMounted(true);
    }
  }, []);

  // Предотвращаем рендеринг до монтирования на клиенте
  if (!mounted || typeof window === 'undefined') {
    return null;
  }

  const prompts = {
    artist: [
      {
        id: '1',
        title: 'Продвижение нового релиза',
        description: 'Как эффективно продвинуть новый трек или альбом',
        text: 'Мне нужно продвинуть новый трек/альбом. Помоги составить стратегию продвижения.',
        popularity: 95
      },
      {
        id: '2',
        title: 'Рост в социальных сетях',
        description: 'Увеличение подписчиков и вовлеченности',
        text: 'Хочу увеличить количество подписчиков в Instagram, TikTok и YouTube. Какие сервисы посоветуешь?',
        popularity: 88
      },
      {
        id: '3',
        title: 'Построение бренда артиста',
        description: 'Создание узнаваемого образа и стиля',
        text: 'Помоги создать сильный бренд артиста. Нужна стратегия для узнаваемости.',
        popularity: 75
      }
    ],
    track: [
      {
        id: '4',
        title: 'Вирусная стратегия для трека',
        description: 'Как сделать трек вирусным в TikTok и других платформах',
        text: 'Хочу сделать свой трек вирусным в TikTok и других соцсетях. Какую стратегию посоветуешь?',
        popularity: 92
      },
      {
        id: '5',
        title: 'Увеличение прослушиваний',
        description: 'Рост стримов на Spotify, Apple Music, Яндекс.Музыка',
        text: 'Нужно увеличить количество прослушиваний трека на стриминговых платформах. Что делать?',
        popularity: 85
      },
      {
        id: '6',
        title: 'Попадание в плейлисты',
        description: 'Размещение трека в популярных плейлистах',
        text: 'Как попасть в популярные плейлисты на Spotify и других платформах?',
        popularity: 80
      }
    ]
  };

  const services = {
    artist: [
      {
        id: '1',
        platform: 'Instagram',
        name: 'Продвижение в Instagram',
        description: 'Органический рост подписчиков и вовлеченности',
        price: 15000,
        duration: 30,
        icon: '📷'
      },
      {
        id: '2',
        platform: 'TikTok',
        name: 'Вирусное продвижение в TikTok',
        description: 'Создание вирусного контента и трендов',
        price: 20000,
        duration: 21,
        icon: '🎵'
      },
      {
        id: '3',
        platform: 'YouTube',
        name: 'Развитие YouTube канала',
        description: 'Комплексное развитие канала артиста',
        price: 35000,
        duration: 60,
        icon: '📺'
      }
    ],
    track: [
      {
        id: '4',
        platform: 'Spotify',
        name: 'Продвижение на Spotify',
        description: 'Увеличение прослушиваний и попадание в плейлисты',
        price: 12000,
        duration: 14,
        icon: '🎧'
      },
      {
        id: '5',
        platform: 'TikTok',
        name: 'Создание тренда в TikTok',
        description: 'Превращение трека в вирусный тренд',
        price: 25000,
        duration: 21,
        icon: '🎵'
      },
      {
        id: '6',
        platform: 'Radio',
        name: 'Радиокампания',
        description: 'Продвижение трека на радиостанциях',
        price: 40000,
        duration: 30,
        icon: '📻'
      }
    ]
  };

  const handlePromptClick = (prompt: any) => {
    setUserQuery(prompt.text);
    generateProposal(prompt.text);
  };

  const generateProposal = async (query: string) => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setShowProposal(true);
    setIsLoading(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userQuery.trim()) {
      generateProposal(userQuery);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const currentServices = services[activeCategory];
  const currentPrompts = prompts[activeCategory];
  const totalPrice = currentServices.reduce((sum, service) => sum + service.price, 0);
  const maxDuration = Math.max(...currentServices.map(service => service.duration));

  return (
    <section className="py-20 bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              AI-Помощник <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">HIVE</span>
            </h2>
            <Sparkles className="w-8 h-8 text-yellow-400 animate-pulse" />
          </div>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            Умный помощник для продвижения музыки. Опишите свои цели, и я подберу оптимальную стратегию с расчетом стоимости и времени.
          </p>

          {/* Category Switcher */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800/50 rounded-2xl p-2 backdrop-blur-xl border border-gray-700/50">
              <button
                onClick={() => setActiveCategory('artist')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeCategory === 'artist'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Users className="w-5 h-5 inline-block mr-2" />
                Продвижение артистов
              </button>
              <button
                onClick={() => setActiveCategory('track')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeCategory === 'track'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Music className="w-5 h-5 inline-block mr-2" />
                Продвижение треков
              </button>
            </div>
          </div>
        </div>

        {!showProposal ? (
          <div className="max-w-4xl mx-auto">
            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="mb-12">
              <div className="relative">
                <textarea
                  value={userQuery}
                  onChange={(e) => setUserQuery(e.target.value)}
                  placeholder={`Опишите ваши цели по продвижению ${activeCategory === 'artist' ? 'артиста' : 'трека'}...`}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-2xl px-6 py-4 text-white placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500 transition-colors"
                  rows={4}
                />
                <button
                  type="submit"
                  disabled={!userQuery.trim() || isLoading}
                  className="absolute bottom-4 right-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-xl hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
            </form>

            {/* Quick Prompts */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-6 text-center">
                Или выберите готовый запрос:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {currentPrompts.map((prompt) => (
                  <div
                    key={prompt.id}
                    onClick={() => handlePromptClick(prompt)}
                    className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 cursor-pointer hover:border-blue-500 hover:bg-gray-800/70 transition-all duration-300 group"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">
                        {prompt.title}
                      </h4>
                      <div className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">
                        {prompt.popularity}%
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm">
                      {prompt.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // Proposal Display
          <div className="max-w-6xl mx-auto">
            {/* AI Analysis */}
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-600/10 border border-blue-500/30 rounded-2xl p-8 mb-8">
              <div className="flex items-center gap-3 mb-4">
                <Bot className="w-6 h-6 text-blue-400" />
                <h3 className="text-xl font-bold text-white">Анализ AI-помощника</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                На основе вашего запроса я рекомендую комплексную стратегию {activeCategory === 'artist' ? 'продвижения артиста' : 'продвижения трека'}. 
                Эта кампания охватит основные каналы и обеспечит максимальный охват вашей целевой аудитории.
              </p>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {currentServices.map((service, index) => (
                <div
                  key={service.id}
                  className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 hover:border-blue-500 transition-all duration-300 transform hover:scale-105"
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl bg-blue-500/20">
                      {service.icon}
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">{service.name}</h4>
                      <p className="text-sm text-gray-400">{service.platform}</p>
                    </div>
                  </div>
                  
                  <p className="text-gray-300 text-sm mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                    <div className="flex items-center gap-2 text-sm text-gray-400">
                      <Clock className="w-4 h-4" />
                      {service.duration} дней
                    </div>
                    <div className="text-lg font-bold text-white">
                      {formatPrice(service.price)}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Summary */}
            <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <DollarSign className="w-6 h-6 text-green-400" />
                    <h4 className="text-lg font-semibold text-white">Общая стоимость</h4>
                  </div>
                  <p className="text-3xl font-bold text-green-400">
                    {formatPrice(totalPrice)}
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Clock className="w-6 h-6 text-blue-400" />
                    <h4 className="text-lg font-semibold text-white">Время кампании</h4>
                  </div>
                  <p className="text-3xl font-bold text-blue-400">
                    {maxDuration} дней
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Sparkles className="w-6 h-6 text-purple-400" />
                    <h4 className="text-lg font-semibold text-white">Ожидаемый охват</h4>
                  </div>
                  <p className="text-3xl font-bold text-purple-400">
                    {activeCategory === 'artist' ? '500K-2M' : '1M-10M'}
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => setShowProposal(false)}
                  className="px-8 py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-colors"
                >
                  Изменить запрос
                </button>
                <button
                  onClick={() => {
                    alert('Предложение добавлено в дашборд! Менеджер свяжется с вами в ближайшее время.');
                  }}
                  className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl transition-all duration-300 flex items-center gap-2 justify-center"
                >
                  Заказать кампанию
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default AIHelperClient;
