'use client';

import { useState } from 'react';
import { ShoppingCart, CreditCard, CheckCircle } from 'lucide-react';

interface OrderCreatorProps {
  serviceType: 'artist' | 'track';
  packageType: string;
  title: string;
  description: string;
  price: number;
  onOrderCreated?: (orderId: string) => void;
}

const OrderCreatorStub: React.FC<OrderCreatorProps> = ({
  serviceType,
  packageType,
  title,
  description,
  price,
  onOrderCreated
}) => {
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateOrder = async () => {
    setIsCreating(true);
    
    // Имитация создания заказа
    setTimeout(() => {
      const orderId = `order_${Date.now()}`;
      alert(`Заказ создан!\n\nТип: ${serviceType}\nПакет: ${packageType}\nЦена: ${price.toLocaleString()} ₽\n\nID заказа: ${orderId}`);
      
      if (onOrderCreated) {
        onOrderCreated(orderId);
      }
      
      setIsCreating(false);
    }, 2000);
  };

  return (
    <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
      <div className="flex items-center gap-3 mb-4">
        <ShoppingCart className="w-6 h-6 text-red-500" />
        <h3 className="text-xl font-semibold text-white">Создать заказ</h3>
      </div>
      
      <div className="space-y-4">
        <div>
          <h4 className="text-lg font-medium text-white mb-2">{title}</h4>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
        
        <div className="flex items-center justify-between py-3 border-t border-gray-700">
          <span className="text-gray-300">Тип услуги:</span>
          <span className="text-white font-medium">{serviceType === 'artist' ? 'Артист' : 'Трек'}</span>
        </div>
        
        <div className="flex items-center justify-between py-3 border-t border-gray-700">
          <span className="text-gray-300">Пакет:</span>
          <span className="text-white font-medium">{packageType}</span>
        </div>
        
        <div className="flex items-center justify-between py-3 border-t border-gray-700">
          <span className="text-gray-300">Стоимость:</span>
          <span className="text-white font-bold text-lg">{price.toLocaleString()} ₽</span>
        </div>
        
        <button
          onClick={handleCreateOrder}
          disabled={isCreating}
          className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
        >
          {isCreating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              Создание заказа...
            </>
          ) : (
            <>
              <CreditCard className="w-5 h-5" />
              Создать заказ
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default OrderCreatorStub;
