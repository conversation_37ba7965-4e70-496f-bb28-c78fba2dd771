import React from 'react';

const ServicesSection: React.FC = () => {
  return (
    <section id="services" className="py-16 md:py-32 bg-black">
      <div className="container-wide">
        <div className="text-center mb-12 md:mb-24 max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-6 md:mb-10 text-center leading-tight">
            Услуги
          </h2>
          <div className="w-32 md:w-40 h-1 md:h-1.5 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16 rounded-full"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-5xl mx-auto leading-relaxed font-light">
            Профессиональное продвижение артистов и треков в музыкальной индустрии
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-30 max-w-7xl mx-auto">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-700/50 rounded-full mb-8 group-hover:bg-red-500/20 transition-all duration-300">
                <div className="text-red-500 text-4xl">🎵</div>
              </div>
              <h3 className="text-xl md:text-2xl font-bold text-white mb-6 text-center">Spotify Promotion</h3>
              <p className="text-gray-300 text-center leading-relaxed text-lg">Продвижение в плейлистах Spotify и увеличение прослушиваний</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-700/50 rounded-full mb-8 group-hover:bg-red-500/20 transition-all duration-300">
                <div className="text-red-500 text-4xl">📱</div>
              </div>
              <h3 className="text-xl md:text-2xl font-bold text-white mb-6 text-center">TikTok Campaigns</h3>
              <p className="text-gray-300 text-center leading-relaxed text-lg">Вирусные кампании в TikTok для максимального охвата</p>
            </div>
          </div>

          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gray-700/50 rounded-full mb-8 group-hover:bg-red-500/20 transition-all duration-300">
                <div className="text-red-500 text-4xl">📺</div>
              </div>
              <h3 className="text-xl md:text-2xl font-bold text-white mb-6 text-center">YouTube Promotion</h3>
              <p className="text-gray-300 text-center leading-relaxed text-lg">Продвижение клипов и увеличение подписчиков</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
