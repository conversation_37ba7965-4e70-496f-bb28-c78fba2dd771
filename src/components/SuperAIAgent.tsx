'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Sparkles,
  Upload,
  Music,
  Mic,
  Play,
  Pause,
  Volume2,
  Headphones,
  Zap,
  Target,
  TrendingUp,
  Star,
  Send,
  Loader2,
  FileAudio,
  BarChart3,
  Eye,
  Heart,
  Share2,
  Download,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { orderService } from '@/services/orderService';
import OrderModal from './OrderModal';

interface TrackAnalysis {
  genre: string;
  subgenre: string;
  bpm: number;
  key: string;
  energy: number;
  danceability: number;
  valence: number;
  potential: {
    commercial: number;
    viral: number;
    radio: number;
    club: number;
  };
  recommendations: string[];
  targetAudience: string[];
  platforms: {
    name: string;
    score: number;
    reason: string;
  }[];
}

interface AIConversation {
  id: string;
  type: 'user' | 'ai';
  message: string;
  timestamp: Date;
  trackAnalysis?: TrackAnalysis;
  audioFile?: File;
}

interface ServiceCard {
  id: string;
  title: string;
  description: string;
  price: number;
  expectedResults: {
    newListeners: number;
    sales: number;
    reach: number;
  };
  platforms: string[];
  duration: string;
  icon: string;
}

interface ServicePackage {
  totalPrice: number;
  totalListeners: number;
  totalSales: number;
  totalReach: number;
  services: ServiceCard[];
}

const SuperAIAgent = () => {
  const { user } = useAuth();
  const [activeMode, setActiveMode] = useState<'artist' | 'track'>('track');
  const [userInput, setUserInput] = useState('');
  const [conversations, setConversations] = useState<AIConversation[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedTrack, setUploadedTrack] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [trackAnalysis, setTrackAnalysis] = useState<TrackAnalysis | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState<'input' | 'analysis' | 'recommendations'>('input');
  const [mounted, setMounted] = useState(false);
  const [servicePackage, setServicePackage] = useState<ServicePackage | null>(null);
  const [showServices, setShowServices] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const messageIdRef = useRef(0);

  // Предотвращение ошибок гидратации
  useEffect(() => {
    setMounted(true);
  }, []);

  // Генерация пакета сервисов на основе анализа
  const generateServicePackage = useCallback((mode: 'artist' | 'track', userQuery: string): ServicePackage => {
    const services: ServiceCard[] = [];

    if (mode === 'track') {
      services.push(
        {
          id: 'spotify-promotion',
          title: 'Продвижение на Spotify',
          description: 'Плейлисты, алгоритмы, органический рост',
          price: 25000,
          expectedResults: { newListeners: 5000, sales: 200, reach: 50000 },
          platforms: ['Spotify', 'Apple Music'],
          duration: '30 дней',
          icon: '🎵'
        },
        {
          id: 'social-media',
          title: 'SMM продвижение',
          description: 'Instagram, TikTok, YouTube Shorts',
          price: 15000,
          expectedResults: { newListeners: 3000, sales: 100, reach: 100000 },
          platforms: ['Instagram', 'TikTok', 'YouTube'],
          duration: '30 дней',
          icon: '📱'
        },
        {
          id: 'radio-clubs',
          title: 'Радио и клубы',
          description: 'Ротация на радио, клубные диджеи',
          price: 35000,
          expectedResults: { newListeners: 8000, sales: 300, reach: 200000 },
          platforms: ['Радио', 'Клубы'],
          duration: '60 дней',
          icon: '📻'
        }
      );
    } else {
      services.push(
        {
          id: 'artist-branding',
          title: 'Брендинг артиста',
          description: 'Создание имиджа, фотосессии, контент',
          price: 40000,
          expectedResults: { newListeners: 10000, sales: 500, reach: 150000 },
          platforms: ['Instagram', 'TikTok', 'YouTube'],
          duration: '45 дней',
          icon: '🎭'
        },
        {
          id: 'pr-campaign',
          title: 'PR кампания',
          description: 'Интервью, статьи, медиа покрытие',
          price: 30000,
          expectedResults: { newListeners: 7000, sales: 250, reach: 300000 },
          platforms: ['Медиа', 'Блоги', 'Подкасты'],
          duration: '30 дней',
          icon: '📰'
        },
        {
          id: 'email-marketing',
          title: 'Email маркетинг',
          description: 'Рассылки, фан-база, лояльность',
          price: 20000,
          expectedResults: { newListeners: 4000, sales: 400, reach: 80000 },
          platforms: ['Email', 'Telegram'],
          duration: '60 дней',
          icon: '📧'
        }
      );
    }

    const totalPrice = services.reduce((sum, service) => sum + service.price, 0);
    const totalListeners = services.reduce((sum, service) => sum + service.expectedResults.newListeners, 0);
    const totalSales = services.reduce((sum, service) => sum + service.expectedResults.sales, 0);
    const totalReach = services.reduce((sum, service) => sum + service.expectedResults.reach, 0);

    return {
      totalPrice,
      totalListeners,
      totalSales,
      totalReach,
      services
    };
  }, []);

  // Симуляция анализа трека с AI
  const analyzeTrack = useCallback(async (file: File): Promise<TrackAnalysis> => {
    setIsAnalyzing(true);
    
    // Симуляция AI анализа
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const analysis: TrackAnalysis = {
      genre: 'Electronic',
      subgenre: 'Progressive House',
      bpm: 128,
      key: 'A minor',
      energy: 0.85,
      danceability: 0.92,
      valence: 0.78,
      potential: {
        commercial: 87,
        viral: 73,
        radio: 65,
        club: 94
      },
      recommendations: [
        'Увеличить басовую линию для клубного звучания',
        'Добавить вокальные хуки для коммерческого потенциала',
        'Рассмотреть ремикс в стиле Future House',
        'Оптимизировать для TikTok (30-секундный хук)'
      ],
      targetAudience: [
        'Любители электронной музыки 18-35 лет',
        'Клубная аудитория',
        'Фестивальная публика',
        'TikTok пользователи'
      ],
      platforms: [
        { name: 'Spotify', score: 89, reason: 'Отличная энергетика для плейлистов' },
        { name: 'TikTok', score: 76, reason: 'Хороший потенциал для танцевальных видео' },
        { name: 'YouTube', score: 82, reason: 'Подходит для музыкальных каналов' },
        { name: 'SoundCloud', score: 91, reason: 'Идеально для андеграунд сцены' }
      ]
    };
    
    setIsAnalyzing(false);
    return analysis;
  }, []);

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadedTrack(file);
    
    // Анализируем трек
    const analysis = await analyzeTrack(file);
    setTrackAnalysis(analysis);
    
    // Добавляем сообщение пользователя
    const userMessage: AIConversation = {
      id: `user-${++messageIdRef.current}`,
      type: 'user',
      message: `Загружен трек: ${file.name}`,
      timestamp: new Date(),
      audioFile: file
    };
    
    // Добавляем AI ответ с анализом
    const aiMessage: AIConversation = {
      id: `ai-${++messageIdRef.current}`,
      type: 'ai',
      message: `🎵 Анализ завершен! Ваш трек "${file.name}" имеет отличный потенциал!`,
      timestamp: new Date(),
      trackAnalysis: analysis
    };
    
    setConversations(prev => [...prev, userMessage, aiMessage]);
    setCurrentStep('analysis');
  }, [analyzeTrack]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim() || isProcessing) return;

    setIsProcessing(true);
    
    const userMessage: AIConversation = {
      id: `user-${++messageIdRef.current}`,
      type: 'user',
      message: userInput,
      timestamp: new Date()
    };
    
    setConversations(prev => [...prev, userMessage]);
    setUserInput('');
    
    // Симуляция AI ответа с генерацией сервисов
    setTimeout(() => {
      const aiResponse: AIConversation = {
        id: `ai-${++messageIdRef.current}`,
        type: 'ai',
        message: `🤖 Отлично! Я проанализировал ваш запрос и подготовил персональный план продвижения.

Основываясь на вашем описании, я рекомендую следующую стратегию продвижения с ожидаемыми результатами:`,
        timestamp: new Date()
      };

      setConversations(prev => [...prev, aiResponse]);

      // Генерируем пакет сервисов через 2 секунды
      setTimeout(() => {
        const servicePackageData = generateServicePackage(activeMode, userInput);
        setServicePackage(servicePackageData);
        setShowServices(true);
        setCurrentStep('recommendations');
      }, 2000);

      setIsProcessing(false);
    }, 1500);
  };

  const togglePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const scrollToServices = () => {
    const servicesElement = document.getElementById('services-section');
    if (servicesElement) {
      servicesElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const handleOrderPackage = () => {
    if (!user) {
      alert('Пожалуйста, войдите в систему для оформления заказа');
      return;
    }
    setShowOrderModal(true);
  };

  const handleOrderSubmit = async (formData: any) => {
    if (!servicePackage || !user) return;

    const orderData = {
      userId: user.$id,
      type: activeMode,
      services: servicePackage.services.map(service => ({
        serviceId: service.id,
        title: service.title,
        platform: service.platforms.join(', '),
        price: service.price,
        estimatedReach: service.expectedResults.reach.toLocaleString()
      })),
      totalAmount: servicePackage.totalPrice,
      status: 'pending' as const,
      projectData: {
        title: formData.name,
        description: formData.message,
        targetAudience: servicePackage.services.flatMap(s => s.platforms).join(', '),
        goals: `Продвижение ${activeMode === 'artist' ? 'артиста' : 'трека'} с бюджетом ${servicePackage.totalPrice.toLocaleString()} ₽`
      },
      deliverables: servicePackage.services.map(service => ({
        title: service.title,
        description: service.description,
        status: 'pending' as const,
        dueDate: new Date(Date.now() + parseInt(service.duration) * 24 * 60 * 60 * 1000)
      })),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await orderService.createOrder(orderData);
  };

  // Предотвращение ошибок гидратации
  if (!mounted) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-500/20 via-transparent to-transparent"></div>
          <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,_var(--tw-gradient-stops))] from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-spin-slow"></div>
        </div>
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-white text-xl">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-500/20 via-transparent to-transparent"></div>
        
        {/* Floating Particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-60"
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 8 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            style={{
              left: `${10 + (i * 4) % 80}%`,
              top: `${20 + (i * 3) % 60}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 px-1 py-8"> {/* 5px отступы от краев */}
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8 max-w-6xl mx-auto"
        >
          <div className="inline-flex items-center gap-4 mb-8 bg-gradient-to-r from-purple-500/10 to-blue-500/10 backdrop-blur-xl border border-purple-500/20 rounded-3xl px-8 py-6 shadow-2xl shadow-purple-500/10">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg"
            >
              <Headphones className="w-10 h-10 text-white" />
            </motion.div>
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
              H!VE AI
            </h1>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Sparkles className="w-10 h-10 text-yellow-400" />
            </motion.div>
          </div>

          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Супер-умный AI помощник для анализа и продвижения музыки.
            Загрузите трек для глубокого анализа или опишите ваш проект.
          </p>
        </motion.div>

        {/* Mode Switcher */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center mb-10 max-w-6xl mx-auto"
        >
          <div className="bg-gradient-to-r from-black/40 to-gray-900/40 backdrop-blur-xl rounded-3xl p-3 border border-purple-500/30 shadow-2xl shadow-purple-500/10">
            {(['artist', 'track'] as const).map((mode) => (
              <button
                key={mode}
                onClick={() => setActiveMode(mode)}
                className={`px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-500 transform ${
                  activeMode === mode
                    ? 'bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 text-white shadow-2xl shadow-purple-500/40 scale-105 border border-purple-400/50'
                    : 'text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 hover:scale-102 hover:shadow-lg'
                }`}
              >
                <span className="flex items-center gap-3">
                  {mode === 'artist' ? '🎤' : '🎵'}
                  {mode === 'artist' ? 'Артист' : 'Трек'}
                </span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Main Interface */}
        <div className="max-w-7xl mx-auto px-2"> {/* Увеличенная ширина с минимальными отступами */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Уменьшенный gap для больше места */}

            {/* Input Section */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-8"
            >
              {/* Track Upload - только для треков */}
              {activeMode === 'track' && (
                <div className="bg-gradient-to-br from-black/30 to-gray-900/30 backdrop-blur-xl rounded-3xl p-10 border border-purple-500/30 hover:border-purple-500/50 transition-all duration-500 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20">
                  <div className="text-center">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.9 }}
                      className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-3xl mb-8 cursor-pointer shadow-2xl shadow-purple-500/30 hover:shadow-purple-500/50 transition-all duration-300"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="w-12 h-12 text-white" />
                    </motion.div>

                    <h3 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                      Загрузить трек для анализа
                    </h3>
                    <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                      AI проанализирует жанр, стиль, потенциал и даст персональные рекомендации
                  </p>

                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="audio/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => fileInputRef.current?.click()}
                      className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg shadow-purple-500/25"
                    >
                      <FileAudio className="w-5 h-5 inline mr-2" />
                      Выбрать файл
                    </motion.button>
                  </div>
                </div>
              )}

              {/* Text Input */}
              <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/40 backdrop-blur-xl rounded-3xl border border-cyan-400/30 hover:border-cyan-400/50 transition-all duration-500 shadow-2xl shadow-cyan-500/20">
                <form onSubmit={handleSubmit} className="p-2 space-y-8">
                  <div className="relative"> {/* Убираем дополнительные отступы */}
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-2xl blur-xl"></div>
                    <textarea
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      placeholder={activeMode === 'track'
                        ? `Опишите ваш трек для анализа...\n\nНапример: "Мой трек в стиле progressive house, BPM 128, хочу продвинуть на Spotify и в клубах. Целевая аудитория 18-35 лет, бюджет 50к рублей"`
                        : `Расскажите о вашем артистическом проекте...\n\nНапример: "Я исполнитель в стиле indie pop, выпустил 3 сингла, хочу увеличить аудиторию в Instagram и TikTok. Целевая аудитория 16-30 лет, бюджет 30к рублей"`
                      }
                      className="relative w-full h-80 bg-gradient-to-br from-indigo-950/80 to-purple-950/80 backdrop-blur-xl border-2 border-cyan-400/40 rounded-2xl px-8 py-6 text-white text-lg placeholder-cyan-200/60 focus:outline-none focus:border-cyan-400/80 focus:ring-4 focus:ring-cyan-400/30 transition-all duration-500 resize-none overflow-hidden shadow-xl shadow-cyan-500/20 hover:shadow-cyan-500/30"
                      disabled={isProcessing}
                    />
                  </div>

                  <div className="relative"> {/* Убираем дополнительные отступы */}
                    <motion.button
                      type="submit"
                      disabled={!userInput.trim() || isProcessing}
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 hover:from-cyan-600 hover:via-blue-600 hover:to-indigo-600 disabled:from-gray-600 disabled:to-gray-700 text-white font-bold text-lg py-6 px-10 rounded-2xl transition-all duration-500 shadow-2xl shadow-cyan-500/40 hover:shadow-cyan-500/60 flex items-center justify-center gap-4 border border-cyan-400/30 hover:border-cyan-400/50"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="w-6 h-6 animate-spin" />
                          <span className="bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
                            AI анализирует...
                          </span>
                        </>
                      ) : (
                        <>
                          <Send className="w-6 h-6" />
                          <span className="bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
                            Отправить AI
                          </span>
                        </>
                      )}
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>

            {/* Chat/Analysis Section */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="bg-black/20 backdrop-blur-xl rounded-3xl border border-purple-500/20 overflow-hidden"
            >
              <div className="p-6 border-b border-purple-500/20">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <h3 className="text-xl font-bold text-white">AI Ассистент</h3>
                  <Zap className="w-5 h-5 text-yellow-400" />
                </div>
              </div>

              <div className="h-96 overflow-y-auto p-6 space-y-4">
                <AnimatePresence>
                  {conversations.length === 0 ? (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-12"
                    >
                      <Headphones className="w-16 h-16 text-purple-400 mx-auto mb-4 opacity-50" />
                      <p className="text-gray-400">
                        Загрузите трек или опишите ваш проект для начала анализа
                      </p>
                    </motion.div>
                  ) : (
                    conversations.map((conv) => (
                      <motion.div
                        key={conv.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className={`flex ${conv.type === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] p-4 rounded-2xl ${
                          conv.type === 'user'
                            ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                            : 'bg-black/30 text-gray-100 border border-purple-500/20'
                        }`}>
                          <p className="whitespace-pre-wrap">{conv.message}</p>
                          {conv.audioFile && (
                            <div className="mt-3 p-3 bg-black/20 rounded-xl">
                              <div className="flex items-center gap-3">
                                <Music className="w-5 h-5 text-purple-400" />
                                <span className="text-sm text-gray-300">{conv.audioFile.name}</span>
                              </div>
                            </div>
                          )}
                          {conv.type === 'ai' && servicePackage && (
                            <motion.button
                              onClick={scrollToServices}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 1, duration: 0.5 }}
                              className="mt-4 w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-cyan-500/30"
                            >
                              <Eye className="w-5 h-5" />
                              Посмотреть результат
                              <ChevronDown className="w-5 h-5 animate-bounce" />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>
                    ))
                  )}
                </AnimatePresence>

                {isAnalyzing && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-start"
                  >
                    <div className="bg-black/30 border border-purple-500/20 p-4 rounded-2xl">
                      <div className="flex items-center gap-3">
                        <Loader2 className="w-5 h-5 animate-spin text-purple-400" />
                        <span className="text-gray-300">AI анализирует ваш трек...</span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>

          {/* Service Package Cards */}
          {showServices && servicePackage && (
            <motion.div
              id="services-section"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="mt-16 max-w-7xl mx-auto px-2"
            >
              {/* Services Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {servicePackage.services.map((service, index) => (
                  <motion.div
                    key={service.id}
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    whileHover={{ scale: 1.05, y: -10 }}
                    className="relative group"
                  >
                    {/* Connection Lines */}
                    {index < servicePackage.services.length - 1 && (
                      <motion.div
                        initial={{ scaleX: 0 }}
                        animate={{ scaleX: 1 }}
                        transition={{ duration: 1, delay: index * 0.3 + 0.8 }}
                        className="absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 z-10 hidden lg:block origin-left"
                      />
                    )}

                    <div className="bg-gradient-to-br from-slate-800/80 to-indigo-900/80 backdrop-blur-xl rounded-3xl p-8 border border-cyan-400/30 hover:border-cyan-400/60 transition-all duration-500 shadow-2xl shadow-cyan-500/20 hover:shadow-cyan-500/40 h-full">
                      {/* Service Icon */}
                      <div className="text-6xl mb-6 text-center">{service.icon}</div>

                      {/* Service Info */}
                      <h3 className="text-2xl font-bold text-white mb-4 text-center bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                        {service.title}
                      </h3>

                      <p className="text-gray-300 mb-6 text-center leading-relaxed">
                        {service.description}
                      </p>

                      {/* Platforms */}
                      <div className="flex flex-wrap gap-2 mb-6 justify-center">
                        {service.platforms.map((platform) => (
                          <span
                            key={platform}
                            className="px-3 py-1 bg-cyan-500/20 text-cyan-300 rounded-full text-sm border border-cyan-400/30"
                          >
                            {platform}
                          </span>
                        ))}
                      </div>

                      {/* Expected Results */}
                      <div className="space-y-3 mb-6">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Новые слушатели:</span>
                          <span className="text-green-400 font-semibold">+{service.expectedResults.newListeners.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Продажи:</span>
                          <span className="text-green-400 font-semibold">+{service.expectedResults.sales}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Охват:</span>
                          <span className="text-green-400 font-semibold">{service.expectedResults.reach.toLocaleString()}</span>
                        </div>
                      </div>

                      {/* Price and Duration */}
                      <div className="text-center border-t border-cyan-400/20 pt-6">
                        <div className="text-3xl font-bold text-white mb-2">
                          {service.price.toLocaleString()} ₽
                        </div>
                        <div className="text-cyan-400 text-sm">
                          {service.duration}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Total Package Summary */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.5 }}
                className="bg-gradient-to-br from-indigo-900/60 to-purple-900/60 backdrop-blur-xl rounded-3xl p-12 border border-cyan-400/40 shadow-2xl shadow-cyan-500/30"
              >
                <h2 className="text-4xl font-bold text-center text-white mb-8 bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                  Полный пакет продвижения
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">
                      +{servicePackage.totalListeners.toLocaleString()}
                    </div>
                    <div className="text-gray-300">Новых слушателей</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">
                      +{servicePackage.totalSales}
                    </div>
                    <div className="text-gray-300">Продаж</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">
                      {servicePackage.totalReach.toLocaleString()}
                    </div>
                    <div className="text-gray-300">Общий охват</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-cyan-400 mb-2">
                      {servicePackage.totalPrice.toLocaleString()} ₽
                    </div>
                    <div className="text-gray-300">Общая стоимость</div>
                  </div>
                </div>

                <div className="text-center">
                  <motion.button
                    onClick={handleOrderPackage}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 hover:from-cyan-600 hover:via-blue-600 hover:to-indigo-600 text-white font-bold text-xl py-6 px-16 rounded-2xl transition-all duration-500 shadow-2xl shadow-cyan-500/40 hover:shadow-cyan-500/60 border border-cyan-400/30 hover:border-cyan-400/50 flex items-center gap-4"
                  >
                    🚀 Заказать пакет сервисов
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Order Modal */}
      <OrderModal
        isOpen={showOrderModal}
        onClose={() => setShowOrderModal(false)}
        servicePackage={servicePackage}
        onSubmit={handleOrderSubmit}
      />
    </div>
  );
};

export default SuperAIAgent;
