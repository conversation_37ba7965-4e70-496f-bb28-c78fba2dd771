'use client';

import { motion } from 'framer-motion';
import { Check, Star, Zap, Crown, Rocket, Sparkles, TrendingUp, Target } from 'lucide-react';

const ModernPricingSection = () => {
  const plans = [
    {
      name: 'Starter',
      price: '15,000',
      period: 'месяц',
      description: 'Идеально для начинающих артистов',
      icon: <Rocket className="w-8 h-8" />,
      color: 'from-blue-500 to-cyan-500',
      borderColor: 'border-blue-500/30',
      popular: false,
      features: [
        'AI анализ трека',
        'Продвижение на 3 платформах',
        'Базовая аналитика',
        '10K потенциальных прослушиваний',
        'Email поддержка',
        'Отчеты раз в неделю'
      ]
    },
    {
      name: 'Professional',
      price: '35,000',
      period: 'месяц',
      description: 'Для серьезного продвижения',
      icon: <Star className="w-8 h-8" />,
      color: 'from-purple-500 to-pink-500',
      borderColor: 'border-purple-500/50',
      popular: true,
      features: [
        'Расширенный AI анализ',
        'Продвижение на 7 платформах',
        'Продвинутая аналитика',
        '50K потенциальных прослушиваний',
        'Приоритетная поддержка',
        'Ежедневные отчеты',
        'Персональный менеджер',
        'A/B тестирование кампаний'
      ]
    },
    {
      name: 'Enterprise',
      price: '75,000',
      period: 'месяц',
      description: 'Максимальный результат',
      icon: <Crown className="w-8 h-8" />,
      color: 'from-yellow-500 to-orange-500',
      borderColor: 'border-yellow-500/30',
      popular: false,
      features: [
        'Полный AI анализ + прогнозы',
        'Продвижение на всех платформах',
        'Премиум аналитика + инсайты',
        '200K+ потенциальных прослушиваний',
        '24/7 VIP поддержка',
        'Реальтайм отчеты',
        'Команда экспертов',
        'Кастомные стратегии',
        'Гарантия результата'
      ]
    }
  ];

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-indigo-900/20 to-black">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-indigo-500/10 via-transparent to-transparent"></div>
        
        {/* Animated Grid */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}></div>
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-3 mb-6">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl"
            >
              <TrendingUp className="w-6 h-6 text-white" />
            </motion.div>
            <h2 className="text-5xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              Тарифные планы
            </h2>
          </div>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Выберите план, который подходит для ваших целей. Все планы включают AI-анализ и гарантию результата.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -10 }}
              className={`relative bg-black/20 backdrop-blur-xl rounded-3xl p-8 border ${plan.borderColor} hover:border-opacity-60 transition-all duration-300 ${
                plan.popular ? 'ring-2 ring-purple-500/50 shadow-2xl shadow-purple-500/20' : ''
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full text-sm font-bold flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    Популярный
                  </div>
                </div>
              )}

              {/* Header */}
              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${plan.color} rounded-2xl mb-6`}>
                  <div className="text-white">
                    {plan.icon}
                  </div>
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-6">{plan.description}</p>
                
                <div className="flex items-baseline justify-center gap-2">
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  <span className="text-gray-400">₽/{plan.period}</span>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <motion.div
                    key={featureIndex}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 + featureIndex * 0.05 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-3"
                  >
                    <div className={`flex-shrink-0 w-6 h-6 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center`}>
                      <Check className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-300">{feature}</span>
                  </motion.div>
                ))}
              </div>

              {/* CTA Button */}
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`w-full py-4 px-6 rounded-2xl font-bold text-white transition-all duration-300 ${
                  plan.popular
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg shadow-purple-500/25'
                    : `bg-gradient-to-r ${plan.color} hover:shadow-lg transition-all duration-300`
                }`}
              >
                <Target className="w-5 h-5 inline mr-2" />
                Выбрать план
              </motion.button>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-8 border border-indigo-500/20 max-w-2xl mx-auto">
            <Zap className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-4">Нужен индивидуальный план?</h3>
            <p className="text-gray-300 mb-6">
              Свяжитесь с нами для создания персонального предложения под ваши цели и бюджет
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300"
            >
              Связаться с нами
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernPricingSection;
