'use client';

import { useState } from 'react';
import { Crown, Rocket, Check, Music, CreditCard, Bitcoin, Smartphone, Zap, Users, BarChart3, Shield, Star } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  type: string;
  price: number;
  currency: string;
  duration: number;
  popular: boolean;
  features: Array<{
    id: string;
    name: string;
    description: string;
    limit?: number;
  }>;
  limits: Record<string, number>;
}

const PricingSectionNew = () => {
  const user = null; // Временно отключаем аутентификацию
  const [activeTab, setActiveTab] = useState<'artist' | 'track'>('artist');
  const [selectedTier, setSelectedTier] = useState<PricingPlan | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Простые данные тарифов
  const artistPlans = [
    {
      id: 'artist-starter',
      name: 'Старт',
      type: 'artist',
      price: 25000,
      currency: 'RUB',
      duration: 30,
      popular: false,
      features: [
        { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', limit: 3 },
        { id: 'basic-bots', name: 'Базовые боты', description: 'Автоматизация лайков и комментариев', limit: 2 },
        { id: 'email-campaigns', name: 'Email рассылки', description: 'Рассылки для фанатов', limit: 5 },
        { id: 'telegram-growth', name: 'Рост Telegram', description: 'Привлечение подписчиков в Telegram', limit: 1000 },
      ],
      limits: { socialAccounts: 3, botActivations: 2, emailCampaigns: 5, telegramMembers: 1000 }
    },
    {
      id: 'artist-pro',
      name: 'Профи',
      type: 'artist',
      price: 50000,
      currency: 'RUB',
      duration: 30,
      popular: true,
      features: [
        { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', limit: 8 },
        { id: 'advanced-bots', name: 'Продвинутые боты', description: 'ИИ боты для всех платформ', limit: 6 },
        { id: 'email-campaigns', name: 'Email рассылки', description: 'Неограниченные рассылки' },
        { id: 'telegram-growth', name: 'Рост Telegram', description: 'Массовое привлечение подписчиков', limit: 5000 },
      ],
      limits: { socialAccounts: 8, botActivations: 6, emailCampaigns: -1, telegramMembers: 5000 }
    },
    {
      id: 'artist-enterprise',
      name: 'Энтерпрайз',
      type: 'artist',
      price: 100000,
      currency: 'RUB',
      duration: 30,
      popular: false,
      features: [
        { id: 'unlimited-accounts', name: 'Неограниченные аккаунты', description: 'Без лимитов на количество' },
        { id: 'ai-bots', name: 'ИИ боты премиум', description: 'Самые умные боты с GPT-4' },
        { id: 'viral-strategies', name: 'Вирусные стратегии', description: 'Создание вирусного контента' },
        { id: 'personal-manager', name: 'Персональный менеджер', description: 'Выделенный специалист' },
      ],
      limits: { socialAccounts: -1, botActivations: -1, emailCampaigns: -1, telegramMembers: -1 }
    }
  ];

  const trackPlans = [
    {
      id: 'track-basic',
      name: 'Базовый',
      type: 'track',
      price: 15000,
      currency: 'RUB',
      duration: 14,
      popular: false,
      features: [
        { id: 'playlist-placement', name: 'Размещение в плейлистах', description: 'Spotify, Apple Music, Яндекс', limit: 10 },
        { id: 'social-promotion', name: 'Продвижение в соцсетях', description: 'Instagram, TikTok, VK' },
        { id: 'basic-bots', name: 'Базовые боты', description: 'Автолайки и репосты', limit: 2 },
      ],
      limits: { socialAccounts: 2, botActivations: 2, emailCampaigns: 1, telegramMembers: 500 }
    },
    {
      id: 'track-viral',
      name: 'Вирусный',
      type: 'track',
      price: 35000,
      currency: 'RUB',
      duration: 21,
      popular: true,
      features: [
        { id: 'viral-campaign', name: 'Вирусная кампания', description: 'Создание трендового контента' },
        { id: 'influencer-network', name: 'Сеть инфлюенсеров', description: 'Охват через блогеров', limit: 20 },
        { id: 'ai-content', name: 'ИИ контент', description: 'Генерация мемов и видео' },
      ],
      limits: { socialAccounts: 5, botActivations: 8, emailCampaigns: 3, telegramMembers: 2000 }
    },
    {
      id: 'track-platinum',
      name: 'Платиновый',
      type: 'track',
      price: 75000,
      currency: 'RUB',
      duration: 30,
      popular: false,
      features: [
        { id: 'platinum-campaign', name: 'Платиновая кампания', description: 'Максимальный охват' },
        { id: 'celebrity-network', name: 'Сеть знаменитостей', description: 'Продвижение через звезд' },
        { id: 'global-outreach', name: 'Глобальный охват', description: 'Международное продвижение' },
      ],
      limits: { socialAccounts: -1, botActivations: -1, emailCampaigns: -1, telegramMembers: -1 }
    }
  ];

  const handleSelectPlan = (tier: PricingPlan) => {
    if (!user) {
      // Перенаправляем на страницу входа
      window.location.href = '/auth/login?redirect=/pricing';
      return;
    }
    
    setSelectedTier(tier);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = async (paymentId: string) => {
    try {
      console.log('Payment success:', paymentId);
      alert('Платеж успешно выполнен! ID: ' + paymentId);
      setShowPaymentModal(false);
    } catch (error) {
      console.error('Error handling payment:', error);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getFeatureIcon = (featureId: string) => {
    const icons: Record<string, React.ReactNode> = {
      'social-accounts': <Users className="w-4 h-4" />,
      'basic-bots': <Zap className="w-4 h-4" />,
      'advanced-bots': <Rocket className="w-4 h-4" />,
      'email-campaigns': <BarChart3 className="w-4 h-4" />,
      'analytics': <BarChart3 className="w-4 h-4" />,
      'support': <Shield className="w-4 h-4" />,
      'telegram-growth': <Users className="w-4 h-4" />,
      'content-creation': <Music className="w-4 h-4" />,
      'default': <Check className="w-4 h-4" />
    };
    return icons[featureId] || icons.default;
  };

  const currentPlans = activeTab === 'artist' ? artistPlans : trackPlans;

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Выберите свой <span className="text-red-500">тариф</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            Профессиональное продвижение музыки с гарантированным результатом. 
            Выберите подходящий тариф и начните свой путь к успеху уже сегодня.
          </p>

          {/* Tab Switcher */}
          <div className="flex justify-center mb-12">
            <div className="bg-gray-800/50 rounded-2xl p-2 backdrop-blur-xl border border-gray-700/50">
              <button
                onClick={() => setActiveTab('artist')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'artist'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Users className="w-5 h-5 inline-block mr-2" />
                Продвижение артистов
              </button>
              <button
                onClick={() => setActiveTab('track')}
                className={`px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === 'track'
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Music className="w-5 h-5 inline-block mr-2" />
                Продвижение треков
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {currentPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-gray-900/50 backdrop-blur-xl rounded-3xl border transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                plan.popular
                  ? 'border-red-500/50 shadow-lg shadow-red-500/25'
                  : 'border-gray-700/50 hover:border-gray-600/50'
              }`}
              style={{ padding: '32px' }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-red-500 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                    <Star className="w-4 h-4" />
                    Популярный
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="text-4xl font-bold text-white mb-2">
                  {formatPrice(plan.price, plan.currency)}
                </div>
                <p className="text-gray-400">за {plan.duration} дней</p>
              </div>

              {/* Features */}
              <div className="mb-8">
                <h4 className="text-white font-semibold mb-4">Что включено:</h4>
                <ul className="space-y-3">
                  {plan.features.slice(0, 6).map((feature) => (
                    <li key={feature.id} className="flex items-start gap-3">
                      <div className="text-red-400 mt-0.5">
                        {getFeatureIcon(feature.id)}
                      </div>
                      <div>
                        <span className="text-white font-medium">{feature.name}</span>
                        {feature.limit && feature.limit > 0 && (
                          <span className="text-gray-400 text-sm ml-2">({feature.limit})</span>
                        )}
                        <p className="text-gray-400 text-sm">{feature.description}</p>
                      </div>
                    </li>
                  ))}
                  {plan.features.length > 6 && (
                    <li className="text-gray-400 text-sm">
                      +{plan.features.length - 6} дополнительных функций
                    </li>
                  )}
                </ul>
              </div>

              {/* Limits Info */}
              <div className="mb-8 p-4 bg-gray-800/30 rounded-xl">
                <h5 className="text-white font-medium mb-2">Лимиты тарифа:</h5>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-gray-400">
                    Соц. аккаунты: <span className="text-white">{plan.limits.socialAccounts === -1 ? '∞' : plan.limits.socialAccounts}</span>
                  </div>
                  <div className="text-gray-400">
                    Боты: <span className="text-white">{plan.limits.botActivations === -1 ? '∞' : plan.limits.botActivations}</span>
                  </div>
                  <div className="text-gray-400">
                    Email кампании: <span className="text-white">{plan.limits.emailCampaigns === -1 ? '∞' : plan.limits.emailCampaigns}</span>
                  </div>
                  <div className="text-gray-400">
                    Telegram: <span className="text-white">{plan.limits.telegramMembers === -1 ? '∞' : plan.limits.telegramMembers.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <button
                onClick={() => handleSelectPlan(plan)}
                className={`w-full py-4 rounded-xl font-semibold transition-all duration-300 ${
                  plan.popular
                    ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg shadow-red-500/25'
                    : 'bg-gray-700 hover:bg-gray-600 text-white'
                }`}
              >
                {user ? 'Выбрать тариф' : 'Войти и выбрать'}
              </button>

              {/* Payment Methods */}
              <div className="mt-4 flex justify-center items-center gap-2 text-gray-400 text-xs">
                <CreditCard className="w-4 h-4" />
                <Bitcoin className="w-4 h-4" />
                <Smartphone className="w-4 h-4" />
                <span>Карта • Крипто • СБП</span>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-16">
          <p className="text-gray-400 mb-4">
            Все тарифы включают бесплатную консультацию и гарантию результата
          </p>
          <div className="flex justify-center items-center gap-8 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Безопасные платежи
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4" />
              Гарантия результата
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              150+ успешных кампаний
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal - Временно отключен */}
      {showPaymentModal && selectedTier && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center" style={{ padding: '20px' }}>
          <div className="bg-gray-900 rounded-2xl border border-gray-800 max-w-md w-full" style={{ padding: '30px' }}>
            <h3 className="text-xl font-bold text-white" style={{ marginBottom: '20px', lineHeight: '1.4' }}>
              Оплата тарифа
            </h3>
            <p className="text-gray-400" style={{ marginBottom: '20px', lineHeight: '1.6' }}>
              Тариф: {selectedTier.name}<br />
              Стоимость: {formatPrice(selectedTier.price, selectedTier.currency)}
            </p>
            <p className="text-yellow-400 text-sm" style={{ marginBottom: '20px', lineHeight: '1.6' }}>
              Система платежей временно недоступна. Свяжитесь с нами для оформления заказа.
            </p>
            <button
              onClick={() => setShowPaymentModal(false)}
              className="w-full bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              style={{ padding: '12px', lineHeight: '1.4' }}
            >
              Закрыть
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default PricingSectionNew;
