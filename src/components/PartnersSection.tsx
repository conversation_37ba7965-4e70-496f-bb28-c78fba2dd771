'use client';

import { Music, Radio, Building, Tv } from 'lucide-react';

const PartnersSection = () => {
  const partners = [
    {
      id: 1,
      name: 'Universal Music',
      type: 'label',
      description: 'Мировой лидер музыкальной индустрии',
    },
    {
      id: 2,
      name: 'Radio Record',
      type: 'radio',
      description: 'Крупнейшая радиосеть России',
    },
    {
      id: 3,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'label',
      description: 'Ведущий российский лейбл',
    },
    {
      id: 4,
      name: 'Europa Plus',
      type: 'radio',
      description: 'Популярная радиостанция',
    },
    {
      id: 5,
      name: 'Первый канал',
      type: 'tv',
      description: 'Федеральный телеканал',
    },
    {
      id: 6,
      name: 'Black Star',
      type: 'label',
      description: 'Известный музыкальный лейбл',
    },
    {
      id: 7,
      name: 'Русское Радио',
      type: 'radio',
      description: 'Популярная радиостанция',
    },
    {
      id: 8,
      name: 'НТВ',
      type: 'tv',
      description: 'Федеральный телеканал',
    },
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case 'label':
        return <Music className="text-red-500 text-2xl" />;
      case 'radio':
        return <Radio className="text-red-500 text-2xl" />;
      case 'tv':
        return <Tv className="text-red-500 text-2xl" />;
      default:
        return <Building className="text-red-500 text-2xl" />;
    }
  };

  return (
    <section id="partners" className="py-16 md:py-32 bg-gray-900">
      <div className="container-wide">
        <div className="text-center mb-12 md:mb-24 max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-6 md:mb-10 text-center leading-tight">
            Партнеры
          </h2>
          <div className="w-32 md:w-40 h-1 md:h-1.5 bg-gradient-to-r from-red-500 to-red-600 mx-auto mb-8 md:mb-16 rounded-full"></div>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-200 max-w-5xl mx-auto leading-relaxed font-light">
            Мы работаем с ведущими лейблами, радиостанциями и медиа-компаниями для максимального охвата вашей аудитории
          </p>
        </div>

        {/* Partners Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-30 mb-12 md:mb-24 max-w-7xl mx-auto">
          {partners.map((partner) => (
            <div
              key={partner.id}
              className="bg-gray-800 rounded-2xl p-6 md:p-8 group hover:bg-gray-700 transition-all duration-300 hover:scale-105"
            >
              <div className="flex flex-col items-center text-center">
                <div className="mb-4 p-3 md:p-4 bg-gray-700 rounded-full group-hover:bg-gray-600 transition-all duration-300">
                  <div className="text-gray-400 group-hover:text-red-400 transition-colors duration-300">
                    {getIcon(partner.type)}
                  </div>
                </div>
                <h3 className="text-white font-semibold mb-2 text-sm md:text-base group-hover:text-red-50 transition-colors duration-300">
                  {partner.name}
                </h3>
                <p className="text-gray-400 text-xs md:text-sm group-hover:text-gray-300 transition-colors duration-300">
                  {partner.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-30 text-center max-w-7xl mx-auto">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 group-hover:text-red-400 transition-colors duration-300 text-center bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
              100+
            </div>
            <div className="text-gray-300 text-base md:text-lg group-hover:text-gray-200 transition-colors duration-300 text-center font-medium">
              Радиостанций
            </div>
          </div>
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 group-hover:text-red-400 transition-colors duration-300 text-center bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
              50+
            </div>
            <div className="text-gray-300 text-base md:text-lg group-hover:text-gray-200 transition-colors duration-300 text-center font-medium">
              Лейблов
            </div>
          </div>
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 group-hover:text-red-400 transition-colors duration-300 text-center bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
              20+
            </div>
            <div className="text-gray-300 text-base md:text-lg group-hover:text-gray-200 transition-colors duration-300 text-center font-medium">
              ТВ каналов
            </div>
          </div>
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 md:p-10 group hover:bg-gray-700 transition-all duration-300 hover:scale-105 shadow-xl">
            <div className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 group-hover:text-red-400 transition-colors duration-300 text-center bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
              1M+
            </div>
            <div className="text-gray-300 text-base md:text-lg group-hover:text-gray-200 transition-colors duration-300 text-center font-medium">
              Охват
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-6">
            Хотите стать нашим партнером?
          </h3>
          <button className="bg-red-500 hover:bg-red-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-colors duration-300">
            Связаться с нами
          </button>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
