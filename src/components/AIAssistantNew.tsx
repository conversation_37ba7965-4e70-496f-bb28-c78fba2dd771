'use client';

import { useState } from 'react';
import {
  MessageCircle,
  X
} from 'lucide-react';

interface AIAssistantProps {
  language?: 'ru' | 'en';
}

const AIAssistant: React.FC<AIAssistantProps> = ({ language = 'ru' }) => {
  const [isOpen, setIsOpen] = useState(false);

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="bg-red-500 hover:bg-red-600 text-white p-4 rounded-full shadow-lg transition-all duration-300"
        >
          <MessageCircle className="w-6 h-6" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50 w-96">
      <div className="bg-gray-900 border border-gray-800 rounded-2xl shadow-2xl overflow-hidden">
        <div className="bg-red-500 p-4 flex items-center justify-between">
          <h3 className="text-white font-semibold">HIVE AI Assistant</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-white hover:text-gray-200"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <div className="p-4">
          <p className="text-gray-300">
            {language === 'ru' ? 'Привет! Я AI-помощник HIVE Agency.' : 'Hi! I\'m HIVE Agency AI assistant.'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
