import { useAuth } from '@/contexts/AuthContext';
import { useMemo } from 'react';

export interface AdminPermissions {
  canAccessAdminPanel: boolean;
  canManageOrders: boolean;
  canManageUsers: boolean;
  canViewAnalytics: boolean;
  canManageSettings: boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
}

export function useAdminAuth(): AdminPermissions {
  const { user } = useAuth();

  const permissions = useMemo(() => {
    if (!user) {
      return {
        canAccessAdminPanel: false,
        canManageOrders: false,
        canManageUsers: false,
        canViewAnalytics: false,
        canManageSettings: false,
        isAdmin: false,
        isSuperAdmin: false,
      };
    }

    // Проверяем роль администратора
    const userPrefs = user.prefs as Record<string, unknown> || {};
    const isAdmin = user.type === 'admin' || 
                   userPrefs.type === 'admin' ||
                   userPrefs.role === 'administrator';

    const isSuperAdmin = userPrefs.role === 'super_administrator';

    // Проверяем конкретные разрешения
    const userPermissions = (userPrefs.permissions as string[]) || [];
    
    return {
      canAccessAdminPanel: isAdmin || userPermissions.includes('admin_panel'),
      canManageOrders: isAdmin || userPermissions.includes('manage_orders'),
      canManageUsers: isSuperAdmin || userPermissions.includes('manage_users'),
      canViewAnalytics: isAdmin || userPermissions.includes('view_analytics'),
      canManageSettings: isSuperAdmin || userPermissions.includes('manage_settings'),
      isAdmin,
      isSuperAdmin,
    };
  }, [user]);

  return permissions;
}

// Хук для проверки конкретного разрешения
export function useHasPermission(permission: keyof AdminPermissions): boolean {
  const permissions = useAdminAuth();
  return permissions[permission];
}

// Хук для проверки множественных разрешений
export function useHasAnyPermission(permissionList: (keyof AdminPermissions)[]): boolean {
  const permissions = useAdminAuth();
  return permissionList.some(permission => permissions[permission]);
}

// Хук для проверки всех разрешений
export function useHasAllPermissions(permissionList: (keyof AdminPermissions)[]): boolean {
  const permissions = useAdminAuth();
  return permissionList.every(permission => permissions[permission]);
}
