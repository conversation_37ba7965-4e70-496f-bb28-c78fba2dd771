'use client';

import { useState, useEffect, useCallback } from 'react';
import { NotificationData, NotificationType, NOTIFICATION_TEMPLATES } from '@/types/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { AppwriteNotificationService } from '@/services/appwriteServiceStub';

interface CreateNotificationParams {
  type: NotificationType;
  variables?: Record<string, any>;
  customTitle?: string;
  customMessage?: string;
  customActionUrl?: string;
  customActionText?: string;
  userId?: string;
  relatedOrderId?: string;
  relatedUserId?: string;
  metadata?: Record<string, any>;
}

export const useNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Загрузка уведомлений при инициализации
  useEffect(() => {
    if (user) {
      loadNotifications();
    }
  }, [user]);

  // Загрузка уведомлений из Appwrite
  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const appwriteNotifications = await AppwriteNotificationService.getUserNotifications(user.$id);

      // Конвертируем формат Appwrite в наш формат
      const convertedNotifications: NotificationData[] = appwriteNotifications.map(notification => ({
        id: notification.$id,
        type: notification.type as NotificationType,
        category: notification.category,
        title: notification.title,
        message: notification.message,
        actionUrl: notification.actionUrl,
        actionText: notification.actionText,
        isRead: notification.isRead,
        isImportant: notification.isImportant,
        priority: notification.priority || 'normal',
        createdAt: notification.createdAt,
        relatedOrderId: notification.relatedOrderId,
        relatedUserId: notification.relatedUserId,
        metadata: notification.metadata || {},
        icon: getNotificationIcon(notification.type as NotificationType),
      }));

      setNotifications(convertedNotifications);

      // Также сохраняем в localStorage как резерв
      localStorage.setItem('hive_notifications', JSON.stringify(convertedNotifications));
    } catch (error) {
      console.error('Error loading notifications from Appwrite:', error);

      // Fallback к localStorage если Appwrite недоступен
      const savedNotifications = localStorage.getItem('hive_notifications');
      if (savedNotifications) {
        setNotifications(JSON.parse(savedNotifications));
      } else {
        // Создаем демо уведомления для первого запуска
        const demoNotifications = createDemoNotifications();
        setNotifications(demoNotifications);
        localStorage.setItem('hive_notifications', JSON.stringify(demoNotifications));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Создание демо уведомлений
  const createDemoNotifications = (): NotificationData[] => {
    return [
      createNotification({
        type: 'order_completed',
        variables: {
          orderTitle: 'Продвижение артиста John Doe',
          orderId: 'order_123'
        }
      }),
      createNotification({
        type: 'payment_received',
        variables: {
          amount: '35,000',
          orderId: 'order_123'
        }
      }),
      createNotification({
        type: 'order_requires_info',
        variables: {
          orderTitle: 'Продвижение трека "Summer Vibes"',
          orderId: 'order_124'
        }
      }),
      createNotification({
        type: 'promo_discount',
        variables: {
          discount: '20',
          services: 'все пакеты продвижения',
          endDate: 'конца месяца',
          promoCode: 'SAVE20'
        }
      }),
      createNotification({
        type: 'system_update',
        variables: {
          features: 'отслеживание прогресса в реальном времени и новая аналитика'
        }
      })
    ];
  };

  // Создание уведомления
  const createNotification = (params: CreateNotificationParams): NotificationData => {
    const template = NOTIFICATION_TEMPLATES[params.type];
    const id = generateId();
    const now = new Date().toISOString();

    // Замена переменных в шаблонах
    const title = params.customTitle || replaceVariables(template.titleTemplate, params.variables || {});
    const message = params.customMessage || replaceVariables(template.messageTemplate, params.variables || {});
    const actionUrl = params.customActionUrl || (template.actionUrlTemplate ? replaceVariables(template.actionUrlTemplate, params.variables || {}) : undefined);
    const actionText = params.customActionText || template.actionText;

    // Вычисление времени истечения
    const expiresAt = template.expiresInHours 
      ? new Date(Date.now() + template.expiresInHours * 60 * 60 * 1000).toISOString()
      : undefined;

    return {
      id,
      type: params.type,
      category: template.category,
      title,
      message,
      actionUrl,
      actionText,
      isRead: false,
      isImportant: template.isImportant,
      priority: template.priority,
      relatedOrderId: params.relatedOrderId,
      relatedUserId: params.relatedUserId,
      metadata: params.metadata,
      createdAt: now,
      expiresAt,
      icon: getNotificationIcon(params.type)
    };
  };

  // Добавление нового уведомления
  const addNotification = useCallback(async (params: CreateNotificationParams) => {
    if (!user) return;

    try {
      const notification = createNotification(params);

      // Создаем уведомление в Appwrite
      await AppwriteNotificationService.createNotification({
        userId: user.$id,
        type: notification.type,
        category: notification.category,
        title: notification.title,
        message: notification.message,
        actionUrl: notification.actionUrl,
        actionText: notification.actionText,
        relatedOrderId: notification.relatedOrderId,
        relatedUserId: notification.relatedUserId,
        priority: notification.isImportant ? 'high' : 'normal',
        isImportant: notification.isImportant,
        metadata: notification.metadata,
      });

      // Обновляем локальное состояние
      setNotifications(prev => {
        const updated = [notification, ...prev];
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });

      // Показать браузерное уведомление если разрешено
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification.id
        });
      }

      return notification.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      // Fallback к локальному хранению
      const notification = createNotification(params);
      setNotifications(prev => {
        const updated = [notification, ...prev];
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
      return notification.id;
    }
  }, [user]);

  // Отметить как прочитанное
  const markAsRead = useCallback(async (id: string) => {
    try {
      await AppwriteNotificationService.markAsRead(id);

      setNotifications(prev => {
        const updated = prev.map(n =>
          n.id === id ? { ...n, isRead: true } : n
        );
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Fallback к локальному обновлению
      setNotifications(prev => {
        const updated = prev.map(n =>
          n.id === id ? { ...n, isRead: true } : n
        );
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    }
  }, []);

  // Отметить как непрочитанное
  const markAsUnread = useCallback(async (id: string) => {
    try {
      await AppwriteNotificationService.markAsUnread(id);

      setNotifications(prev => {
        const updated = prev.map(n =>
          n.id === id ? { ...n, isRead: false } : n
        );
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      // Fallback к локальному обновлению
      setNotifications(prev => {
        const updated = prev.map(n =>
          n.id === id ? { ...n, isRead: false } : n
        );
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    }
  }, []);

  // Отметить все как прочитанные
  const markAllAsRead = useCallback(async () => {
    if (!user) return;

    try {
      await AppwriteNotificationService.markAllAsRead(user.$id);

      setNotifications(prev => {
        const updated = prev.map(n => ({ ...n, isRead: true }));
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      // Fallback к локальному обновлению
      setNotifications(prev => {
        const updated = prev.map(n => ({ ...n, isRead: true }));
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    }
  }, [user]);

  // Удалить уведомление
  const deleteNotification = useCallback(async (id: string) => {
    try {
      await AppwriteNotificationService.deleteNotification(id);

      setNotifications(prev => {
        const updated = prev.filter(n => n.id !== id);
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      // Fallback к локальному удалению
      setNotifications(prev => {
        const updated = prev.filter(n => n.id !== id);
        localStorage.setItem('hive_notifications', JSON.stringify(updated));
        return updated;
      });
    }
  }, []);

  // Удалить все уведомления
  const clearAll = useCallback(async () => {
    if (!user) return;

    try {
      await AppwriteNotificationService.clearAllNotifications(user.$id);

      setNotifications([]);
      localStorage.removeItem('hive_notifications');
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      // Fallback к локальному очищению
      setNotifications([]);
      localStorage.removeItem('hive_notifications');
    }
  }, [user]);

  // Удалить истекшие уведомления
  const removeExpired = useCallback(() => {
    const now = new Date().toISOString();
    setNotifications(prev => {
      const updated = prev.filter(n => !n.expiresAt || n.expiresAt > now);
      localStorage.setItem('hive_notifications', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Получить количество непрочитанных
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const importantUnreadCount = notifications.filter(n => !n.isRead && n.isImportant).length;

  // Получить уведомления по фильтру
  const getFilteredNotifications = (filter: 'all' | 'unread' | 'important' | 'read') => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.isRead);
      case 'important':
        return notifications.filter(n => n.isImportant);
      case 'read':
        return notifications.filter(n => n.isRead);
      default:
        return notifications;
    }
  };

  // Запросить разрешение на браузерные уведомления
  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  };

  // Автоматическое удаление истекших уведомлений
  useEffect(() => {
    const interval = setInterval(removeExpired, 60000); // Каждую минуту
    return () => clearInterval(interval);
  }, [removeExpired]);

  return {
    notifications,
    isLoading,
    unreadCount,
    importantUnreadCount,
    addNotification,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    clearAll,
    removeExpired,
    getFilteredNotifications,
    requestNotificationPermission,
    loadNotifications
  };
};

// Вспомогательные функции
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

const replaceVariables = (template: string, variables: Record<string, any>): string => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key] || match;
  });
};

const getNotificationIcon = (type: NotificationType): React.ReactNode => {
  const template = NOTIFICATION_TEMPLATES[type];
  return template.icon || '📢';
};

// Предустановленные функции для быстрого создания уведомлений
export const notificationHelpers = {
  // Заказы
  orderCreated: (orderTitle: string, orderId: string) => ({
    type: 'order_created' as NotificationType,
    variables: { orderTitle, orderId }
  }),
  
  orderCompleted: (orderTitle: string, orderId: string) => ({
    type: 'order_completed' as NotificationType,
    variables: { orderTitle, orderId }
  }),
  
  orderRequiresInfo: (orderTitle: string, orderId: string) => ({
    type: 'order_requires_info' as NotificationType,
    variables: { orderTitle, orderId }
  }),
  
  // Платежи
  paymentReceived: (amount: string, orderId?: string) => ({
    type: 'payment_received' as NotificationType,
    variables: { amount, orderId }
  }),
  
  paymentFailed: (amount: string, paymentId: string) => ({
    type: 'payment_failed' as NotificationType,
    variables: { amount, paymentId }
  }),
  
  // Промо
  discount: (discount: string, services: string, endDate: string, promoCode: string) => ({
    type: 'promo_discount' as NotificationType,
    variables: { discount, services, endDate, promoCode }
  }),
  
  // Система
  welcome: () => ({
    type: 'welcome' as NotificationType,
    variables: {}
  }),
  
  profileIncomplete: () => ({
    type: 'profile_incomplete' as NotificationType,
    variables: {}
  })
};
