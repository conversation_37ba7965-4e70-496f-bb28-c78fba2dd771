import { useState, useEffect } from 'react';
import { client, databases } from '@/lib/appwrite';
import { orderService, Order } from '@/services/orderService';

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID!;
const ORDERS_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_ORDERS_COLLECTION_ID!;

export const useRealtimeOrders = (filters?: {
  status?: string;
  type?: string;
  userId?: string;
}) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Загрузка начальных данных
  useEffect(() => {
    const loadOrders = async () => {
      try {
        setLoading(true);
        const ordersData = await orderService.getAllOrders();
        setOrders(ordersData);
        setError(null);
      } catch (err) {
        console.error('Error loading orders:', err);
        setError('Ошибка загрузки заказов');
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  }, [filters?.status, filters?.type, filters?.userId]);

  // Настройка реал-тайм подписки
  useEffect(() => {
    const unsubscribe = client.subscribe(
      `databases.${DATABASE_ID}.collections.${ORDERS_COLLECTION_ID}.documents`,
      (response) => {
        const { events, payload } = response;
        
        if (events.includes('databases.*.collections.*.documents.*.create')) {
          // Новый заказ создан
          const newOrder = transformOrder(payload);
          if (matchesFilters(newOrder, filters)) {
            setOrders(prev => [newOrder, ...prev]);
          }
        } else if (events.includes('databases.*.collections.*.documents.*.update')) {
          // Заказ обновлен
          const updatedOrder = transformOrder(payload);
          if (matchesFilters(updatedOrder, filters)) {
            setOrders(prev => 
              prev.map(order => 
                order.id === updatedOrder.id ? updatedOrder : order
              )
            );
          } else {
            // Заказ больше не соответствует фильтрам, удаляем его
            setOrders(prev => prev.filter(order => order.id !== updatedOrder.id));
          }
        } else if (events.includes('databases.*.collections.*.documents.*.delete')) {
          // Заказ удален
          setOrders(prev => prev.filter(order => order.id !== payload.$id));
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, [filters?.status, filters?.type, filters?.userId]);

  // Функция обновления статуса заказа
  const updateOrderStatus = async (orderId: string, status: string, progress?: number) => {
    try {
      await orderService.updateOrderStatus(orderId, status, progress);
      // Обновление произойдет автоматически через реал-тайм подписку
    } catch (err) {
      console.error('Error updating order status:', err);
      throw err;
    }
  };

  // Функция создания заказа
  const createOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newOrder = await orderService.createOrder(orderData);
      // Новый заказ появится автоматически через реал-тайм подписку
      return newOrder;
    } catch (err) {
      console.error('Error creating order:', err);
      throw err;
    }
  };

  return {
    orders,
    loading,
    error,
    updateOrderStatus,
    createOrder,
    refetch: () => {
      setLoading(true);
      orderService.getOrders(filters).then(setOrders).finally(() => setLoading(false));
    }
  };
};

// Вспомогательные функции
function transformOrder(doc: any): Order {
  return {
    id: doc.$id,
    userId: doc.userId,
    type: doc.type,
    services: doc.services || [],
    totalAmount: doc.totalAmount,
    status: doc.status,
    paymentMethod: doc.paymentMethod,
    paymentId: doc.paymentId,
    aiConversationId: doc.aiConversationId,
    projectData: doc.projectData || {},
    deliverables: doc.deliverables || [],
    statistics: doc.statistics,
    createdAt: new Date(doc.createdAt),
    updatedAt: new Date(doc.updatedAt),
    completedAt: doc.completedAt ? new Date(doc.completedAt) : undefined
  };
}

function matchesFilters(order: Order, filters?: {
  status?: string;
  type?: string;
  userId?: string;
}): boolean {
  if (!filters) return true;
  
  if (filters.status && filters.status !== 'all' && order.status !== filters.status) {
    return false;
  }
  
  if (filters.type && order.type !== filters.type) {
    return false;
  }
  
  if (filters.userId && order.userId !== filters.userId) {
    return false;
  }
  
  return true;
}
