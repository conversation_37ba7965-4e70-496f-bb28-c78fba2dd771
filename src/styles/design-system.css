/* HIVE AGENCY DESIGN SYSTEM */

/* ===== COLOR PALETTE ===== */
:root {
  /* Primary Colors */
  --primary-50: #faf5ff;
  --primary-100: #f3e8ff;
  --primary-200: #e9d5ff;
  --primary-300: #d8b4fe;
  --primary-400: #c084fc;
  --primary-500: #a855f7;
  --primary-600: #9333ea;
  --primary-700: #7c3aed;
  --primary-800: #6b21a8;
  --primary-900: #581c87;

  /* Secondary Colors */
  --secondary-50: #fdf2f8;
  --secondary-100: #fce7f3;
  --secondary-200: #fbcfe8;
  --secondary-300: #f9a8d4;
  --secondary-400: #f472b6;
  --secondary-500: #ec4899;
  --secondary-600: #db2777;
  --secondary-700: #be185d;
  --secondary-800: #9d174d;
  --secondary-900: #831843;

  /* Accent Colors */
  --accent-blue: #3b82f6;
  --accent-cyan: #06b6d4;
  --accent-green: #10b981;
  --accent-yellow: #f59e0b;
  --accent-orange: #f97316;
  --accent-red: #ef4444;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;

  /* Background Colors */
  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #171717;
  --bg-glass: rgba(0, 0, 0, 0.2);
  --bg-glass-light: rgba(255, 255, 255, 0.05);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #d4d4d4;
  --text-tertiary: #a3a3a3;
  --text-muted: #737373;

  /* Border Colors */
  --border-primary: rgba(168, 85, 247, 0.2);
  --border-secondary: rgba(168, 85, 247, 0.1);
  --border-accent: rgba(168, 85, 247, 0.4);

  /* Shadow Colors */
  --shadow-primary: rgba(168, 85, 247, 0.25);
  --shadow-secondary: rgba(168, 85, 247, 0.15);
  --shadow-glow: rgba(168, 85, 247, 0.5);
}

/* ===== TYPOGRAPHY ===== */
.text-display-1 {
  font-size: 4rem; /* 64px */
  line-height: 1.1;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.text-display-2 {
  font-size: 3rem; /* 48px */
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.01em;
}

.text-heading-1 {
  font-size: 2.5rem; /* 40px */
  line-height: 1.2;
  font-weight: 700;
}

.text-heading-2 {
  font-size: 2rem; /* 32px */
  line-height: 1.3;
  font-weight: 600;
}

.text-heading-3 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.4;
  font-weight: 600;
}

.text-body-large {
  font-size: 1.25rem; /* 20px */
  line-height: 1.6;
  font-weight: 400;
}

.text-body {
  font-size: 1rem; /* 16px */
  line-height: 1.6;
  font-weight: 400;
}

.text-body-small {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
  font-weight: 400;
}

.text-caption {
  font-size: 0.75rem; /* 12px */
  line-height: 1.4;
  font-weight: 500;
}

/* ===== SPACING SYSTEM ===== */
.space-xs { margin: 0.5rem; }
.space-sm { margin: 1rem; }
.space-md { margin: 1.5rem; }
.space-lg { margin: 2rem; }
.space-xl { margin: 3rem; }
.space-2xl { margin: 4rem; }
.space-3xl { margin: 6rem; }

.gap-xs { gap: 0.5rem; }
.gap-sm { gap: 1rem; }
.gap-md { gap: 1.5rem; }
.gap-lg { gap: 2rem; }
.gap-xl { gap: 3rem; }

.padding-xs { padding: 0.5rem; }
.padding-sm { padding: 1rem; }
.padding-md { padding: 1.5rem; }
.padding-lg { padding: 2rem; }
.padding-xl { padding: 3rem; }

/* ===== COMPONENT STYLES ===== */

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  color: var(--text-primary);
  padding: 16px 32px; /* Увеличенные паддинги */
  border-radius: 1rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px var(--shadow-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 48px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px var(--shadow-primary);
}

.btn-secondary {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  color: var(--text-primary);
  padding: 16px 32px; /* Увеличенные паддинги */
  border-radius: 1rem;
  font-weight: 600;
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 48px;
}

.btn-secondary:hover {
  border-color: var(--border-accent);
  background: var(--bg-glass-light);
}

/* Button sizes */
.btn-sm {
  padding: 12px 24px;
  font-size: 0.875rem;
  min-height: 40px;
}

.btn-lg {
  padding: 20px 40px;
  font-size: 1.125rem;
  min-height: 56px;
}

/* Cards */
.card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
  border-radius: 1.5rem;
  padding: 2rem; /* 32px паддинг внутри карточек */
  transition: all 0.3s ease;
  text-align: center; /* Центрирование контента */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.card:hover {
  border-color: var(--border-accent);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(139, 92, 246, 0.2);
}

/* Card spacing */
@media (max-width: 768px) {
  .card {
    padding: 1.5rem; /* Меньше паддинг на мобильных */
  }
}

.card-premium {
  background: linear-gradient(135deg, var(--bg-glass), var(--bg-glass-light));
  border: 2px solid var(--primary-500);
  box-shadow: 0 0 30px var(--shadow-glow);
}

/* Glassmorphism */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-secondary);
}

.glass-strong {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(30px);
  border: 1px solid var(--border-primary);
}

/* Gradients */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px var(--shadow-primary); }
  50% { box-shadow: 0 0 40px var(--shadow-glow); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 8s linear infinite;
}

/* Layout */
.container-hive {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 10px; /* 10px отступы от краев экрана */
}

/* Utility classes for consistent spacing */
.spacing-section {
  padding: 4rem 0; /* 64px vertical */
}

.spacing-cards {
  gap: 2rem; /* 32px between cards */
}

@media (max-width: 768px) {
  .spacing-section {
    padding: 3rem 0; /* 48px on mobile */
  }

  .spacing-cards {
    gap: 1.5rem; /* 24px on mobile */
  }
}

/* Input fields */
.input-field {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
  border-radius: 1rem;
  padding: 16px 20px; /* Увеличенные паддинги */
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
  min-height: 48px;
  display: flex;
  align-items: center;
  text-align: center;
}

.input-field:focus {
  outline: none;
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.input-field::placeholder {
  color: var(--text-secondary);
  text-align: center;
}

.section-spacing {
  padding: 4rem 0; /* Уменьшили вертикальные отступы */
}

.section-spacing-sm {
  padding: 2rem 0;
}

/* Grid spacing */
.grid-spacing {
  gap: 2rem; /* 32px между карточками */
}

.grid-spacing-sm {
  gap: 1.5rem; /* 24px между карточками на мобильных */
}



/* Content centering */
.content-center {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Responsive */
@media (max-width: 768px) {
  .container-hive {
    padding: 0 10px; /* Сохраняем 10px на мобильных */
  }

  .section-spacing {
    padding: 3rem 0;
  }

  .text-display-1 {
    font-size: 2.5rem;
  }

  .text-display-2 {
    font-size: 2rem;
  }

  .grid-spacing {
    gap: 1.5rem;
  }
}
