#!/usr/bin/env node

const { Client, Databases, ID, Permission, Role } = require('node-appwrite');

// Конфигурация Appwrite
const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('6872a2e20006222a3ad1')
  .setKey('standard_715f67138926017c37d228050c7493e0487a8038c72c2bd33c018b446fd71a6eeccd97cd974ec7042b31a137591d8a35261c23914cc0d0b9dd2a11f927378dfb0919c6cf8c479558ea554bce6989529e4b39eb29dac1dd0baf3d2ed4c8ce136e50bc846dc59668b3ffa1fe9f19d60e046fb2313b1bc4f89c6d7d9c6aa8af9728');

const databases = new Databases(client);
const DATABASE_ID = '6872a355001ea979ad3c';

// Новые коллекции для AI-помощника
const AI_COLLECTIONS = [
  {
    id: 'ai_prompts',
    name: 'AI Prompts',
    description: 'Готовые промпты для AI-помощника'
  },
  {
    id: 'ai_recommendations',
    name: 'AI Recommendations',
    description: 'Рекомендации AI для пользователей'
  },
  {
    id: 'service_templates',
    name: 'Service Templates',
    description: 'Шаблоны сервисов для рекомендаций'
  },
  {
    id: 'campaign_proposals',
    name: 'Campaign Proposals',
    description: 'Предложения кампаний от AI'
  },
  {
    id: 'ai_conversations',
    name: 'AI Conversations',
    description: 'История разговоров с AI'
  }
];

// Атрибуты для коллекций
const COLLECTION_ATTRIBUTES = {
  // AI Prompts - готовые промпты
  'ai_prompts': [
    { key: 'prompt_id', type: 'string', size: 255, required: true },
    { key: 'category', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'title', type: 'string', size: 255, required: true },
    { key: 'description', type: 'string', size: 500, required: true },
    { key: 'prompt_text', type: 'string', size: 1000, required: true },
    { key: 'tags', type: 'json' },
    { key: 'popularity', type: 'integer', default: 0 },
    { key: 'active', type: 'boolean', default: true },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // Service Templates - шаблоны сервисов
  'service_templates': [
    { key: 'template_id', type: 'string', size: 255, required: true },
    { key: 'category', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'service_type', type: 'enum', elements: ['social_media', 'streaming', 'email', 'pr', 'advertising', 'content'], required: true },
    { key: 'platform', type: 'string', size: 100, required: true },
    { key: 'name', type: 'string', size: 255, required: true },
    { key: 'description', type: 'string', size: 1000, required: true },
    { key: 'icon', type: 'string', size: 100 },
    { key: 'color', type: 'string', size: 20 },
    { key: 'base_price', type: 'integer', required: true },
    { key: 'duration_days', type: 'integer', required: true },
    { key: 'features', type: 'json', required: true },
    { key: 'metrics', type: 'json' },
    { key: 'requirements', type: 'json' },
    { key: 'active', type: 'boolean', default: true },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // Campaign Proposals - предложения кампаний
  'campaign_proposals': [
    { key: 'proposal_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'session_id', type: 'string', size: 255, required: true },
    { key: 'category', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'user_query', type: 'string', size: 2000, required: true },
    { key: 'ai_analysis', type: 'json', required: true },
    { key: 'recommended_services', type: 'json', required: true },
    { key: 'total_price', type: 'integer', required: true },
    { key: 'total_duration', type: 'integer', required: true },
    { key: 'expected_results', type: 'json' },
    { key: 'status', type: 'enum', elements: ['draft', 'presented', 'accepted', 'rejected', 'ordered'], default: 'draft' },
    { key: 'expires_at', type: 'datetime' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // AI Conversations - история разговоров
  'ai_conversations': [
    { key: 'conversation_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255 },
    { key: 'session_id', type: 'string', size: 255, required: true },
    { key: 'category', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'messages', type: 'json', required: true },
    { key: 'context', type: 'json' },
    { key: 'last_message_at', type: 'datetime', required: true },
    { key: 'created_at', type: 'datetime', required: true }
  ],

  // AI Recommendations - рекомендации
  'ai_recommendations': [
    { key: 'recommendation_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255 },
    { key: 'category', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'recommendation_type', type: 'enum', elements: ['service', 'strategy', 'content', 'timing'], required: true },
    { key: 'title', type: 'string', size: 255, required: true },
    { key: 'description', type: 'string', size: 1000, required: true },
    { key: 'confidence_score', type: 'float', min: 0, max: 1 },
    { key: 'data', type: 'json', required: true },
    { key: 'shown', type: 'boolean', default: false },
    { key: 'clicked', type: 'boolean', default: false },
    { key: 'created_at', type: 'datetime', required: true }
  ]
};

// Функция для создания коллекции
async function createCollection(collectionData) {
  try {
    console.log(`📦 Создание коллекции: ${collectionData.name}`);
    
    const collection = await databases.createCollection(
      DATABASE_ID,
      collectionData.id,
      collectionData.name,
      [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ]
    );
    
    console.log(`✅ Коллекция ${collectionData.name} создана с ID: ${collection.$id}`);
    return collection;
  } catch (error) {
    if (error.code === 409) {
      console.log(`⚠️  Коллекция ${collectionData.name} уже существует`);
      return { $id: collectionData.id };
    } else {
      console.error(`❌ Ошибка создания коллекции ${collectionData.name}:`, error.message);
      throw error;
    }
  }
}

// Функция для создания атрибута
async function createAttribute(collectionId, attributeData) {
  try {
    let attribute;
    
    switch (attributeData.type) {
      case 'string':
        attribute = await databases.createStringAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.size || 255,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'integer':
        attribute = await databases.createIntegerAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.min || null,
          attributeData.max || null,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'float':
        attribute = await databases.createFloatAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.min || null,
          attributeData.max || null,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'boolean':
        attribute = await databases.createBooleanAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'datetime':
        attribute = await databases.createDatetimeAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'enum':
        attribute = await databases.createEnumAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.elements,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'json':
        attribute = await databases.createStringAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          65535,
          attributeData.required || false,
          attributeData.default || null,
          false
        );
        break;
        
      default:
        throw new Error(`Неподдерживаемый тип атрибута: ${attributeData.type}`);
    }
    
    console.log(`  ✅ Атрибут ${attributeData.key} создан`);
    return attribute;
  } catch (error) {
    if (error.code === 409) {
      console.log(`  ⚠️  Атрибут ${attributeData.key} уже существует`);
    } else {
      console.error(`  ❌ Ошибка создания атрибута ${attributeData.key}:`, error.message);
    }
  }
}

// Основная функция
async function setupAICollections() {
  console.log('🤖 Начинаем настройку AI коллекций...\n');
  
  try {
    // Создаем коллекции
    for (const collectionData of AI_COLLECTIONS) {
      await createCollection(collectionData);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🔧 Создание атрибутов...\n');
    
    // Создаем атрибуты
    for (const [collectionId, attributes] of Object.entries(COLLECTION_ATTRIBUTES)) {
      console.log(`📦 Обработка коллекции: ${collectionId}`);
      
      for (const attributeData of attributes) {
        await createAttribute(collectionId, attributeData);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      console.log(`✅ Атрибуты для ${collectionId} созданы\n`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('✅ Все AI коллекции созданы успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка настройки AI коллекций:', error);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  setupAICollections();
}

module.exports = { setupAICollections };
