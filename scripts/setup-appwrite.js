#!/usr/bin/env node

const { Client, Databases, ID, Permission, Role } = require('node-appwrite');

// Конфигурация Appwrite
const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('6872a2e20006222a3ad1')
  .setKey('standard_715f67138926017c37d228050c7493e0487a8038c72c2bd33c018b446fd71a6eeccd97cd974ec7042b31a137591d8a35261c23914cc0d0b9dd2a11f927378dfb0919c6cf8c479558ea554bce6989529e4b39eb29dac1dd0baf3d2ed4c8ce136e50bc846dc59668b3ffa1fe9f19d60e046fb2313b1bc4f89c6d7d9c6aa8af9728');

const databases = new Databases(client);
const DATABASE_ID = '6872a355001ea979ad3c';

// Существующие коллекции
const EXISTING_COLLECTIONS = {
  USERS: '6872a379002356a12506',
  ARTISTS: '6872a44a003a01c72704',
  TRACKS: '6872a49c0028a6239430',
  SUBSCRIPTIONS: '6872a5f600265dbae1c3',
  CAMPAIGNS: '6872a65a001bbaf7450b',
  PARTNERS: '6872a75d00305e3077d9',
  TEAM_MEMBERS: '6872a7c3002e99de567a',
  CASE_STUDIES: '6872a8330002a3f904dd',
  SERVICES: '6872a877002f574e8ff3',
  CONTACTS: '6872a8c3001359479d7e',
  NOTIFICATIONS: '68765e620022d0f034a6',
  ORDERS: '6876507200352e6dd85f'
};

// Новые коллекции для создания
const NEW_COLLECTIONS = [
  {
    id: 'pricing_tiers',
    name: 'Pricing Tiers',
    description: 'Тарифные планы для продвижения'
  },
  {
    id: 'social_pages',
    name: 'Social Pages',
    description: 'Социальные страницы пользователей'
  },
  {
    id: 'statistics',
    name: 'Statistics',
    description: 'Статистика продвижения'
  },
  {
    id: 'royalties',
    name: 'Royalties',
    description: 'Роялти и выплаты'
  },
  {
    id: 'files',
    name: 'Files',
    description: 'Файлы пользователей'
  },
  {
    id: 'bot_activations',
    name: 'Bot Activations',
    description: 'Активации ботов'
  },
  {
    id: 'email_campaigns',
    name: 'Email Campaigns',
    description: 'Email кампании'
  },
  {
    id: 'telegram_channels',
    name: 'Telegram Channels',
    description: 'Telegram каналы'
  },
  {
    id: 'payment_transactions',
    name: 'Payment Transactions',
    description: 'Платежные транзакции'
  },
  {
    id: 'admin_logs',
    name: 'Admin Logs',
    description: 'Логи администраторов'
  }
];

// Функция для создания коллекции
async function createCollection(collectionData) {
  try {
    console.log(`📦 Создание коллекции: ${collectionData.name}`);
    
    const collection = await databases.createCollection(
      DATABASE_ID,
      collectionData.id,
      collectionData.name,
      [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ]
    );
    
    console.log(`✅ Коллекция ${collectionData.name} создана с ID: ${collection.$id}`);
    return collection;
  } catch (error) {
    if (error.code === 409) {
      console.log(`⚠️  Коллекция ${collectionData.name} уже существует`);
      return { $id: collectionData.id };
    } else {
      console.error(`❌ Ошибка создания коллекции ${collectionData.name}:`, error.message);
      throw error;
    }
  }
}

// Функция для создания атрибута
async function createAttribute(collectionId, attributeData) {
  try {
    let attribute;
    
    switch (attributeData.type) {
      case 'string':
        attribute = await databases.createStringAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.size || 255,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'integer':
        attribute = await databases.createIntegerAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.min || null,
          attributeData.max || null,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'float':
        attribute = await databases.createFloatAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.min || null,
          attributeData.max || null,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'boolean':
        attribute = await databases.createBooleanAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'datetime':
        attribute = await databases.createDatetimeAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'enum':
        attribute = await databases.createEnumAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          attributeData.elements,
          attributeData.required || false,
          attributeData.default || null,
          attributeData.array || false
        );
        break;
        
      case 'json':
        // JSON атрибуты создаются как строковые с большим размером
        attribute = await databases.createStringAttribute(
          DATABASE_ID,
          collectionId,
          attributeData.key,
          65535, // Максимальный размер для JSON
          attributeData.required || false,
          attributeData.default || null,
          false
        );
        break;
        
      default:
        throw new Error(`Неподдерживаемый тип атрибута: ${attributeData.type}`);
    }
    
    console.log(`  ✅ Атрибут ${attributeData.key} создан`);
    return attribute;
  } catch (error) {
    if (error.code === 409) {
      console.log(`  ⚠️  Атрибут ${attributeData.key} уже существует`);
    } else {
      console.error(`  ❌ Ошибка создания атрибута ${attributeData.key}:`, error.message);
    }
  }
}

// Функция для создания индекса
async function createIndex(collectionId, indexData) {
  try {
    const index = await databases.createIndex(
      DATABASE_ID,
      collectionId,
      indexData.key,
      indexData.type || 'key',
      indexData.attributes,
      indexData.orders || []
    );
    
    console.log(`  🔍 Индекс ${indexData.key} создан`);
    return index;
  } catch (error) {
    if (error.code === 409) {
      console.log(`  ⚠️  Индекс ${indexData.key} уже существует`);
    } else {
      console.error(`  ❌ Ошибка создания индекса ${indexData.key}:`, error.message);
    }
  }
}

// Основная функция
async function setupAppwrite() {
  console.log('🚀 Начинаем настройку Appwrite...\n');
  
  try {
    // Создаем новые коллекции
    for (const collectionData of NEW_COLLECTIONS) {
      await createCollection(collectionData);
      
      // Добавляем небольшую задержку между созданием коллекций
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✅ Все коллекции созданы успешно!');
    console.log('\n📋 Следующий шаг: запустите setup-attributes.js для создания атрибутов');
    
  } catch (error) {
    console.error('❌ Ошибка настройки Appwrite:', error);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  setupAppwrite();
}

module.exports = {
  createCollection,
  createAttribute,
  createIndex,
  DATABASE_ID,
  EXISTING_COLLECTIONS,
  NEW_COLLECTIONS
};
