#!/usr/bin/env node

const { Client, Databases, ID } = require('node-appwrite');

const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('6872a2e20006222a3ad1')
  .setKey('standard_715f67138926017c37d228050c7493e0487a8038c72c2bd33c018b446fd71a6eeccd97cd974ec7042b31a137591d8a35261c23914cc0d0b9dd2a11f927378dfb0919c6cf8c479558ea554bce6989529e4b39eb29dac1dd0baf3d2ed4c8ce136e50bc846dc59668b3ffa1fe9f19d60e046fb2313b1bc4f89c6d7d9c6aa8af9728');

const databases = new Databases(client);
const DATABASE_ID = '6872a355001ea979ad3c';

// Готовые промпты для AI
const AI_PROMPTS_DATA = [
  // Промпты для артистов
  {
    prompt_id: 'artist-new-release',
    category: 'artist',
    title: 'Продвижение нового релиза',
    description: 'Как эффективно продвинуть новый трек или альбом',
    prompt_text: 'Мне нужно продвинуть новый трек/альбом. Помоги составить стратегию продвижения.',
    tags: JSON.stringify(['релиз', 'продвижение', 'стратегия']),
    popularity: 95,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'artist-social-growth',
    category: 'artist',
    title: 'Рост в социальных сетях',
    description: 'Увеличение подписчиков и вовлеченности',
    prompt_text: 'Хочу увеличить количество подписчиков в Instagram, TikTok и YouTube. Какие сервисы посоветуешь?',
    tags: JSON.stringify(['соцсети', 'подписчики', 'рост']),
    popularity: 88,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'artist-brand-building',
    category: 'artist',
    title: 'Построение бренда артиста',
    description: 'Создание узнаваемого образа и стиля',
    prompt_text: 'Помоги создать сильный бренд артиста. Нужна стратегия для узнаваемости.',
    tags: JSON.stringify(['бренд', 'имидж', 'узнаваемость']),
    popularity: 75,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'artist-concert-promo',
    category: 'artist',
    title: 'Продвижение концертов',
    description: 'Реклама живых выступлений и туров',
    prompt_text: 'У меня запланированы концерты. Как лучше их прорекламировать для максимальной посещаемости?',
    tags: JSON.stringify(['концерты', 'туры', 'билеты']),
    popularity: 70,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },

  // Промпты для треков
  {
    prompt_id: 'track-viral-strategy',
    category: 'track',
    title: 'Вирусная стратегия для трека',
    description: 'Как сделать трек вирусным в TikTok и других платформах',
    prompt_text: 'Хочу сделать свой трек вирусным в TikTok и других соцсетях. Какую стратегию посоветуешь?',
    tags: JSON.stringify(['вирус', 'tiktok', 'тренды']),
    popularity: 92,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'track-streaming-boost',
    category: 'track',
    title: 'Увеличение прослушиваний',
    description: 'Рост стримов на Spotify, Apple Music, Яндекс.Музыка',
    prompt_text: 'Нужно увеличить количество прослушиваний трека на стриминговых платформах. Что делать?',
    tags: JSON.stringify(['стриминг', 'spotify', 'прослушивания']),
    popularity: 85,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'track-playlist-placement',
    category: 'track',
    title: 'Попадание в плейлисты',
    description: 'Размещение трека в популярных плейлистах',
    prompt_text: 'Как попасть в популярные плейлисты на Spotify и других платформах?',
    tags: JSON.stringify(['плейлисты', 'кураторы', 'размещение']),
    popularity: 80,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    prompt_id: 'track-radio-promotion',
    category: 'track',
    title: 'Продвижение на радио',
    description: 'Попадание в ротацию радиостанций',
    prompt_text: 'Хочу, чтобы мой трек играли на радио. Как организовать радиопродвижение?',
    tags: JSON.stringify(['радио', 'ротация', 'эфир']),
    popularity: 65,
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Шаблоны сервисов
const SERVICE_TEMPLATES_DATA = [
  // Сервисы для артистов
  {
    template_id: 'artist-instagram-growth',
    category: 'artist',
    service_type: 'social_media',
    platform: 'Instagram',
    name: 'Продвижение в Instagram',
    description: 'Органический рост подписчиков, лайков и комментариев в Instagram',
    icon: 'instagram',
    color: '#E4405F',
    base_price: 15000,
    duration_days: 30,
    features: JSON.stringify([
      'Органический рост подписчиков',
      'Увеличение лайков и комментариев',
      'Создание контента',
      'Stories продвижение',
      'Работа с хештегами'
    ]),
    metrics: JSON.stringify({
      followers: '+500-2000',
      engagement: '+25%',
      reach: '+300%'
    }),
    requirements: JSON.stringify(['Активный аккаунт', 'Качественный контент']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    template_id: 'artist-tiktok-viral',
    category: 'artist',
    service_type: 'social_media',
    platform: 'TikTok',
    name: 'Вирусное продвижение в TikTok',
    description: 'Создание вирусного контента и продвижение в TikTok',
    icon: 'tiktok',
    color: '#000000',
    base_price: 20000,
    duration_days: 21,
    features: JSON.stringify([
      'Создание вирусного контента',
      'Работа с трендами',
      'Коллаборации с тиктокерами',
      'Хештег-челленджи',
      'Продвижение через алгоритмы'
    ]),
    metrics: JSON.stringify({
      views: '+100K-1M',
      followers: '+1000-5000',
      viral_potential: '85%'
    }),
    requirements: JSON.stringify(['Музыкальный контент', 'Готовность к экспериментам']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    template_id: 'artist-youtube-channel',
    category: 'artist',
    service_type: 'content',
    platform: 'YouTube',
    name: 'Развитие YouTube канала',
    description: 'Комплексное развитие YouTube канала артиста',
    icon: 'youtube',
    color: '#FF0000',
    base_price: 35000,
    duration_days: 60,
    features: JSON.stringify([
      'Оптимизация канала',
      'Создание видеоконтента',
      'SEO оптимизация',
      'Продвижение видео',
      'Монетизация канала'
    ]),
    metrics: JSON.stringify({
      subscribers: '+2000-10000',
      views: '+50K-500K',
      watch_time: '+200%'
    }),
    requirements: JSON.stringify(['Регулярный контент', 'Качественное видео']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },

  // Сервисы для треков
  {
    template_id: 'track-spotify-promotion',
    category: 'track',
    service_type: 'streaming',
    platform: 'Spotify',
    name: 'Продвижение на Spotify',
    description: 'Увеличение прослушиваний и попадание в плейлисты Spotify',
    icon: 'spotify',
    color: '#1DB954',
    base_price: 12000,
    duration_days: 14,
    features: JSON.stringify([
      'Размещение в плейлистах',
      'Органические прослушивания',
      'Работа с кураторами',
      'Spotify for Artists оптимизация',
      'Аналитика и отчеты'
    ]),
    metrics: JSON.stringify({
      streams: '+10K-100K',
      monthly_listeners: '+500-5000',
      playlist_adds: '10-50'
    }),
    requirements: JSON.stringify(['Трек на Spotify', 'Качественная музыка']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    template_id: 'track-tiktok-trend',
    category: 'track',
    service_type: 'social_media',
    platform: 'TikTok',
    name: 'Создание тренда в TikTok',
    description: 'Превращение трека в TikTok тренд с помощью челленджей',
    icon: 'tiktok',
    color: '#000000',
    base_price: 25000,
    duration_days: 21,
    features: JSON.stringify([
      'Создание челленджа',
      'Работа с инфлюенсерами',
      'Вирусные видео',
      'Хештег кампании',
      'Мониторинг трендов'
    ]),
    metrics: JSON.stringify({
      challenge_views: '+1M-10M',
      user_videos: '+1000-10000',
      viral_coefficient: '90%'
    }),
    requirements: JSON.stringify(['Запоминающийся отрывок', 'Танцевальный потенциал']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    template_id: 'track-radio-campaign',
    category: 'track',
    service_type: 'pr',
    platform: 'Radio',
    name: 'Радиокампания',
    description: 'Продвижение трека на радиостанциях',
    icon: 'radio',
    color: '#FF6B35',
    base_price: 40000,
    duration_days: 30,
    features: JSON.stringify([
      'Размещение на радиостанциях',
      'Интервью с артистом',
      'Ротация в эфире',
      'Региональное покрытие',
      'Отчеты по эфирам'
    ]),
    metrics: JSON.stringify({
      radio_stations: '20-50',
      airplay_hours: '100-500',
      audience_reach: '500K-2M'
    }),
    requirements: JSON.stringify(['Радиоформат трека', 'Пресс-кит артиста']),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Функция для создания документа
async function createDocument(collectionId, data) {
  try {
    const document = await databases.createDocument(
      DATABASE_ID,
      collectionId,
      ID.unique(),
      data
    );
    
    console.log(`✅ Документ создан в ${collectionId}: ${document.$id}`);
    return document;
  } catch (error) {
    console.error(`❌ Ошибка создания документа в ${collectionId}:`, error.message);
    throw error;
  }
}

// Основная функция
async function seedAIData() {
  console.log('🤖 Заполнение AI данными...\n');
  
  try {
    // Заполняем промпты
    console.log('📝 Создание AI промптов...');
    for (const promptData of AI_PROMPTS_DATA) {
      await createDocument('ai_prompts', promptData);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Заполняем шаблоны сервисов
    console.log('\n🛠️ Создание шаблонов сервисов...');
    for (const templateData of SERVICE_TEMPLATES_DATA) {
      await createDocument('service_templates', templateData);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n✅ AI данные успешно загружены!');
    
  } catch (error) {
    console.error('❌ Ошибка заполнения AI данными:', error);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  seedAIData();
}

module.exports = { seedAIData };
