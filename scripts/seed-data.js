#!/usr/bin/env node

const { Client, Databases, ID, Permission, Role } = require('node-appwrite');

// Конфигурация Appwrite
const client = new Client()
  .setEndpoint('https://cloud.appwrite.io/v1')
  .setProject('6872a2e20006222a3ad1')
  .setKey('standard_715f67138926017c37d228050c7493e0487a8038c72c2bd33c018b446fd71a6eeccd97cd974ec7042b31a137591d8a35261c23914cc0d0b9dd2a11f927378dfb0919c6cf8c479558ea554bce6989529e4b39eb29dac1dd0baf3d2ed4c8ce136e50bc846dc59668b3ffa1fe9f19d60e046fb2313b1bc4f89c6d7d9c6aa8af9728');

const databases = new Databases(client);
const DATABASE_ID = '6872a355001ea979ad3c';

// Тестовые данные для тарифных планов
const PRICING_TIERS_DATA = [
  // Тарифы для артистов
  {
    tier_id: 'artist-starter',
    name: 'Стар<PERSON>',
    type: 'artist',
    price: 25000,
    currency: 'RUB',
    duration: 30,
    popular: false,
    features: JSON.stringify([
      { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', limit: 3, included: true },
      { id: 'basic-bots', name: 'Базовые боты', description: 'Автоматизация лайков и комментариев', limit: 2, included: true },
      { id: 'email-campaigns', name: 'Email рассылки', description: 'Рассылки для фанатов', limit: 5, included: true },
      { id: 'telegram-growth', name: 'Рост Telegram', description: 'Привлечение подписчиков в Telegram', limit: 1000, included: true },
      { id: 'analytics', name: 'Аналитика', description: 'Базовая аналитика', included: true },
      { id: 'support', name: 'Поддержка', description: 'Email поддержка', included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: 3, 
      botActivations: 2, 
      emailCampaigns: 5, 
      telegramMembers: 1000,
      analyticsReports: 1
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    tier_id: 'artist-pro',
    name: 'Профи',
    type: 'artist',
    price: 50000,
    currency: 'RUB',
    duration: 30,
    popular: true,
    features: JSON.stringify([
      { id: 'social-accounts', name: 'Социальные аккаунты', description: 'Управление аккаунтами в соцсетях', limit: 8, included: true },
      { id: 'advanced-bots', name: 'Продвинутые боты', description: 'ИИ боты для всех платформ', limit: 6, included: true },
      { id: 'email-campaigns', name: 'Email рассылки', description: 'Неограниченные рассылки', included: true },
      { id: 'telegram-growth', name: 'Рост Telegram', description: 'Массовое привлечение подписчиков', limit: 5000, included: true },
      { id: 'content-creation', name: 'Создание контента', description: 'Генерация контента с ИИ', included: true },
      { id: 'influencer-network', name: 'Сеть инфлюенсеров', description: 'Доступ к сети блогеров', included: true },
      { id: 'analytics', name: 'Аналитика', description: 'Продвинутая аналитика', included: true },
      { id: 'support', name: 'Поддержка', description: 'Приоритетная поддержка 24/7', included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: 8, 
      botActivations: 6, 
      emailCampaigns: -1, 
      telegramMembers: 5000,
      analyticsReports: 5
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    tier_id: 'artist-enterprise',
    name: 'Энтерпрайз',
    type: 'artist',
    price: 100000,
    currency: 'RUB',
    duration: 30,
    popular: false,
    features: JSON.stringify([
      { id: 'unlimited-accounts', name: 'Неограниченные аккаунты', description: 'Без лимитов на количество', included: true },
      { id: 'ai-bots', name: 'ИИ боты премиум', description: 'Самые умные боты с GPT-4', included: true },
      { id: 'viral-strategies', name: 'Вирусные стратегии', description: 'Создание вирусного контента', included: true },
      { id: 'personal-manager', name: 'Персональный менеджер', description: 'Выделенный специалист', included: true },
      { id: 'global-outreach', name: 'Глобальный охват', description: 'Международное продвижение', included: true },
      { id: 'custom-bots', name: 'Кастомные боты', description: 'Разработка ботов под задачи', included: true },
      { id: 'white-label', name: 'White Label', description: 'Брендирование под клиента', included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: -1, 
      botActivations: -1, 
      emailCampaigns: -1, 
      telegramMembers: -1,
      analyticsReports: -1
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  
  // Тарифы для треков
  {
    tier_id: 'track-basic',
    name: 'Базовый',
    type: 'track',
    price: 15000,
    currency: 'RUB',
    duration: 14,
    popular: false,
    features: JSON.stringify([
      { id: 'playlist-placement', name: 'Размещение в плейлистах', description: 'Spotify, Apple Music, Яндекс', limit: 10, included: true },
      { id: 'social-promotion', name: 'Продвижение в соцсетях', description: 'Instagram, TikTok, VK', included: true },
      { id: 'basic-bots', name: 'Базовые боты', description: 'Автолайки и репосты', limit: 2, included: true },
      { id: 'email-campaigns', name: 'Email рассылки', description: 'Анонс релиза', limit: 1, included: true },
      { id: 'telegram-posts', name: 'Telegram посты', description: 'Размещение в каналах', limit: 5, included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: 2, 
      botActivations: 2, 
      emailCampaigns: 1, 
      telegramMembers: 500,
      analyticsReports: 1
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    tier_id: 'track-viral',
    name: 'Вирусный',
    type: 'track',
    price: 35000,
    currency: 'RUB',
    duration: 21,
    popular: true,
    features: JSON.stringify([
      { id: 'viral-campaign', name: 'Вирусная кампания', description: 'Создание трендового контента', included: true },
      { id: 'influencer-network', name: 'Сеть инфлюенсеров', description: 'Охват через блогеров', limit: 20, included: true },
      { id: 'ai-content', name: 'ИИ контент', description: 'Генерация мемов и видео', included: true },
      { id: 'bot-engagement', name: 'Массовое вовлечение', description: 'Через умных ботов', included: true },
      { id: 'telegram-channels', name: 'Telegram каналы', description: 'Размещение в каналах', limit: 50, included: true },
      { id: 'radio-promotion', name: 'Радио продвижение', description: 'Размещение на радиостанциях', included: true },
      { id: 'press-releases', name: 'Пресс-релизы', description: 'В музыкальных СМИ', included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: 5, 
      botActivations: 8, 
      emailCampaigns: 3, 
      telegramMembers: 2000,
      analyticsReports: 3
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    tier_id: 'track-platinum',
    name: 'Платиновый',
    type: 'track',
    price: 75000,
    currency: 'RUB',
    duration: 30,
    popular: false,
    features: JSON.stringify([
      { id: 'platinum-campaign', name: 'Платиновая кампания', description: 'Максимальный охват', included: true },
      { id: 'celebrity-network', name: 'Сеть знаменитостей', description: 'Продвижение через звезд', included: true },
      { id: 'bot-army', name: 'Армия ботов', description: 'Сотни умных ботов', included: true },
      { id: 'global-outreach', name: 'Глобальный охват', description: 'Международное продвижение', included: true },
      { id: 'telegram-network', name: 'Сеть Telegram', description: 'Более 100 каналов', included: true },
      { id: 'media-coverage', name: 'Освещение в СМИ', description: 'Все музыкальные СМИ', included: true },
      { id: 'streaming-boost', name: 'Продвижение в топы', description: 'Стриминговые сервисы', included: true },
      { id: 'dedicated-team', name: 'Выделенная команда', description: '5+ специалистов', included: true }
    ]),
    limits: JSON.stringify({ 
      socialAccounts: -1, 
      botActivations: -1, 
      emailCampaigns: -1, 
      telegramMembers: -1,
      analyticsReports: -1
    }),
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Функция для создания документа
async function createDocument(collectionId, data) {
  try {
    const document = await databases.createDocument(
      DATABASE_ID,
      collectionId,
      ID.unique(),
      data
    );
    
    console.log(`✅ Документ создан с ID: ${document.$id}`);
    return document;
  } catch (error) {
    console.error(`❌ Ошибка создания документа:`, error.message);
    throw error;
  }
}

// Основная функция для заполнения данными
async function seedData() {
  console.log('🌱 Начинаем заполнение тестовыми данными...\n');
  
  try {
    // Заполняем тарифные планы
    console.log('📦 Заполнение тарифных планов...');
    for (const tierData of PRICING_TIERS_DATA) {
      await createDocument('pricing_tiers', tierData);
      // Небольшая задержка между созданием документов
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✅ Тестовые данные успешно добавлены!');
    
  } catch (error) {
    console.error('❌ Ошибка заполнения данными:', error);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  seedData();
}

module.exports = { seedData };
