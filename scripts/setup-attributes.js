#!/usr/bin/env node

const { createAttribute, createIndex, DATABASE_ID, EXISTING_COLLECTIONS } = require('./setup-appwrite');

// Определения атрибутов для всех коллекций
const COLLECTION_ATTRIBUTES = {
  // 1. Users Collection - дополнительные атрибуты
  [EXISTING_COLLECTIONS.USERS]: [
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'type', type: 'enum', elements: ['artist', 'label', 'admin'], required: true, default: 'artist' },
    { key: 'subscription_plan', type: 'string', size: 100 },
    { key: 'phone', type: 'string', size: 20 },
    { key: 'avatar', type: 'string', size: 500 },
    { key: 'bio', type: 'string', size: 1000 },
    { key: 'links', type: 'json' },
    { key: 'verified', type: 'boolean', default: false },
    { key: 'tier', type: 'string', size: 50 },
    { key: 'followers', type: 'integer', default: 0 },
    { key: 'monthly_listeners', type: 'integer', default: 0 },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // 2. Orders Collection - обновленные атрибуты
  [EXISTING_COLLECTIONS.ORDERS]: [
    { key: 'order_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'pricing_tier_id', type: 'string', size: 255 },
    { key: 'service_type', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'package_type', type: 'string', size: 100, required: true },
    { key: 'title', type: 'string', size: 255, required: true },
    { key: 'description', type: 'string', size: 2000 },
    { key: 'price', type: 'integer', required: true },
    { key: 'currency', type: 'string', size: 3, default: 'RUB' },
    { key: 'status', type: 'enum', elements: ['draft', 'pending_payment', 'paid', 'in_progress', 'completed', 'cancelled'], default: 'draft' },
    { key: 'payment_status', type: 'enum', elements: ['pending', 'processing', 'completed', 'failed', 'refunded'], default: 'pending' },
    { key: 'payment_method', type: 'enum', elements: ['card', 'crypto', 'sbp'] },
    { key: 'payment_id', type: 'string', size: 255 },
    { key: 'progress', type: 'integer', default: 0, min: 0, max: 100 },
    { key: 'client_name', type: 'string', size: 255 },
    { key: 'client_email', type: 'string', size: 255 },
    { key: 'client_phone', type: 'string', size: 20 },
    { key: 'client_telegram', type: 'string', size: 100 },
    { key: 'requirements', type: 'json' },
    { key: 'metadata', type: 'json' },
    { key: 'notes', type: 'string', size: 2000 },
    { key: 'files', type: 'json' },
    { key: 'deliverables', type: 'json' },
    { key: 'assigned_manager_id', type: 'string', size: 255 },
    { key: 'estimated_completion_date', type: 'datetime' },
    { key: 'actual_completion_date', type: 'datetime' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true },
    { key: 'paid_at', type: 'datetime' },
    { key: 'completed_at', type: 'datetime' }
  ],

  // 3. Notifications Collection - обновленные атрибуты
  [EXISTING_COLLECTIONS.NOTIFICATIONS]: [
    { key: 'notification_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'type', type: 'string', size: 100, required: true },
    { key: 'category', type: 'enum', elements: ['info', 'success', 'warning', 'error'], default: 'info' },
    { key: 'title', type: 'string', size: 255, required: true },
    { key: 'message', type: 'string', size: 1000, required: true },
    { key: 'action_url', type: 'string', size: 500 },
    { key: 'action_text', type: 'string', size: 100 },
    { key: 'is_read', type: 'boolean', default: false },
    { key: 'is_important', type: 'boolean', default: false },
    { key: 'priority', type: 'enum', elements: ['low', 'normal', 'high', 'urgent'], default: 'normal' },
    { key: 'related_order_id', type: 'string', size: 255 },
    { key: 'related_user_id', type: 'string', size: 255 },
    { key: 'metadata', type: 'json' },
    { key: 'avatar', type: 'string', size: 500 },
    { key: 'expires_at', type: 'datetime' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // 4. Pricing Tiers Collection
  'pricing_tiers': [
    { key: 'tier_id', type: 'string', size: 255, required: true },
    { key: 'name', type: 'string', size: 255, required: true },
    { key: 'type', type: 'enum', elements: ['artist', 'track'], required: true },
    { key: 'price', type: 'integer', required: true },
    { key: 'currency', type: 'string', size: 3, default: 'RUB' },
    { key: 'duration', type: 'integer', required: true },
    { key: 'features', type: 'json', required: true },
    { key: 'popular', type: 'boolean', default: false },
    { key: 'discount_percentage', type: 'integer', min: 0, max: 100 },
    { key: 'discount_valid_until', type: 'datetime' },
    { key: 'limits', type: 'json', required: true },
    { key: 'active', type: 'boolean', default: true },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // 5. Social Pages Collection
  'social_pages': [
    { key: 'page_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'platform', type: 'enum', elements: ['instagram', 'tiktok', 'youtube', 'vk', 'telegram', 'twitter'], required: true },
    { key: 'username', type: 'string', size: 255, required: true },
    { key: 'url', type: 'string', size: 500, required: true },
    { key: 'followers_count', type: 'integer', default: 0 },
    { key: 'verified', type: 'boolean', default: false },
    { key: 'active', type: 'boolean', default: true },
    { key: 'last_sync', type: 'datetime' },
    { key: 'metadata', type: 'json' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // 6. Statistics Collection
  'statistics': [
    { key: 'stat_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'track_id', type: 'string', size: 255 },
    { key: 'order_id', type: 'string', size: 255 },
    { key: 'platform', type: 'string', size: 100, required: true },
    { key: 'metric_type', type: 'enum', elements: ['plays', 'likes', 'shares', 'comments', 'followers', 'reach'], required: true },
    { key: 'value', type: 'integer', required: true },
    { key: 'date', type: 'datetime', required: true },
    { key: 'metadata', type: 'json' },
    { key: 'created_at', type: 'datetime', required: true }
  ],

  // 7. Bot Activations Collection
  'bot_activations': [
    { key: 'activation_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'order_id', type: 'string', size: 255, required: true },
    { key: 'bot_type', type: 'enum', elements: ['like_bot', 'comment_bot', 'follow_bot', 'view_bot', 'share_bot'], required: true },
    { key: 'platform', type: 'string', size: 100, required: true },
    { key: 'target_url', type: 'string', size: 500, required: true },
    { key: 'status', type: 'enum', elements: ['pending', 'active', 'paused', 'completed', 'failed'], default: 'pending' },
    { key: 'actions_completed', type: 'integer', default: 0 },
    { key: 'actions_target', type: 'integer', required: true },
    { key: 'config', type: 'json' },
    { key: 'started_at', type: 'datetime' },
    { key: 'completed_at', type: 'datetime' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ],

  // 8. Payment Transactions Collection
  'payment_transactions': [
    { key: 'transaction_id', type: 'string', size: 255, required: true },
    { key: 'order_id', type: 'string', size: 255, required: true },
    { key: 'user_id', type: 'string', size: 255, required: true },
    { key: 'payment_method', type: 'enum', elements: ['card', 'crypto', 'sbp'], required: true },
    { key: 'amount', type: 'integer', required: true },
    { key: 'currency', type: 'string', size: 3, required: true },
    { key: 'status', type: 'enum', elements: ['pending', 'processing', 'completed', 'failed', 'refunded'], default: 'pending' },
    { key: 'provider', type: 'string', size: 100 },
    { key: 'provider_transaction_id', type: 'string', size: 255 },
    { key: 'metadata', type: 'json' },
    { key: 'processed_at', type: 'datetime' },
    { key: 'created_at', type: 'datetime', required: true },
    { key: 'updated_at', type: 'datetime', required: true }
  ]
};

// Индексы для оптимизации
const COLLECTION_INDEXES = {
  [EXISTING_COLLECTIONS.USERS]: [
    { key: 'user_id_unique', type: 'unique', attributes: ['user_id'] },
    { key: 'email_unique', type: 'unique', attributes: ['email'] },
    { key: 'type_index', type: 'key', attributes: ['type'] }
  ],
  
  [EXISTING_COLLECTIONS.ORDERS]: [
    { key: 'order_id_unique', type: 'unique', attributes: ['order_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'status_index', type: 'key', attributes: ['status'] },
    { key: 'created_at_index', type: 'key', attributes: ['created_at'], orders: ['desc'] }
  ],
  
  [EXISTING_COLLECTIONS.NOTIFICATIONS]: [
    { key: 'notification_id_unique', type: 'unique', attributes: ['notification_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'is_read_index', type: 'key', attributes: ['is_read'] },
    { key: 'created_at_index', type: 'key', attributes: ['created_at'], orders: ['desc'] }
  ],
  
  'pricing_tiers': [
    { key: 'tier_id_unique', type: 'unique', attributes: ['tier_id'] },
    { key: 'type_index', type: 'key', attributes: ['type'] },
    { key: 'active_index', type: 'key', attributes: ['active'] }
  ],
  
  'social_pages': [
    { key: 'page_id_unique', type: 'unique', attributes: ['page_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'platform_index', type: 'key', attributes: ['platform'] }
  ],
  
  'statistics': [
    { key: 'stat_id_unique', type: 'unique', attributes: ['stat_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'date_index', type: 'key', attributes: ['date'], orders: ['desc'] }
  ],
  
  'bot_activations': [
    { key: 'activation_id_unique', type: 'unique', attributes: ['activation_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'order_id_index', type: 'key', attributes: ['order_id'] },
    { key: 'status_index', type: 'key', attributes: ['status'] }
  ],
  
  'payment_transactions': [
    { key: 'transaction_id_unique', type: 'unique', attributes: ['transaction_id'] },
    { key: 'order_id_index', type: 'key', attributes: ['order_id'] },
    { key: 'user_id_index', type: 'key', attributes: ['user_id'] },
    { key: 'status_index', type: 'key', attributes: ['status'] }
  ]
};

// Основная функция для создания атрибутов
async function setupAttributes() {
  console.log('🔧 Начинаем создание атрибутов...\n');
  
  try {
    for (const [collectionId, attributes] of Object.entries(COLLECTION_ATTRIBUTES)) {
      console.log(`📦 Обработка коллекции: ${collectionId}`);
      
      // Создаем атрибуты
      for (const attributeData of attributes) {
        await createAttribute(collectionId, attributeData);
        // Небольшая задержка между созданием атрибутов
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      console.log(`✅ Атрибуты для ${collectionId} созданы\n`);
      
      // Задержка между коллекциями
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('🔍 Создание индексов...\n');
    
    // Создаем индексы
    for (const [collectionId, indexes] of Object.entries(COLLECTION_INDEXES)) {
      console.log(`🔍 Создание индексов для: ${collectionId}`);
      
      for (const indexData of indexes) {
        await createIndex(collectionId, indexData);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      console.log(`✅ Индексы для ${collectionId} созданы\n`);
    }
    
    console.log('✅ Все атрибуты и индексы созданы успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка создания атрибутов:', error);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  setupAttributes();
}

module.exports = { setupAttributes };
