# 🤖💳 HIVE Agency - Интеграция платежей и ботов

## 🎯 Обзор системы

HIVE Agency теперь включает полноценную систему интеграции платежей с умными ботами для продвижения музыки. Система автоматически создает заказы после оплаты и предоставляет админам мощные инструменты для управления продвижением.

## 💳 Система платежей

### **Тарифные планы**

#### **Продвижение артистов**
- 🥉 **Старт** - 25,000 ₽/месяц
  - 3 социальных аккаунта
  - 2 базовых бота
  - 5 email кампаний
  - 1,000 подписчиков Telegram

- 🥈 **Профи** - 50,000 ₽/месяц (Популярный)
  - 8 социальных аккаунтов
  - 6 продвинутых ботов
  - Неограниченные email кампании
  - 5,000 подписчиков Telegram

- 🥇 **Энтерпрайз** - 100,000 ₽/месяц
  - Неограниченные аккаунты
  - ИИ боты премиум с GPT-4
  - Персональный менеджер
  - Кастомные боты

#### **Продвижение треков**
- 🎵 **Базовый** - 15,000 ₽/14 дней
- 🎵 **Вирусный** - 35,000 ₽/21 день (Популярный)
- 🎵 **Платиновый** - 75,000 ₽/30 дней

### **Способы оплаты**
- 💳 **Банковские карты** (Stripe) - 2.9% комиссия
- ₿ **Криптовалюты** (10+ монет) - 1% комиссия
- 📱 **СБП** (Система быстрых платежей) - 0% комиссия

## 🤖 Система ботов

### **Instagram боты**
```typescript
// Engagement Bot
- Умные лайки и комментарии
- Анализ хештегов
- Отслеживание конкурентов
- Оптимальное время активности

// Content Bot
- ИИ генерация подписей
- Анализ трендов
- Оптимизация визуального контента
- Автоматические Stories

// Outreach Bot
- Персонализированные DM
- Контакт с инфлюенсерами
- Скоринг лидов
- Последовательности follow-up
```

### **YouTube боты**
```typescript
// Promotion Bot
- Анализ видео
- Оптимизация ключевых слов
- Исследование конкурентов
- Симуляция времени просмотра
```

### **TikTok боты**
```typescript
// Viral Bot
- Детекция трендов
- Оптимизация хештегов
- Предсказание вирусности
- Человекоподобное поведение
```

### **Telegram боты**
```typescript
// Growth Bot
- Скрапинг целевой аудитории
- Фильтрация качества
- Оптимизация удержания
- Постепенный рост

// Content Bot
- Автопостинг
- Кросспостинг
- Курирование контента
- Анализ аудитории
```

### **Email боты**
```typescript
// Scraper Bot
- Извлечение email адресов
- Валидация адресов
- Построение списков
- Таргетинг по жанрам
- GDPR соответствие
```

## 🎯 Процесс заказа

### **1. Выбор тарифа**
```typescript
// Пользователь выбирает тариф
const handleSelectPlan = (tier: PricingTier) => {
  if (!user) {
    // Перенаправление на вход
    window.location.href = '/auth/login?redirect=/pricing';
    return;
  }
  
  setSelectedTier(tier);
  setShowPaymentModal(true);
};
```

### **2. Оплата**
```typescript
// Обработка успешного платежа
const handlePaymentSuccess = async (paymentId: string) => {
  const orderData = {
    userId: user?.id,
    pricingTierId: selectedTier?.id,
    paymentId,
    amount: selectedTier?.price,
    currency: selectedTier?.currency,
    status: 'paid'
  };

  // Создание заказа
  await fetch('/api/orders/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(orderData)
  });
};
```

### **3. Создание заказа**
```typescript
// API создания заказа
export async function POST(request: NextRequest) {
  // Создание заказа в Appwrite
  const order = await databases.createDocument(
    DATABASE_ID,
    ORDERS_COLLECTION_ID,
    ID.unique(),
    orderData
  );

  // Уведомление админов
  await createNotification({
    type: 'order_created',
    title: 'Новый заказ',
    message: `Создан заказ #${order.$id}`,
    userId: 'admin'
  });
}
```

## 🛠️ Админ-панель

### **Обработка заказов**
```typescript
// Переход к обработке заказа
<button onClick={() => {
  setSelectedOrderId(order.id);
  setShowOrderProcessing(true);
}}>
  <ArrowRight className="w-4 h-4" />
  Обработать
</button>
```

### **Управление ресурсами**

#### **Социальные аккаунты**
```typescript
// Добавление аккаунта
const addSocialAccount = async (accountData) => {
  const newAccount = {
    id: ID.unique(),
    platform: accountData.platform,
    username: accountData.username,
    followers: accountData.followers,
    engagement: accountData.engagement,
    credentials: {
      accessToken: accountData.accessToken
    },
    stats: {
      postsCount: 0,
      avgLikes: 0,
      lastActivity: new Date().toISOString()
    }
  };

  // Обновление заказа
  await updateOrderResources(orderId, { socialAccounts: [...existing, newAccount] });
};
```

#### **Активация ботов**
```typescript
// Активация бота
const activateBot = async (botConfig) => {
  const template = BOT_TEMPLATES[botConfig.platform][botConfig.type];
  
  const newBot = {
    id: ID.unique(),
    name: template.name,
    platform: botConfig.platform,
    type: botConfig.type,
    config: {
      targetKeywords: botConfig.targetKeywords,
      activityLevel: botConfig.activityLevel,
      workingHours: botConfig.workingHours,
      limits: {
        dailyActions: botConfig.dailyActions,
        hourlyActions: botConfig.hourlyActions
      }
    },
    capabilities: template.capabilities,
    smartFeatures: template.smartFeatures,
    antiDetection: template.antiDetection
  };

  await updateOrderResources(orderId, { activeBots: [...existing, newBot] });
};
```

## 🔄 Автоматизация

### **Уведомления**
```typescript
// Автоматические уведомления при изменении статуса
const updateOrderStatus = async (orderId, status) => {
  await updateOrder(orderId, { status });
  
  const messages = {
    'in_progress': 'Ваш заказ принят в работу',
    'completed': 'Ваш заказ успешно выполнен',
    'cancelled': 'Ваш заказ был отменен'
  };

  await createNotification({
    type: `order_${status}`,
    title: 'Обновление заказа',
    message: messages[status],
    userId: order.userId
  });
};
```

### **Мониторинг ботов**
```typescript
// Обновление статистики ботов
const updateBotStats = async (botId, stats) => {
  await fetch(`/api/admin/orders/${orderId}/bots`, {
    method: 'PUT',
    body: JSON.stringify({
      botId,
      action: 'update_stats',
      config: {
        totalActions: stats.totalActions,
        successRate: stats.successRate,
        generatedLeads: stats.generatedLeads
      }
    })
  });
};
```

## 📊 Аналитика

### **Метрики продвижения**
- 📈 **Общий охват**: 1.2M+ пользователей
- ⚡ **Вовлеченность**: 8.5% средняя
- 🎯 **Конверсии**: 3.2% в подписчики
- 🌟 **Новые фаны**: 15.7K за неделю

### **Прогресс по платформам**
- Instagram: 85% выполнения
- YouTube: 72% выполнения
- TikTok: 91% выполнения
- Telegram: 68% выполнения
- VK: 79% выполнения

## 🛡️ Безопасность ботов

### **Анти-детекция**
```typescript
const antiDetectionFeatures = {
  humanDelays: true,        // Человеческие задержки
  randomPatterns: true,     // Случайные паттерны
  proxyRotation: true,      // Ротация прокси
  deviceFingerprinting: true, // Отпечатки устройств
  sessionManagement: true,   // Управление сессиями
  captchaSolving: true,     // Решение капчи
  naturalLanguage: true     // Естественный язык
};
```

### **Лимиты активности**
```typescript
const botLimits = {
  dailyActions: 100,        // Действий в день
  hourlyActions: 10,        // Действий в час
  workingHours: {           // Рабочие часы
    start: '09:00',
    end: '21:00',
    timezone: 'Europe/Moscow'
  }
};
```

## 🚀 Развертывание

### **Переменные окружения**
```env
# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Crypto
NEXT_PUBLIC_CRYPTO_API_KEY=crypto_api_key
NEXT_PUBLIC_CRYPTO_API_URL=https://api.cryptopay.com

# Appwrite
NEXT_PUBLIC_APPWRITE_ORDERS_COLLECTION_ID=orders_collection_id
```

### **API эндпоинты**
```
POST /api/orders/create              # Создание заказа
GET  /api/admin/orders/[id]          # Получение заказа
PUT  /api/admin/orders/[id]/status   # Обновление статуса
POST /api/admin/orders/[id]/social-accounts  # Добавление аккаунта
POST /api/admin/orders/[id]/bots     # Активация бота
```

## 🎯 Результаты

### **Для клиентов**
- ✅ Простая оплата в 3 клика
- ✅ Автоматическое создание заказа
- ✅ Уведомления о прогрессе
- ✅ Прозрачная аналитика

### **Для админов**
- ✅ Централизованное управление
- ✅ Автоматизация процессов
- ✅ Мониторинг ботов
- ✅ Детальная аналитика

### **Для бизнеса**
- ✅ Масштабируемость
- ✅ Автоматизация
- ✅ Высокая конверсия
- ✅ Минимальные затраты

Система готова к обработке тысяч заказов и управлению сотнями ботов одновременно! 🎵🚀
