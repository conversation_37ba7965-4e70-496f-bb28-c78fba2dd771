# Appwrite Collections для HIVE Agency

## 1. Collection: orders (Заказы)

### Атрибуты:

| Attribute ID | Type | Size | Required | Default | Array | Description |
|-------------|------|------|----------|---------|-------|-------------|
| orderId | string | 36 | Yes | - | No | Уникальный ID заказа |
| userId | string | 36 | Yes | - | No | ID пользователя (связь с users) |
| serviceType | string | 50 | Yes | - | No | Тип услуги (artist_promotion, track_promotion) |
| packageType | string | 50 | Yes | - | No | Тип пакета (starter, pro, premium) |
| title | string | 200 | Yes | - | No | Название заказа |
| description | string | 1000 | No | - | No | Описание заказа |
| price | integer | - | Yes | 0 | No | Цена в рублях |
| status | string | 30 | Yes | pending | No | Статус (pending, in_progress, completed, cancelled) |
| paymentStatus | string | 30 | Yes | unpaid | No | Статус оплаты (unpaid, paid, refunded) |
| paymentMethod | string | 50 | No | - | No | Способ оплаты |
| paymentId | string | 100 | No | - | No | ID платежа в платежной системе |
| startDate | datetime | - | No | - | No | Дата начала работ |
| endDate | datetime | - | No | - | No | Дата окончания работ |
| deliveryDate | datetime | - | No | - | No | Планируемая дата доставки |
| completedDate | datetime | - | No | - | No | Фактическая дата завершения |
| artistName | string | 100 | No | - | No | Имя артиста |
| trackName | string | 100 | No | - | No | Название трека |
| socialLinks | string | 2000 | No | - | No | JSON со ссылками на соц. сети |
| requirements | string | 2000 | No | - | No | Требования клиента |
| deliverables | string | 2000 | No | - | No | JSON с результатами работы |
| progress | integer | - | Yes | 0 | No | Прогресс выполнения (0-100) |
| assignedTo | string | 36 | No | - | No | ID исполнителя |
| priority | string | 20 | Yes | normal | No | Приоритет (low, normal, high, urgent) |
| tags | string | 500 | No | - | Yes | Теги для категоризации |
| files | string | 2000 | No | - | No | JSON с файлами проекта |
| notes | string | 2000 | No | - | No | Внутренние заметки |
| clientFeedback | string | 1000 | No | - | No | Отзыв клиента |
| rating | integer | - | No | - | No | Оценка клиента (1-5) |
| createdAt | datetime | - | Yes | - | No | Дата создания |
| updatedAt | datetime | - | Yes | - | No | Дата обновления |

### Индексы:
- userId (для быстрого поиска заказов пользователя)
- status (для фильтрации по статусу)
- serviceType (для группировки по типу услуг)
- createdAt (для сортировки по дате)
- assignedTo (для поиска заказов исполнителя)

---

## 2. Collection: notifications (Уведомления)

### Атрибуты:

| Attribute ID | Type | Size | Required | Default | Array | Description |
|-------------|------|------|----------|---------|-------|-------------|
| notificationId | string | 36 | Yes | - | No | Уникальный ID уведомления |
| userId | string | 36 | Yes | - | No | ID получателя уведомления |
| type | string | 50 | Yes | - | No | Тип уведомления (order, payment, system, promo) |
| category | string | 50 | Yes | info | No | Категория (info, success, warning, error) |
| title | string | 200 | Yes | - | No | Заголовок уведомления |
| message | string | 1000 | Yes | - | No | Текст уведомления |
| actionUrl | string | 500 | No | - | No | Ссылка для действия |
| actionText | string | 100 | No | - | No | Текст кнопки действия |
| isRead | boolean | - | Yes | false | No | Прочитано ли уведомление |
| isImportant | boolean | - | Yes | false | No | Важное уведомление |
| priority | string | 20 | Yes | normal | No | Приоритет (low, normal, high) |
| relatedOrderId | string | 36 | No | - | No | Связанный заказ (если применимо) |
| relatedUserId | string | 36 | No | - | No | Связанный пользователь |
| metadata | string | 1000 | No | - | No | JSON с дополнительными данными |
| expiresAt | datetime | - | No | - | No | Дата истечения уведомления |
| sentAt | datetime | - | No | - | No | Дата отправки |
| readAt | datetime | - | No | - | No | Дата прочтения |
| createdAt | datetime | - | Yes | - | No | Дата создания |
| updatedAt | datetime | - | Yes | - | No | Дата обновления |

### Индексы:
- userId (для получения уведомлений пользователя)
- isRead (для фильтрации непрочитанных)
- type (для группировки по типу)
- priority (для сортировки по важности)
- createdAt (для сортировки по дате)
- expiresAt (для удаления истекших)

---

## 3. Дополнительные коллекции (рекомендуемые)

### Collection: users_profiles (Профили пользователей)
Расширенная информация о пользователях:
- bio, socialLinks, preferences, settings, avatar, etc.

### Collection: analytics (Аналитика)
Статистика по заказам и пользователям:
- metrics, period, data, charts, reports, etc.

### Collection: files (Файлы)
Управление файлами проектов:
- fileId, orderId, fileName, fileType, fileSize, url, etc.

### Collection: reviews (Отзывы)
Отзывы клиентов:
- reviewId, orderId, userId, rating, comment, isPublic, etc.

---

## Настройки безопасности

### Permissions для orders:
- **Create**: Authenticated users
- **Read**: Document owner, Admins
- **Update**: Document owner, Assigned staff, Admins  
- **Delete**: Admins only

### Permissions для notifications:
- **Create**: System, Admins
- **Read**: Document owner (userId)
- **Update**: Document owner (только isRead, readAt)
- **Delete**: Document owner, Admins

---

## Примеры использования

### Создание заказа:
```javascript
const order = {
  orderId: generateId(),
  userId: currentUser.$id,
  serviceType: 'artist_promotion',
  packageType: 'pro',
  title: 'Продвижение артиста John Doe',
  price: 35000,
  status: 'pending',
  paymentStatus: 'unpaid',
  artistName: 'John Doe',
  socialLinks: JSON.stringify({
    instagram: '@johndoe',
    youtube: 'johndoe_music'
  }),
  progress: 0,
  priority: 'normal'
};
```

### Создание уведомления:
```javascript
const notification = {
  notificationId: generateId(),
  userId: order.userId,
  type: 'order',
  category: 'success',
  title: 'Заказ создан',
  message: 'Ваш заказ на продвижение артиста успешно создан',
  actionUrl: `/dashboard/orders/${order.orderId}`,
  actionText: 'Посмотреть заказ',
  relatedOrderId: order.orderId,
  priority: 'normal'
};
```

---

## Система уведомлений - Полное руководство

### 🔔 Компоненты системы уведомлений

#### 1. **NotificationCenter** - Основной компонент
- Красивое выпадающее окно с уведомлениями
- Счетчик непрочитанных уведомлений
- Фильтрация по статусу (все, новые, важные)
- Действия: отметить как прочитанное/непрочитанное, удалить
- Анимации и hover эффекты

#### 2. **useNotifications** - Хук для управления
- Создание, чтение, обновление, удаление уведомлений
- Локальное хранение в localStorage
- Автоматическое удаление истекших уведомлений
- Поддержка браузерных уведомлений

#### 3. **NotificationService** - Автоматизация
- Обработка системных событий
- Автоматическое создание уведомлений
- Планировщик задач (дедлайны, напоминания)
- Интеграция с бизнес-логикой

### 📋 Типы уведомлений (25 типов)

#### **Заказы (7 типов)**
- `order_created` - Заказ создан
- `order_confirmed` - Заказ подтвержден
- `order_in_progress` - Заказ в работе
- `order_completed` - Заказ выполнен
- `order_cancelled` - Заказ отменен
- `order_requires_info` - Требуется информация
- `order_deadline_soon` - Приближается дедлайн

#### **Платежи (4 типа)**
- `payment_received` - Платеж получен
- `payment_failed` - Ошибка платежа
- `payment_refunded` - Возврат средств
- `payment_reminder` - Напоминание об оплате

#### **Система (3 типа)**
- `system_maintenance` - Техобслуживание
- `system_update` - Обновление системы
- `system_announcement` - Объявление

#### **Промо (3 типа)**
- `promo_discount` - Скидки и акции
- `promo_new_service` - Новая услуга
- `promo_referral` - Реферальная программа

#### **Сообщения (2 типа)**
- `message_new` - Новое сообщение
- `message_reply` - Ответ на сообщение

#### **Отзывы (2 типа)**
- `review_request` - Запрос на отзыв
- `review_received` - Отзыв получен

#### **Аналитика (2 типа)**
- `analytics_report` - Отчет готов
- `milestone_reached` - Цель достигнута

#### **Прочее (2 типа)**
- `welcome` - Приветствие
- `profile_incomplete` - Неполный профиль

### 🎨 UX/UI Особенности

#### **Визуальные индикаторы**
- 🔴 Красный кружок - количество непрочитанных
- 🟡 Желтая точка - важные уведомления
- 🔵 Синяя точка - новые уведомления
- ⚪ Серая точка - прочитанные

#### **Цветовая схема по категориям**
- 🟢 **Success** - зеленый (заказ выполнен, платеж получен)
- 🟡 **Warning** - желтый (требуется информация, дедлайн)
- 🔴 **Error** - красный (ошибка платежа, отмена заказа)
- 🔵 **Info** - синий (обновления, новости, сообщения)

#### **Приоритеты**
- 🚨 **Urgent** - мигающая анимация
- ⚡ **High** - пульсирующая анимация
- 📢 **Normal** - стандартное отображение
- 📝 **Low** - приглушенные цвета

### 🔧 Автоматические уведомления

#### **Ежедневные проверки (9:00)**
- Проверка дедлайнов заказов
- Уведомления о приближающихся сроках

#### **Ежедневные напоминания (10:00)**
- Напоминания об оплате
- Неоплаченные заказы старше 24 часов

#### **Еженедельные отчеты (Воскресенье 18:00)**
- Статистика по заказам
- Аналитика продвижения
- Достижения и цели

### 📱 Адаптивность и доступность

#### **Мобильная версия**
- Полноэкранное модальное окно на мобильных
- Свайп-жесты для действий
- Оптимизированные размеры кнопок

#### **Браузерные уведомления**
- Запрос разрешений при первом входе
- Push-уведомления для важных событий
- Настройки уведомлений в профиле

#### **Клавиатурная навигация**
- Tab-навигация по уведомлениям
- Enter для открытия действий
- Escape для закрытия

### 🔄 Интеграция с бизнес-процессами

#### **При создании заказа**
```javascript
// Клиенту
notificationService.handleSystemEvent({
  type: 'order',
  action: 'created',
  data: { orderTitle: 'Продвижение артиста', orderId: '123' },
  userId: 'client_id'
});

// Администратору
notificationService.handleSystemEvent({
  type: 'order',
  action: 'created',
  data: { orderTitle: 'Продвижение артиста', customerName: 'John Doe' },
  userId: 'admin_id'
});
```

#### **При изменении статуса платежа**
```javascript
notificationService.handleSystemEvent({
  type: 'payment',
  action: 'received',
  data: { amount: '35,000', orderId: '123' },
  userId: 'client_id'
});
```

### 📊 Метрики и аналитика

#### **Отслеживаемые события**
- Время прочтения уведомлений
- Клики по действиям
- Удаления без прочтения
- Конверсия уведомлений в действия

#### **A/B тестирование**
- Разные тексты уведомлений
- Время отправки
- Частота напоминаний
- Визуальное оформление
