/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Отключаем ESLint во время билда для быстрого деплоя
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Отключаем проверку TypeScript во время билда для быстрого деплоя
    ignoreBuildErrors: true,
  },
  experimental: {
    optimizeCss: true,
  },
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com'],
  },
}

module.exports = nextConfig
